import {Button} from "@ui/components/ui/button";
import React from "react";

export function Pricing() {
    return (<>        <a id={"pricing"}></a>
        <section className="w-full py-12 md:py-24 lg:py-32 mx-auto max-w-[1280px]">
            <div className="container space-y-12 px-4 md:px-6">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <div className="space-y-2">
                        <div className="inline-block rounded-lg bg-muted px-3 py-1 text-sm">Pricing
                        </div>
                        <h2 className="text-3xl font-bold tracking-tighter sm:text-5xl">
                            Affordable Pricing for Sustainable Investing
                        </h2>
                        <p className="max-w-[900px] text-muted-foreground md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                            ekoIntelligence offers flexible pricing plans to fit your needs, whether
                            you're
                            an individual investor
                            or a large institution.
                        </p>
                    </div>
                </div>
                <div
                    className="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
                    <div className="grid gap-4 bg-muted rounded-lg p-6">
                        <h3 className="text-xl font-bold">Individual</h3>
                        <p className="text-4xl font-bold">£7/mo</p>
                        <p className="text-muted-foreground">
                            Ideal for individual investors looking to assess the sustainability and
                            fairness
                            of their portfolio.
                        </p>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Access to sustainability and fairness analytics
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Portfolio analysis and reporting
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Customizable dashboards
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Email support
                            </li>
                        </ul>
                        <Button>Get Started</Button>
                    </div>
                    <div className="grid gap-4 bg-muted rounded-lg p-6">
                        <h3 className="text-xl font-bold">Professional</h3>
                        <p className="text-4xl font-bold">£39/mo</p>
                        <p className="text-muted-foreground">Ideal for financial advisors and investment
                            professionals.</p>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Everything in the Individual plan
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Advanced portfolio analysis and reporting
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Dedicated account manager
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Priority support
                            </li>
                        </ul>
                        <Button>Get Started</Button>
                    </div>
                    <div className="grid gap-4 bg-muted rounded-lg p-6">
                        <h3 className="text-xl font-bold">Enterprise</h3>
                        <p className="text-4xl font-bold">Custom Pricing</p>
                        <p className="text-muted-foreground">Ideal for large institutions and asset
                            managers.</p>
                        <ul className="space-y-2 text-sm">
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Everything in the Professional plan
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Customized data integrations and reporting
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Dedicated customer success team
                            </li>
                            <li>
                                <CheckIcon className="mr-2 inline-block h-4 w-4"/>
                                Tailored service level agreements
                            </li>
                        </ul>
                        <Button>Contact Sales</Button>
                    </div>
                </div>
            </div>
        </section>
    </>)
        ;
}

function CheckIcon(props: any) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <path d="M20 6 9 17l-5-5"/>
        </svg>
    )
}
