import React from "react";
import {QuoteCard} from "@ui/components/front-page/quote-card";

export function BackgroundReading() {
    return (<>        <a id={"background-reading"}></a>
        <section className="w-full py-12 md:py-24 lg:py-32 mx-auto  bg-brand">

            <div className="max-w-[1280px] mx-auto space-y-12 px-4 md:px-6 ">
                <div className="flex flex-col items-center justify-center space-y-4 text-center">
                    <div className="space-y-2">
                        <div
                            className="inline-block rounded-lg bg-muted px-3 py-1 text-sm text-white opacity-80">Greenwashing
                        </div>
                        <h2 className="text-3xl text-white opacity-90 font-bold tracking-tighter sm:text-5xl">
                            Greenwashing
                        </h2>
                        <p className="max-w-[900px] text-white opacity-80 md:text-xl/relaxed lg:text-base/relaxed xl:text-xl/relaxed">
                            Here are some resources to help you understand the importance of sustainability and the
                            dangers of greenwashing.
                        </p>
                    </div>
                </div>
                <div
                    className="mx-auto grid items-start gap-8 sm:max-w-4xl sm:grid-cols-2 md:gap-12 lg:max-w-5xl lg:grid-cols-3">
                    <QuoteCard title="Greenwashing"
                               author="United Nations"
                               text="Greenwashing presents a significant obstacle to tackling climate change. By misleading the public to believe that a company or other entity is doing more to protect the environment than it is, greenwashing promotes false solutions to the climate crisis that distract from and delay concrete and credible action."
                               context="United Nations Environment Programme"
                               image="/images/turf.png"
                               link="https://www.un.org/en/climatechange/science/climate-issues/greenwashing#:~:text=By%20misleading%20the%20public%20to,delay%20concrete%20and%20credible%20action."
                    />
                    <QuoteCard title="How the EU regulates green claims"
                               author="European Union"
                               text="The EU aims to put an end to greenwashing, when companies claim to be greener than they are, and provide more information to consumers on the durability of products they buy."
                               context="United Parlimentary Assembly"
                               image="/images/shopper.png"
                               link="https://www.europarl.europa.eu/topics/en/article/20240111STO16722/stopping-greenwashing-how-the-eu-regulates-green-claims#:~:text=What%20is%20greenwashing%3F&text=To%20achieve%20that%2C%20the%20EU,the%20producer%20is%20offsetting%20emissions"
                    />
                    <QuoteCard title="Greenwashing"
                               author="United Nations"
                               text="Greenwashing presents a significant obstacle to tackling climate change. By misleading the public to believe that a company or other entity is doing more to protect the environment than it is, greenwashing promotes false solutions to the climate crisis that distract from and delay concrete and credible action."
                               context="United Nations Environment Programme"
                               image="/images/turf.png"
                               link="https://www.un.org/en/climatechange/science/climate-issues/greenwashing#:~:text=By%20misleading%20the%20public%20to,delay%20concrete%20and%20credible%20action."
                    />
                </div>
            </div>
        </section>
    </>)
        ;
}

function CheckIcon(props: any) {
    return (
        <svg
            {...props}
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
        >
            <path d="M20 6 9 17l-5-5"/>
        </svg>
    )
}
