import {
    Building,
    CloudUpload,
    CookingPot,
    Droplets,
    Equal,
    FlaskConical,
    GlassWater,
    GraduationCap,
    Handshake,
    HeartPulse,
    Home,
    Scale,
    Skull,
    Speech,
    SprayCan,
    ThermometerSunIcon,
    Tractor,
    Trees,
    UtilityPole,
    Waves,
    Waypoints
} from "lucide-react";


export type Segment = {
    id: string
    title: string
    description: string
    level: string
    icon: React.ReactNode
}

export type SegmentMapType = Map<String, Segment>;
export const DOUGHNUT_SEGMENT_MAP: SegmentMapType = new Map([
    ["nitrogen_phosphorus", {
        id: "nitrogen_phosphorus",
        title: "Nitrogen & Phosphorous",
        description: "This dimension represents the nitrogen and phosphorous cycles, essential for ecosystem health and food production. Balancing these cycles is crucial to avoid environmental degradation.",
        level: "ecological",
        icon: <FlaskConical/>
    }],
    ["biodiversity", {
        id: "biodiversity",
        title: "Biodiversity",
        description: "Biodiversity encompasses the variety of life forms on Earth, crucial for ecosystem resilience and human well-being. Preserving biodiversity supports sustainable development.",
        level: "ecological",
        icon: <Trees/>
    }],
    ["chemical_pollution", {
        id: "chemical_pollution",
        title: "Chemical Pollution",
        description: "Chemical pollution refers to the contamination of air, water, or soil by harmful synthetic chemicals or heavy metals, posing risks to human health and ecosystems.",
        level: "ecological",
        icon: <Skull/>
    }],
    ["air_pollution", {
        id: "air_pollution",
        title: "Air Pollution",
        description: "Air pollution involves the presence of harmful substances in the atmosphere, such as particulate matter and gases, which adversely affect human health and climate.",
        level: "ecological",
        icon: <CloudUpload/>
    }],
    ["freshwater_withdrawals", {
        id: "freshwater_withdrawals",
        title: "Freshwater Withdrawals",
        description: "Freshwater withdrawals measure the rate at which humanity draws from freshwater sources for various purposes, impacting water availability and ecosystem health.",
        level: "ecological",
        icon: <Droplets/>
    }],
    ["climate_change", {
        id: "climate_change",
        title: "Climate Change",
        description: "Climate change refers to long-term changes in temperature, precipitation, and other atmospheric conditions, primarily caused by human activities, affecting global ecosystems and societies.",
        level: "ecological",
        icon: <ThermometerSunIcon/>
    }],
    ["ocean_acidification", {
        id: "ocean_acidification",
        title: "Ocean Acidification",
        description: "Ocean acidification results from the absorption of carbon dioxide by the oceans, causing a decrease in pH levels. This threatens marine ecosystems, including coral reefs.",
        level: "ecological",
        icon: <Waves/>
    }],
    ["ozone_depletion", {
        id: "ozone_depletion",
        title: "Ozone Depletion",
        description: "Ozone depletion refers to the thinning of the ozone layer in the Earth's stratosphere, primarily caused by human-made chemicals. It increases UV radiation reaching the Earth's surface.",
        level: "ecological",
        icon: <SprayCan/>
    }],
    ["land_conversion", {
        id: "land_conversion",
        title: "Land Conversion",
        description: "Land conversion involves the transformation of natural habitats such as forests or wetlands into agricultural, urban, or industrial land, impacting biodiversity and ecosystems.",
        level: "ecological",
        icon: <Tractor/>
    }],
    ["water", {
        id: "water",
        title: "Water",
        description: "Water availability and access are critical for human health, agriculture, and ecosystem functioning. Managing water resources sustainably is essential for global well-being.",
        level: "social",
        icon: <GlassWater/>
    }],
    ["food", {
        id: "food",
        title: "Food",
        description: "Access to sufficient, nutritious food is a fundamental human right. Ensuring food security while promoting sustainable agricultural practices is crucial.",
        level: "social",
        icon: <CookingPot/>
    }],
    ["health", {
        id: "health",
        title: "Health",
        description: "Health encompasses physical, mental, and social well-being. Promoting good health is essential for sustainable development and quality of life.",
        level: "social",
        icon: <HeartPulse/>
    }],
    ["education", {
        id: "education",
        title: "Education",
        description: "Education is essential for personal development, empowerment, and economic growth. Access to quality education promotes equitable opportunities and sustainable societies.",
        level: "social",
        icon: <GraduationCap/>
    }],
    ["work", {
        id: "work",
        title: "Work",
        description: "Work represents employment opportunities and decent work conditions. Promoting inclusive and sustainable economic growth requires productive and fulfilling employment.",
        level: "social",
        icon: <Building/>

    }],
    ["justice", {
        id: "justice",
        title: "Justice",
        description: "Justice includes access to legal systems, human rights, and social equity. Upholding justice ensures fair treatment and protection for all individuals.",
        level: "social",
        icon: <Scale/>
    }],
    ["voice", {
        id: "voice",
        title: "Voice",
        description: "Voice refers to participatory governance, freedom of expression, and civic engagement. Promoting inclusive decision-making and accountability enhances democratic processes.",
        level: "social",
        icon: <Speech/>
    }],
    ["social_equity", {
        id: "social_equity",
        title: "Social Eq",
        description: "Social equity encompasses fairness, equal opportunities, and human rights. Achieving social equity requires addressing disparities and promoting inclusivity in all aspects of society.",
        level: "social",
        icon: <Handshake/>
    }],
    ["gender_equality", {
        id: "gender_equality",
        title: "Gender Eq",
        description: "Gender equality ensures equal rights, opportunities, and responsibilities for all genders. Empowering women and girls contributes to sustainable development and social justice.",
        level: "social",
        icon: <Equal/>
    }],
    ["housing", {
        id: "housing",
        title: "Housing",
        description: "Housing represents adequate shelter and living conditions. Access to affordable housing is essential for health, well-being, and social stability.",
        level: "social",
        icon: <Home/>
    }],
    ["networks", {
        id: "networks",
        title: "Networks",
        description: "Networks refer to social connections, community support, and access to information. Strengthening networks enhances resilience, collaboration, and resource-sharing within communities.",
        level: "social",
        icon: <Waypoints/>
    }],
    ["energy", {
        id: "energy",
        title: "Energy",
        description: "Energy access and sustainability are critical for economic development and environmental stewardship. Transitioning to renewable energy sources promotes climate resilience and reduces environmental impact.",
        level: "social",
        icon: <UtilityPole/>
    }]
]);


export type SegmentData = {
    name: string;
    value: number;
    count?: number;
    detail?: string;
    icon?: React.ReactNode;
}
