"use client";
import React from 'react'
import { ErrorBoundary } from 'react-error-boundary'

interface ErrorFallbackProps {
  error: Error;
  resetErrorBoundary: () => void;
}

const ErrorFallback: React.FC<ErrorFallbackProps> = ({ error, resetErrorBoundary }) => {
  return (
    <div role="alert">
      <h2>Oops, there is an error!</h2>
      <pre>{error.message}</pre>
      <button type="button" onClick={resetErrorBoundary}>
        Try again?
      </button>
    </div>
  );
};

interface ErrorBoundaryProps {
  children: React.ReactNode;
}

const CustomErrorBoundary: React.FC<ErrorBoundaryProps> = ({ children }) => {
  return (
    <ErrorBoundary
      FallbackComponent={ErrorFallback}
      onReset={() => {
        // You can reset any state here if needed
      }}
    >
      {children}
    </ErrorBoundary>
  );
};

export default CustomErrorBoundary;