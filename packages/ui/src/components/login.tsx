/**
 * This code was generated by v0 by Vercel.
 * @see https://v0.dev/t/Rslbfk8kIRS
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */

/** Add fonts into your Next.js project:

 import { Inter } from 'next/font/google'

 inter({
 subsets: ['latin'],
 display: 'swap',
 })

 To read more about using these font, please visit the Next.js documentation:
 - App Directory: https://nextjs.org/docs/app/building-your-application/optimizing/fonts
 - Pages Directory: https://nextjs.org/docs/pages/building-your-application/optimizing/fonts
 **/
import Link from "next/link"
import {Label} from "@ui/components/ui/label"
import {Input} from "@ui/components/ui/input"
import {Checkbox} from "@ui/components/ui/checkbox"
import {Button} from "@ui/components/ui/button"

export function Login() {
    return (
        <div className="flex min-h-[100dvh] bg-background">
            <div className="flex flex-1 items-center justify-center bg-muted p-6 sm:p-10 lg:p-12 xl:p-16">
                <div className="mx-auto max-w-md space-y-8">
                    <div>
                        <h2 className="mt-6 text-3xl font-bold tracking-tight text-foreground">Sign in to your
                            account</h2>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Or{" "}
                            <Link href="#" className="font-medium text-primary hover:text-primary/90" prefetch={false}>
                                register for a new account
                            </Link>
                        </p>
                    </div>
                    <form className="space-y-6" action="#" method="POST">
                        <div>
                            <Label htmlFor="username" className="block text-sm font-medium text-foreground">
                                Username
                            </Label>
                            <div className="mt-1">
                                <Input
                                    id="username"
                                    name="username"
                                    type="text"
                                    autoComplete="username"
                                    required
                                    className="block w-full appearance-none rounded-md border border-input bg-background px-3 py-2 placeholder-muted-foreground shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div>
                            <Label htmlFor="password" className="block text-sm font-medium text-foreground">
                                Password
                            </Label>
                            <div className="mt-1">
                                <Input
                                    id="password"
                                    name="password"
                                    type="password"
                                    autoComplete="current-password"
                                    required
                                    className="block w-full appearance-none rounded-md border border-input bg-background px-3 py-2 placeholder-muted-foreground shadow-sm focus:border-primary focus:outline-none focus:ring-1 focus:ring-primary sm:text-sm"
                                />
                            </div>
                        </div>
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <Checkbox id="remember-me" name="remember-me" className="h-4 w-4 rounded"/>
                                <Label htmlFor="remember-me" className="ml-2 block text-sm text-foreground">
                                    Remember me
                                </Label>
                            </div>
                            <div className="text-sm">
                                <Link href="#" className="font-medium text-primary hover:text-primary/90"
                                      prefetch={false}>
                                    Forgot your password?
                                </Link>
                            </div>
                        </div>
                        <div>
                            <Button
                                type="submit"
                                className="flex w-full justify-center rounded-md bg-primary px-3 py-2 text-sm font-semibold text-primary-foreground shadow-sm hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
                            >
                                Sign in
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
            <div className="flex-1 bg-background p-6 sm:p-10 lg:p-12 xl:p-16 flex flex-col items-center justify-center">
                <div className="mx-auto max-w-md space-y-8">
                    <div>
                        <h2 className="mt-6 text-3xl font-bold tracking-tight text-foreground">Welcome to Acme Inc.</h2>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Acme Inc. is a leading provider of innovative software solutions. Our platform helps
                            businesses of all
                            sizes streamline their operations and drive growth.
                        </p>
                    </div>
                    <div>
                        <h3 className="mt-6 text-xl font-bold tracking-tight text-foreground">Get Started</h3>
                        <p className="mt-2 text-sm text-muted-foreground">
                            Sign in to your account or register a new one to start using our platform and unlock a world
                            of
                            possibilities.
                        </p>
                        <div className="mt-4 flex gap-2">
                            <Button
                                variant="secondary"
                                className="flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold shadow-sm hover:bg-accent hover:text-accent-foreground focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
                            >
                                Sign in with Google
                            </Button>
                            <Link
                                href="#"
                                className="inline-flex items-center rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground shadow-sm transition-colors hover:bg-primary/90 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-primary"
                                prefetch={false}
                            >
                                Register
                            </Link>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    )
}
