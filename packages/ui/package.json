{"name": "@repo/ui", "version": "0.0.0", "private": true, "exports": {"./components": "./src/components"}, "scripts": {"lint": "eslint . --max-warnings 0", "generate:component": "turbo gen react-component", "build": "tsc", "dev": " "}, "devDependencies": {"@repo/eslint-config": "workspace:*", "@repo/typescript-config": "workspace:*", "@repo/utils": "workspace:*", "@turbo/gen": "^2.2.3", "@types/eslint": "^9.6.1", "@types/node": "^20.17.6", "@types/react": "19.0.1", "@types/react-dom": "19.0.2", "eslint": "^9.14.0", "typescript": "^5.6.3"}, "dependencies": {"@aws-sdk/client-s3": "^3.687.0", "@aws-sdk/lib-storage": "^3.687.0", "@aws-sdk/s3-request-presigner": "^3.687.0", "@formkit/auto-animate": "^0.8.2", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.1", "@radix-ui/react-context-menu": "^2.2.15", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-scroll-area": "^1.2.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.3", "@radix-ui/react-slot": "^1.1.0", "@radix-ui/react-tabs": "^1.1.1", "@radix-ui/react-toast": "^1.2.2", "@radix-ui/react-tooltip": "^1.1.3", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tailwindcss/typography": "^0.5.15", "@types/d3": "^7.4.3", "@types/react-avatar-editor": "^13.0.3", "@vercel/kv": "^3.0.0", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "cmdk": "^1.0.4", "d3": "^7.9.0", "d3plus-react": "^1.3.3", "d3plus-text": "^1.2.5", "framer-motion": "^12.0.0-alpha.2", "geist": "^1.3.1", "lucide-react": "^0.462.0", "next": "15.1.0", "openai": "^4.71.1", "postcss": "^8.4.49", "prop-types": "^15.8.1", "react": "19.0.0", "react-avatar-editor": "^13.0.2", "react-dom": "19.0.0", "react-dropzone": "^14.3.5", "react-error-boundary": "^5.0.0", "react-force-graph-3d": "^1.24.5", "react-hook-form": "^7.53.1", "react-markdown": "^9.0.1", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.15.1", "remark-gfm": "^4.0.0", "sonner": "^1.7.0", "tailwind-merge": "^2.5.4", "tailwindcss": "^3.4.15", "tailwindcss-animate": "^1.0.7", "usehooks-ts": "^3.1.0", "uuid": "^11.0.2"}, "overrides": {"react": "npm:react@19.0.0-rc.1", "react-dom": "npm:react-dom@19.0.0-rc.1", "@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "pnpm": {"overrides": {"@types/react": "19.0.1", "@types/react-dom": "19.0.2"}}}