version: '3.8'

x-env: &env
  POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
  POSTGRES_USER: ${POSTGRES_USER}
  POSTGRES_DB: ${POSTGRES_DB}
  POSTGRES_HOST: db
  VAR_DIR: ${VAR_DIR}
  OPENAI_API_KEY: ${OPENAI_API_KEY}
  GROQ_API_KEY: ${GROQ_API_KEY}
  PDF_CO_API_KEY: ${PDF_CO_API_KEY}
  GOOGLE_CSE_ID: ${GOOGLE_CSE_ID}
  GOOGLE_API_KEY: ${GOOGLE_API_KEY}
  AWS_ACCESS_KEY_ID: ${AWS_ACCESS_KEY_ID}
  AWS_SECRET_ACCESS_KEY: ${AWS_SECRET_ACCESS_KEY}
  AWS_REGION: ${AWS_REGION}
  OPENAI_ASSISTANT_ID: ${OPENAI_ASSISTANT_ID}
  VECTOR_STORE: ${VECTOR_STORE}
  ANTHROPIC_API_KEY: ${ANTHROPIC_API_KEY}
  OLLAMA_HOST: ${OLLAMA_HOST}
  LOG_FORMAT: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"


services:

  db:
    image: ankane/pgvector:latest
    restart: always
    shm_size: 128mb
    environment:
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
      POSTGRES_USER: ${POSTGRES_USER}
      POSTGRES_DB: ${POSTGRES_DB}
    ports:
      - "5432:5432"
    volumes:
      - ${VAR_DIR}/postgres:/var/lib/postgresql/data
      - ${VAR_DIR}/pgdumps:/tmp/pgdumps
      - ./db/init-scripts/:/docker-entrypoint-initdb.d/
    extra_hosts:
      - "host.docker.internal:host-gateway"
