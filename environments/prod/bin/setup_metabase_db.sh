#!/bin/bash
# <PERSON><PERSON>t to set up the Metabase database and user
set -e

# Configuration
METABASE_DB_USER=${METABASE_DB_USER:-metabase}
METABASE_DB_PASS=${METABASE_DB_PASS:-$POSTGRES_PASSWORD}
METABASE_DB_NAME="metabase"

echo "Setting up Metabase database..."

# Connect to PostgreSQL and run SQL commands
psql -v ON_ERROR_STOP=1 --username "$POSTGRES_USER" --dbname "$POSTGRES_DB" <<-EOSQL
    -- Check if the role exists
    DO
    \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM pg_roles WHERE rolname = '$METABASE_DB_USER') THEN
            CREATE ROLE $METABASE_DB_USER WITH LOGIN PASSWORD '$METABASE_DB_PASS';
        END IF;
    END
    \$\$;

    -- Check if database exists
    SELECT 'CREATE DATABASE $METABASE_DB_NAME'
    WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = '$METABASE_DB_NAME')\gexec

    -- Grant privileges
    GRANT ALL PRIVILEGES ON DATABASE $METABASE_DB_NAME TO $METABASE_DB_USER;
    ALTER DATABASE $METABASE_DB_NAME OWNER TO $METABASE_DB_USER;

    -- Connect to the metabase database to set schema privileges
    \c $METABASE_DB_NAME

    -- Grant privileges on the public schema
    GRANT ALL ON SCHEMA public TO $METABASE_DB_USER;
    ALTER SCHEMA public OWNER TO $METABASE_DB_USER;

    -- Grant privileges on the dash schema
    DO
    \$\$
    BEGIN
        IF NOT EXISTS (SELECT 1 FROM information_schema.schemata WHERE schema_name = 'dash') THEN
            CREATE SCHEMA IF NOT EXISTS dash;
        END IF;
    END
    \$\$;
    
    GRANT USAGE ON SCHEMA dash TO $METABASE_DB_USER;
    GRANT SELECT ON ALL TABLES IN SCHEMA dash TO $METABASE_DB_USER;
    ALTER DEFAULT PRIVILEGES IN SCHEMA dash GRANT SELECT ON TABLES TO $METABASE_DB_USER;

    -- Grant privileges on schema needed for pipeline analysis
    DO
    \$\$
    BEGIN
        -- Grant access to other schemas as needed for pipeline analysis
        GRANT USAGE ON SCHEMA public TO $METABASE_DB_USER;
        GRANT SELECT ON ALL TABLES IN SCHEMA public TO $METABASE_DB_USER;
        ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO $METABASE_DB_USER;
    END
    \$\$;
EOSQL

echo "Metabase database setup complete!"