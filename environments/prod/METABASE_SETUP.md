# Metabase Setup for Effect Pipeline Tracking

This document describes how to set up and configure Metabase for visualizing the effect pipeline tracking data.

## Installation

Metabase has been added to the docker-compose.yml file and can be started with the rest of the system:

```bash
# From the environments/prod directory
./bin/init_metabase.sh
```

This script will:
1. Create the necessary database and user
2. Grant appropriate permissions
3. Start the Metabase container
4. Provide initial setup instructions

## Initial Setup

After starting Metabase for the first time:

1. Navigate to http://localhost:3000 (or the configured METABASE_PORT)
2. Use the initial admin credentials:
   - Email: <EMAIL>
   - Password: admin123
3. Change these credentials immediately after login
4. Follow the setup wizard to configure Metabase

## Connecting to the Database

You'll need to set up two database connections:

### 1. Main Application Database

This connection provides access to all tables in the database:

- **Name**: Eko Database
- **Database Type**: PostgreSQL
- **Host**: db
- **Port**: 5432 (or your configured POSTGRES_PORT)
- **Database Name**: your-database-name (from POSTGRES_DB)
- **Username**: postgres (from POSTGRES_USER)
- **Password**: your-postgres-password (from POSTGRES_PASSWORD)

### 2. Effect Pipeline Tracking Database

This connection specifically targets the dash schema for pipeline tracking:

- **Name**: Effect Pipeline Tracking
- **Database Type**: PostgreSQL
- **Host**: db
- **Port**: 5432 (or your configured POSTGRES_PORT)
- **Database Name**: your-database-name (from POSTGRES_DB)
- **Schema**: dash
- **Username**: postgres (from POSTGRES_USER)
- **Password**: your-postgres-password (from POSTGRES_PASSWORD)

## Importing Dashboard Queries

Pre-designed SQL queries are available for creating dashboards:

1. Go to the Metabase admin interface
2. Select "SQL Editor" 
3. Copy and paste queries from:
   ```
   /backoffice/src/eko/analysis_v2/effects/metabase_dash_schema_queries.sql
   ```
4. Save each query with appropriate names

## Creating Dashboards

To create a comprehensive pipeline tracking dashboard:

1. Go to Dashboard → "Create Dashboard"
2. Name it "Effect Pipeline Tracking Dashboard"
3. Add the following visualizations (based on the imported queries):
   - Pipeline Overview by Stage (Bar Chart)
   - Pipeline Errors by Stage (Table)
   - Effect Clustering Performance (Table)
   - Effects Created (Bar Chart)
   - Effect Flags Quality Metrics (Table)
   - Processing Time by Stage (Bar Chart)
   - Vector Quality Metrics (Table)
   - Effect Flag Merging Statistics (Table)
   - Latest Pipeline Runs Summary (Table)
   - Pipeline Stage Flow (Funnel Chart)

## Maintaining Metabase

- **Data Location**: Metabase's data is stored in `${VAR_DIR}/metabase`
- **Backup**: The data directory is included in the regular backup system
- **Updating**: Update the image tag in docker-compose.yml to upgrade Metabase

## Troubleshooting

- **Logs**: Check logs with `docker-compose logs metabase`
- **Database Issues**: Ensure the metabase user has proper permissions
- **Performance**: Adjust METABASE_JAVA_OPTS in .env for more memory if needed

## Reference

For more information on creating custom queries and visualizations, refer to:
- [Metabase Documentation](https://www.metabase.com/docs/latest/)
- SQL reference for pipeline tracking tables in `/backoffice/src/eko/analysis_v2/effects/metabase_dash_schema_queries.sql`