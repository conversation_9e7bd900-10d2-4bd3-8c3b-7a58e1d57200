

SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;


CREATE SCHEMA IF NOT EXISTS "api";


ALTER SCHEMA "api" OWNER TO "postgres";


CREATE EXTENSION IF NOT EXISTS "pgsodium";






COMMENT ON SCHEMA "public" IS 'standard public schema';



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";






CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";






CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";






CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";






CREATE TYPE "public"."enum__company_reports_v_blocks_cr_analysis_refs_type" AS ENUM (
    'web_page',
    'html',
    'pdf'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_cr_analysis_refs_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_cr_analysis_visibility" AS ENUM (
    'public',
    'auth',
    'email',
    'paid'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_cr_analysis_visibility" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_cr_graph_graph" AS ENUM (
    'bar'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_cr_graph_graph" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_highlighted_text_color_theme" AS ENUM (
    'brand',
    'blue',
    'purple',
    'amber',
    'teal',
    'rose',
    'grey',
    'clear'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_highlighted_text_color_theme" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_highlighted_text_max_width" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_highlighted_text_max_width" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_highlighted_text_style" AS ENUM (
    'gradient',
    'bordered',
    'floating',
    'glow',
    'accent-border',
    'quote',
    'glass',
    'spotlight',
    'angled'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_highlighted_text_style" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_blocks_highlighted_text_text_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum__company_reports_v_blocks_highlighted_text_text_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum__company_reports_v_version_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum__company_reports_v_version_status" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_archive_populate_by" AS ENUM (
    'collection',
    'selection'
);


ALTER TYPE "public"."enum__pages_v_blocks_archive_populate_by" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_archive_relation_to" AS ENUM (
    'posts'
);


ALTER TYPE "public"."enum__pages_v_blocks_archive_relation_to" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_content_columns_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum__pages_v_blocks_content_columns_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_content_columns_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum__pages_v_blocks_content_columns_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_content_columns_size" AS ENUM (
    'oneThird',
    'half',
    'twoThirds',
    'full'
);


ALTER TYPE "public"."enum__pages_v_blocks_content_columns_size" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_cta_links_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum__pages_v_blocks_cta_links_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_cta_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum__pages_v_blocks_cta_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_faq_column_layout" AS ENUM (
    'single',
    'double'
);


ALTER TYPE "public"."enum__pages_v_blocks_faq_column_layout" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_highlighted_text_color_theme" AS ENUM (
    'brand',
    'blue',
    'purple',
    'amber',
    'teal',
    'rose',
    'grey',
    'clear'
);


ALTER TYPE "public"."enum__pages_v_blocks_highlighted_text_color_theme" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_highlighted_text_max_width" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum__pages_v_blocks_highlighted_text_max_width" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_highlighted_text_style" AS ENUM (
    'gradient',
    'bordered',
    'floating',
    'glow',
    'accent-border',
    'quote',
    'glass',
    'spotlight',
    'angled'
);


ALTER TYPE "public"."enum__pages_v_blocks_highlighted_text_style" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_highlighted_text_text_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum__pages_v_blocks_highlighted_text_text_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_heading_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_heading_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_heading_position" AS ENUM (
    'above',
    'aboveImage',
    'center'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_heading_position" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_image_border_radius" AS ENUM (
    'none',
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_image_border_radius" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_image_size" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_image_size" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_layout_style" AS ENUM (
    'leftSideImage',
    'rightSideImage',
    'overlay',
    'textAbove',
    'textBelow'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_layout_style" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_blocks_image_text_se_color" AS ENUM (
    'default',
    'light',
    'dark',
    'brand'
);


ALTER TYPE "public"."enum__pages_v_blocks_image_text_se_color" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_version_hero_links_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum__pages_v_version_hero_links_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_version_hero_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum__pages_v_version_hero_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_version_hero_type" AS ENUM (
    'none',
    'superHighImpact',
    'frontPageHero',
    'highImpact',
    'mediumImpact',
    'lowImpact'
);


ALTER TYPE "public"."enum__pages_v_version_hero_type" OWNER TO "postgres";


CREATE TYPE "public"."enum__pages_v_version_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum__pages_v_version_status" OWNER TO "postgres";


CREATE TYPE "public"."enum__posts_v_version_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum__posts_v_version_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_cr_analysis_refs_type" AS ENUM (
    'web_page',
    'html',
    'pdf'
);


ALTER TYPE "public"."enum_company_reports_blocks_cr_analysis_refs_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_cr_analysis_visibility" AS ENUM (
    'public',
    'auth',
    'email',
    'paid'
);


ALTER TYPE "public"."enum_company_reports_blocks_cr_analysis_visibility" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_cr_graph_graph" AS ENUM (
    'bar'
);


ALTER TYPE "public"."enum_company_reports_blocks_cr_graph_graph" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_highlighted_text_color_theme" AS ENUM (
    'brand',
    'blue',
    'purple',
    'amber',
    'teal',
    'rose',
    'grey',
    'clear'
);


ALTER TYPE "public"."enum_company_reports_blocks_highlighted_text_color_theme" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_highlighted_text_max_width" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum_company_reports_blocks_highlighted_text_max_width" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_highlighted_text_style" AS ENUM (
    'gradient',
    'bordered',
    'floating',
    'glow',
    'accent-border',
    'quote',
    'glass',
    'spotlight',
    'angled'
);


ALTER TYPE "public"."enum_company_reports_blocks_highlighted_text_style" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_blocks_highlighted_text_text_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum_company_reports_blocks_highlighted_text_text_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum_company_reports_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum_company_reports_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_company_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_company_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_quick_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_quick_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_resource_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_resource_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_social_links_platform" AS ENUM (
    'facebook',
    'twitter',
    'linkedin',
    'instagram'
);


ALTER TYPE "public"."enum_footer_social_links_platform" OWNER TO "postgres";


CREATE TYPE "public"."enum_footer_solution_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_footer_solution_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_forms_confirmation_type" AS ENUM (
    'message',
    'redirect'
);


ALTER TYPE "public"."enum_forms_confirmation_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_header_nav_items_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_header_nav_items_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_archive_populate_by" AS ENUM (
    'collection',
    'selection'
);


ALTER TYPE "public"."enum_pages_blocks_archive_populate_by" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_archive_relation_to" AS ENUM (
    'posts'
);


ALTER TYPE "public"."enum_pages_blocks_archive_relation_to" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_content_columns_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum_pages_blocks_content_columns_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_content_columns_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_pages_blocks_content_columns_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_content_columns_size" AS ENUM (
    'oneThird',
    'half',
    'twoThirds',
    'full'
);


ALTER TYPE "public"."enum_pages_blocks_content_columns_size" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_cta_links_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum_pages_blocks_cta_links_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_cta_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_pages_blocks_cta_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_faq_column_layout" AS ENUM (
    'single',
    'double'
);


ALTER TYPE "public"."enum_pages_blocks_faq_column_layout" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_highlighted_text_color_theme" AS ENUM (
    'brand',
    'blue',
    'purple',
    'amber',
    'teal',
    'rose',
    'grey',
    'clear'
);


ALTER TYPE "public"."enum_pages_blocks_highlighted_text_color_theme" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_highlighted_text_max_width" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum_pages_blocks_highlighted_text_max_width" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_highlighted_text_style" AS ENUM (
    'gradient',
    'bordered',
    'floating',
    'glow',
    'accent-border',
    'quote',
    'glass',
    'spotlight',
    'angled'
);


ALTER TYPE "public"."enum_pages_blocks_highlighted_text_style" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_highlighted_text_text_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum_pages_blocks_highlighted_text_text_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_heading_alignment" AS ENUM (
    'left',
    'center',
    'right'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_heading_alignment" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_heading_position" AS ENUM (
    'above',
    'aboveImage',
    'center'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_heading_position" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_image_border_radius" AS ENUM (
    'none',
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_image_border_radius" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_image_size" AS ENUM (
    'small',
    'medium',
    'large',
    'full'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_image_size" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_layout_style" AS ENUM (
    'leftSideImage',
    'rightSideImage',
    'overlay',
    'textAbove',
    'textBelow'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_layout_style" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_blocks_image_text_se_color" AS ENUM (
    'default',
    'light',
    'dark',
    'brand'
);


ALTER TYPE "public"."enum_pages_blocks_image_text_se_color" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_hero_links_link_appearance" AS ENUM (
    'default',
    'outline'
);


ALTER TYPE "public"."enum_pages_hero_links_link_appearance" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_hero_links_link_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_pages_hero_links_link_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_hero_type" AS ENUM (
    'none',
    'superHighImpact',
    'frontPageHero',
    'highImpact',
    'mediumImpact',
    'lowImpact'
);


ALTER TYPE "public"."enum_pages_hero_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_pages_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum_pages_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_payload_jobs_log_state" AS ENUM (
    'failed',
    'succeeded'
);


ALTER TYPE "public"."enum_payload_jobs_log_state" OWNER TO "postgres";


CREATE TYPE "public"."enum_payload_jobs_log_task_slug" AS ENUM (
    'inline',
    'schedulePublish'
);


ALTER TYPE "public"."enum_payload_jobs_log_task_slug" OWNER TO "postgres";


CREATE TYPE "public"."enum_payload_jobs_task_slug" AS ENUM (
    'inline',
    'schedulePublish'
);


ALTER TYPE "public"."enum_payload_jobs_task_slug" OWNER TO "postgres";


CREATE TYPE "public"."enum_posts_status" AS ENUM (
    'draft',
    'published'
);


ALTER TYPE "public"."enum_posts_status" OWNER TO "postgres";


CREATE TYPE "public"."enum_redirects_to_type" AS ENUM (
    'reference',
    'custom'
);


ALTER TYPE "public"."enum_redirects_to_type" OWNER TO "postgres";


CREATE TYPE "public"."enum_users_social_links_platform" AS ENUM (
    'twitter',
    'linkedin',
    'github',
    'instagram',
    'facebook',
    'youtube'
);


ALTER TYPE "public"."enum_users_social_links_platform" OWNER TO "postgres";

SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."_company_reports_v" (
    "id" integer NOT NULL,
    "parent_id" integer,
    "version_slug" character varying,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" "public"."enum__company_reports_v_version_status" DEFAULT 'draft'::"public"."enum__company_reports_v_version_status",
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "latest" boolean,
    "version_title" character varying,
    "version_extract" character varying
);


ALTER TABLE "public"."_company_reports_v" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "analysis" "jsonb",
    "visibility" "public"."enum__company_reports_v_blocks_cr_analysis_visibility",
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "title" character varying,
    "url" character varying,
    "publish_year" numeric,
    "publish_date" character varying,
    "type" "public"."enum__company_reports_v_blocks_cr_analysis_refs_type",
    "extract" character varying,
    "doc_page_id" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "short_id" character varying,
    "name" character varying,
    "cn" character varying,
    "description" character varying,
    "url" character varying,
    "eko_id" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "url" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis_refs_authors"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "name" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis_refs"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_pages" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "page" numeric,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_pages" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_analysis_refs_pages"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_cta" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "title" character varying,
    "description" character varying,
    "cta" character varying,
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_cta" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_cta_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_cta_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_cta_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_cta"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_graph" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "data" "jsonb",
    "_uuid" character varying,
    "block_name" character varying,
    "title" character varying,
    "graph" "public"."enum__company_reports_v_blocks_cr_graph_graph"
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_graph" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_graph_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_graph_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_graph_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_graph"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_overview_graphs" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "version" character varying,
    "data" "jsonb",
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_overview_graphs" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_overview_graphs"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "company_data_short_id" character varying,
    "company_data_name" character varying,
    "company_data_cn" character varying,
    "company_data_description" character varying,
    "company_data_url" character varying,
    "company_data_eko_id" character varying,
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile_company_data_domains" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "url" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_domains" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_profile_company_data_domains"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile_company_data_names" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "name" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_names" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_profile_company_data_names"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_cr_profile_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_cr_profile_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_cr_profile_id_seq" OWNED BY "public"."_company_reports_v_blocks_cr_profile"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_form_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT false,
    "form_id" integer,
    "enable_intro" boolean,
    "intro_content" "jsonb",
    "_uuid" character varying,
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "dark_mode" boolean DEFAULT false
);


ALTER TABLE "public"."_company_reports_v_blocks_form_block" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_form_block_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_form_block_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_form_block_id_seq" OWNED BY "public"."_company_reports_v_blocks_form_block"."id";



CREATE TABLE IF NOT EXISTS "public"."_company_reports_v_blocks_highlighted_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT true,
    "style" "public"."enum__company_reports_v_blocks_highlighted_text_style" DEFAULT 'gradient'::"public"."enum__company_reports_v_blocks_highlighted_text_style",
    "text_content" character varying,
    "text_alignment" "public"."enum__company_reports_v_blocks_highlighted_text_text_alignment" DEFAULT 'center'::"public"."enum__company_reports_v_blocks_highlighted_text_text_alignment",
    "max_width" "public"."enum__company_reports_v_blocks_highlighted_text_max_width" DEFAULT 'large'::"public"."enum__company_reports_v_blocks_highlighted_text_max_width",
    "_uuid" character varying,
    "block_name" character varying,
    "color_theme" "public"."enum__company_reports_v_blocks_highlighted_text_color_theme" DEFAULT 'brand'::"public"."enum__company_reports_v_blocks_highlighted_text_color_theme",
    "animate" boolean DEFAULT false
);


ALTER TABLE "public"."_company_reports_v_blocks_highlighted_text" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_blocks_highlighted_text_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_blocks_highlighted_text_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_blocks_highlighted_text_id_seq" OWNED BY "public"."_company_reports_v_blocks_highlighted_text"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_company_reports_v_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_company_reports_v_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_company_reports_v_id_seq" OWNED BY "public"."_company_reports_v"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v" (
    "id" integer NOT NULL,
    "parent_id" integer,
    "version_title" character varying,
    "version_hero_type" "public"."enum__pages_v_version_hero_type" DEFAULT 'lowImpact'::"public"."enum__pages_v_version_hero_type",
    "version_hero_rich_text" "jsonb",
    "version_hero_media_id" integer,
    "version_meta_title" character varying,
    "version_meta_image_id" integer,
    "version_meta_description" character varying,
    "version_published_at" timestamp(3) with time zone,
    "version_slug" character varying,
    "version_slug_lock" boolean DEFAULT true,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" "public"."enum__pages_v_version_status" DEFAULT 'draft'::"public"."enum__pages_v_version_status",
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "latest" boolean,
    "autosave" boolean,
    "version_hero_title" character varying
);


ALTER TABLE "public"."_pages_v" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_archive" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "intro_content" "jsonb",
    "populate_by" "public"."enum__pages_v_blocks_archive_populate_by" DEFAULT 'collection'::"public"."enum__pages_v_blocks_archive_populate_by",
    "relation_to" "public"."enum__pages_v_blocks_archive_relation_to" DEFAULT 'posts'::"public"."enum__pages_v_blocks_archive_relation_to",
    "limit" numeric DEFAULT 10,
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false,
    "heading" character varying
);


ALTER TABLE "public"."_pages_v_blocks_archive" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_archive_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_archive_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_archive_id_seq" OWNED BY "public"."_pages_v_blocks_archive"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_background_reading" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "tag" character varying,
    "heading" character varying,
    "description" character varying,
    "_uuid" character varying,
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_background_reading" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_background_reading_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_background_reading_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_background_reading_id_seq" OWNED BY "public"."_pages_v_blocks_background_reading"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_background_reading_quotes" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "title" character varying,
    "author" character varying,
    "text" character varying,
    "context" character varying,
    "image_id" integer,
    "link" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_background_reading_quotes" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_background_reading_quotes_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_background_reading_quotes_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_background_reading_quotes_id_seq" OWNED BY "public"."_pages_v_blocks_background_reading_quotes"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_content" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_content" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_content_columns" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "size" "public"."enum__pages_v_blocks_content_columns_size" DEFAULT 'oneThird'::"public"."enum__pages_v_blocks_content_columns_size",
    "rich_text" "jsonb",
    "enable_link" boolean,
    "link_type" "public"."enum__pages_v_blocks_content_columns_link_type" DEFAULT 'reference'::"public"."enum__pages_v_blocks_content_columns_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum__pages_v_blocks_content_columns_link_appearance" DEFAULT 'default'::"public"."enum__pages_v_blocks_content_columns_link_appearance",
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_content_columns" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_content_columns_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_content_columns_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_content_columns_id_seq" OWNED BY "public"."_pages_v_blocks_content_columns"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_content_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_content_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_content_id_seq" OWNED BY "public"."_pages_v_blocks_content"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_cta" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "rich_text" "jsonb",
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_cta" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_cta_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_cta_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_cta_id_seq" OWNED BY "public"."_pages_v_blocks_cta"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_cta_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "link_type" "public"."enum__pages_v_blocks_cta_links_link_type" DEFAULT 'reference'::"public"."enum__pages_v_blocks_cta_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum__pages_v_blocks_cta_links_link_appearance" DEFAULT 'default'::"public"."enum__pages_v_blocks_cta_links_link_appearance",
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_cta_links" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_cta_links_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_cta_links_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_cta_links_id_seq" OWNED BY "public"."_pages_v_blocks_cta_links"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_faq" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "column_layout" "public"."enum__pages_v_blocks_faq_column_layout" DEFAULT 'single'::"public"."enum__pages_v_blocks_faq_column_layout",
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_pages_v_blocks_faq" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_faq_faq_items" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "question" character varying,
    "answer" "jsonb",
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_faq_faq_items" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_faq_faq_items_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_faq_faq_items_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_faq_faq_items_id_seq" OWNED BY "public"."_pages_v_blocks_faq_faq_items"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_faq_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_faq_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_faq_id_seq" OWNED BY "public"."_pages_v_blocks_faq"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_features" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "heading" character varying,
    "_uuid" character varying,
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_features" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_features_features" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "title" character varying,
    "description" character varying,
    "icon_id" integer,
    "_uuid" character varying,
    "rich_text_description" "jsonb"
);


ALTER TABLE "public"."_pages_v_blocks_features_features" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_features_features_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_features_features_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_features_features_id_seq" OWNED BY "public"."_pages_v_blocks_features_features"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_features_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_features_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_features_id_seq" OWNED BY "public"."_pages_v_blocks_features"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_form_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "form_id" integer,
    "enable_intro" boolean,
    "intro_content" "jsonb",
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "dark_mode" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_form_block" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_form_block_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_form_block_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_form_block_id_seq" OWNED BY "public"."_pages_v_blocks_form_block"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_highlighted_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT true,
    "style" "public"."enum__pages_v_blocks_highlighted_text_style" DEFAULT 'gradient'::"public"."enum__pages_v_blocks_highlighted_text_style",
    "text_content" character varying,
    "text_alignment" "public"."enum__pages_v_blocks_highlighted_text_text_alignment" DEFAULT 'center'::"public"."enum__pages_v_blocks_highlighted_text_text_alignment",
    "max_width" "public"."enum__pages_v_blocks_highlighted_text_max_width" DEFAULT 'large'::"public"."enum__pages_v_blocks_highlighted_text_max_width",
    "_uuid" character varying,
    "block_name" character varying,
    "color_theme" "public"."enum__pages_v_blocks_highlighted_text_color_theme" DEFAULT 'brand'::"public"."enum__pages_v_blocks_highlighted_text_color_theme",
    "animate" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_highlighted_text" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_highlighted_text_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_highlighted_text_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_highlighted_text_id_seq" OWNED BY "public"."_pages_v_blocks_highlighted_text"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_image_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "content" "jsonb",
    "media_id" integer,
    "image_size" "public"."enum__pages_v_blocks_image_text_image_size" DEFAULT 'medium'::"public"."enum__pages_v_blocks_image_text_image_size",
    "image_border_radius" "public"."enum__pages_v_blocks_image_text_image_border_radius" DEFAULT 'medium'::"public"."enum__pages_v_blocks_image_text_image_border_radius",
    "heading_position" "public"."enum__pages_v_blocks_image_text_heading_position" DEFAULT 'above'::"public"."enum__pages_v_blocks_image_text_heading_position",
    "heading_alignment" "public"."enum__pages_v_blocks_image_text_heading_alignment" DEFAULT 'center'::"public"."enum__pages_v_blocks_image_text_heading_alignment",
    "se_background" boolean DEFAULT false,
    "se_background_media_id" integer,
    "se_color" "public"."enum__pages_v_blocks_image_text_se_color" DEFAULT 'default'::"public"."enum__pages_v_blocks_image_text_se_color",
    "se_enable_shadow" boolean DEFAULT true,
    "se_enable_hover_effect" boolean DEFAULT true,
    "_uuid" character varying,
    "block_name" character varying,
    "layout_style" "public"."enum__pages_v_blocks_image_text_layout_style" DEFAULT 'textBelow'::"public"."enum__pages_v_blocks_image_text_layout_style"
);


ALTER TABLE "public"."_pages_v_blocks_image_text" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_image_text_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_image_text_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_image_text_id_seq" OWNED BY "public"."_pages_v_blocks_image_text"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_media_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "media_id" integer,
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_media_block" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_media_block_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_media_block_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_media_block_id_seq" OWNED BY "public"."_pages_v_blocks_media_block"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_newsletter_signup" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "title" character varying DEFAULT 'Join Our Growing Community'::character varying,
    "subtitle" character varying DEFAULT 'Sign up to stay up to date with the latest news and updates from ekoIntelligence.'::character varying,
    "button_label" character varying DEFAULT 'Sign Up'::character varying,
    "placeholder" character varying DEFAULT 'Enter your email'::character varying,
    "_uuid" character varying,
    "block_name" character varying,
    "background_id" integer,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_newsletter_signup" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_newsletter_signup_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_newsletter_signup_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_newsletter_signup_id_seq" OWNED BY "public"."_pages_v_blocks_newsletter_signup"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_pricing" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "title" character varying,
    "_uuid" character varying,
    "block_name" character varying,
    "intro" "jsonb",
    "inset" boolean DEFAULT false,
    "altcolor" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_pricing" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_pricing_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_pricing_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_pricing_id_seq" OWNED BY "public"."_pages_v_blocks_pricing"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_pricing_pricing_options" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "plan_name" character varying,
    "price" numeric,
    "price_period" character varying DEFAULT '/month'::character varying,
    "button_text" character varying DEFAULT 'Get Started'::character varying,
    "button_link" character varying,
    "is_recommended" boolean DEFAULT false,
    "_uuid" character varying,
    "sub_title" character varying
);


ALTER TABLE "public"."_pages_v_blocks_pricing_pricing_options" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_pricing_pricing_options_features" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "feature" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_pricing_pricing_options_features" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq" OWNED BY "public"."_pages_v_blocks_pricing_pricing_options_features"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_pricing_pricing_options_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_pricing_pricing_options_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_id_seq" OWNED BY "public"."_pages_v_blocks_pricing_pricing_options"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_social_proof" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "monochrome" boolean DEFAULT true,
    "_uuid" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."_pages_v_blocks_social_proof" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_social_proof_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_social_proof_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_social_proof_id_seq" OWNED BY "public"."_pages_v_blocks_social_proof"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_social_proof_logos" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "logo_id" integer,
    "name" character varying,
    "url" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_social_proof_logos" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_social_proof_logos_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_social_proof_logos_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_social_proof_logos_id_seq" OWNED BY "public"."_pages_v_blocks_social_proof_logos"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_team" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" integer NOT NULL,
    "_uuid" character varying,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."_pages_v_blocks_team" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_team_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_team_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_team_id_seq" OWNED BY "public"."_pages_v_blocks_team"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_blocks_team_team_members" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "name" character varying,
    "role" character varying,
    "description" "jsonb",
    "media_id" integer,
    "url" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_blocks_team_team_members" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_blocks_team_team_members_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_blocks_team_team_members_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_blocks_team_team_members_id_seq" OWNED BY "public"."_pages_v_blocks_team_team_members"."id";



CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_id_seq" OWNED BY "public"."_pages_v"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer,
    "categories_id" integer
);


ALTER TABLE "public"."_pages_v_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_rels_id_seq" OWNED BY "public"."_pages_v_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."_pages_v_version_hero_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "link_type" "public"."enum__pages_v_version_hero_links_link_type" DEFAULT 'reference'::"public"."enum__pages_v_version_hero_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum__pages_v_version_hero_links_link_appearance" DEFAULT 'default'::"public"."enum__pages_v_version_hero_links_link_appearance",
    "_uuid" character varying
);


ALTER TABLE "public"."_pages_v_version_hero_links" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_pages_v_version_hero_links_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_pages_v_version_hero_links_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_pages_v_version_hero_links_id_seq" OWNED BY "public"."_pages_v_version_hero_links"."id";



CREATE TABLE IF NOT EXISTS "public"."_posts_v" (
    "id" integer NOT NULL,
    "parent_id" integer,
    "version_title" character varying,
    "version_hero_image_id" integer,
    "version_content" "jsonb",
    "version_meta_title" character varying,
    "version_meta_image_id" integer,
    "version_meta_description" character varying,
    "version_published_at" timestamp(3) with time zone,
    "version_slug" character varying,
    "version_slug_lock" boolean DEFAULT true,
    "version_updated_at" timestamp(3) with time zone,
    "version_created_at" timestamp(3) with time zone,
    "version__status" "public"."enum__posts_v_version_status" DEFAULT 'draft'::"public"."enum__posts_v_version_status",
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "latest" boolean,
    "autosave" boolean
);


ALTER TABLE "public"."_posts_v" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_posts_v_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_posts_v_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_posts_v_id_seq" OWNED BY "public"."_posts_v"."id";



CREATE TABLE IF NOT EXISTS "public"."_posts_v_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "posts_id" integer,
    "categories_id" integer,
    "users_id" integer
);


ALTER TABLE "public"."_posts_v_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_posts_v_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_posts_v_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_posts_v_rels_id_seq" OWNED BY "public"."_posts_v_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."_posts_v_version_populated_authors" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "_uuid" character varying,
    "name" character varying
);


ALTER TABLE "public"."_posts_v_version_populated_authors" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_posts_v_version_populated_authors_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_posts_v_version_populated_authors_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_posts_v_version_populated_authors_id_seq" OWNED BY "public"."_posts_v_version_populated_authors"."id";



CREATE TABLE IF NOT EXISTS "public"."_posts_v_version_populated_authors_social_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" integer NOT NULL,
    "platform" character varying,
    "url" character varying,
    "_uuid" character varying
);


ALTER TABLE "public"."_posts_v_version_populated_authors_social_links" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."_posts_v_version_populated_authors_social_links_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."_posts_v_version_populated_authors_social_links_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."_posts_v_version_populated_authors_social_links_id_seq" OWNED BY "public"."_posts_v_version_populated_authors_social_links"."id";



CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" integer NOT NULL,
    "title" character varying NOT NULL,
    "slug" character varying,
    "slug_lock" boolean DEFAULT true,
    "parent_id" integer,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."categories" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."categories_breadcrumbs" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "doc_id" integer,
    "url" character varying,
    "label" character varying
);


ALTER TABLE "public"."categories_breadcrumbs" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."categories_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."categories_id_seq" OWNED BY "public"."categories"."id";



CREATE TABLE IF NOT EXISTS "public"."company_reports" (
    "id" integer NOT NULL,
    "slug" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "_status" "public"."enum_company_reports_status" DEFAULT 'draft'::"public"."enum_company_reports_status",
    "title" character varying,
    "extract" character varying
);


ALTER TABLE "public"."company_reports" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "analysis" "jsonb",
    "visibility" "public"."enum_company_reports_blocks_cr_analysis_visibility",
    "block_name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis_refs" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "url" character varying,
    "publish_year" numeric,
    "publish_date" character varying,
    "type" "public"."enum_company_reports_blocks_cr_analysis_refs_type",
    "extract" character varying,
    "doc_page_id" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis_refs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis_refs_authors" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "short_id" character varying,
    "name" character varying,
    "cn" character varying,
    "description" character varying,
    "url" character varying,
    "eko_id" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis_refs_authors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis_refs_authors_domains" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "url" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_domains" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis_refs_authors_names" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_names" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_analysis_refs_pages" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "page" numeric
);


ALTER TABLE "public"."company_reports_blocks_cr_analysis_refs_pages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_cta" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "description" character varying,
    "cta" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_cta" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_graph" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "graph" "public"."enum_company_reports_blocks_cr_graph_graph",
    "data" "jsonb",
    "block_name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_graph" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_overview_graphs" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "version" character varying,
    "data" "jsonb",
    "block_name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_overview_graphs" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_profile" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "company_data_short_id" character varying,
    "company_data_name" character varying,
    "company_data_cn" character varying,
    "company_data_description" character varying,
    "company_data_url" character varying,
    "company_data_eko_id" character varying,
    "block_name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_profile" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_profile_company_data_domains" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "url" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_profile_company_data_domains" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_cr_profile_company_data_names" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying
);


ALTER TABLE "public"."company_reports_blocks_cr_profile_company_data_names" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_form_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT false,
    "form_id" integer,
    "enable_intro" boolean,
    "intro_content" "jsonb",
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "dark_mode" boolean DEFAULT false
);


ALTER TABLE "public"."company_reports_blocks_form_block" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."company_reports_blocks_highlighted_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT true,
    "style" "public"."enum_company_reports_blocks_highlighted_text_style" DEFAULT 'gradient'::"public"."enum_company_reports_blocks_highlighted_text_style",
    "text_content" character varying,
    "text_alignment" "public"."enum_company_reports_blocks_highlighted_text_text_alignment" DEFAULT 'center'::"public"."enum_company_reports_blocks_highlighted_text_text_alignment",
    "max_width" "public"."enum_company_reports_blocks_highlighted_text_max_width" DEFAULT 'large'::"public"."enum_company_reports_blocks_highlighted_text_max_width",
    "block_name" character varying,
    "color_theme" "public"."enum_company_reports_blocks_highlighted_text_color_theme" DEFAULT 'brand'::"public"."enum_company_reports_blocks_highlighted_text_color_theme",
    "animate" boolean DEFAULT false
);


ALTER TABLE "public"."company_reports_blocks_highlighted_text" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."company_reports_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."company_reports_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."company_reports_id_seq" OWNED BY "public"."company_reports"."id";



CREATE TABLE IF NOT EXISTS "public"."footer" (
    "id" integer NOT NULL,
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone,
    "company_info_description" character varying DEFAULT 'ekoIntelligence – Behaviour Driven ESG Analytics. Discover how our single tool provides in-depth, foolproof analysis of your or your competitors'''' ESG behaviour.'::character varying NOT NULL,
    "copyright_text" character varying DEFAULT '© {year} ekoIntelligence. All rights reserved.'::character varying
);


ALTER TABLE "public"."footer" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_company_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_company_links_link_type" DEFAULT 'reference'::"public"."enum_footer_company_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_company_links" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."footer_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."footer_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."footer_id_seq" OWNED BY "public"."footer"."id";



CREATE TABLE IF NOT EXISTS "public"."footer_quick_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_quick_links_link_type" DEFAULT 'reference'::"public"."enum_footer_quick_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_quick_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer
);


ALTER TABLE "public"."footer_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."footer_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."footer_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."footer_rels_id_seq" OWNED BY "public"."footer_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."footer_resource_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_resource_links_link_type" DEFAULT 'reference'::"public"."enum_footer_resource_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_resource_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_social_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "platform" "public"."enum_footer_social_links_platform" NOT NULL,
    "url" character varying NOT NULL
);


ALTER TABLE "public"."footer_social_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."footer_solution_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_footer_solution_links_link_type" DEFAULT 'reference'::"public"."enum_footer_solution_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."footer_solution_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."form_submissions" (
    "id" integer NOT NULL,
    "form_id" integer NOT NULL,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."form_submissions" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."form_submissions_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."form_submissions_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."form_submissions_id_seq" OWNED BY "public"."form_submissions"."id";



CREATE TABLE IF NOT EXISTS "public"."form_submissions_submission_data" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "field" character varying NOT NULL,
    "value" character varying NOT NULL
);


ALTER TABLE "public"."form_submissions_submission_data" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms" (
    "id" integer NOT NULL,
    "title" character varying NOT NULL,
    "submit_button_label" character varying,
    "confirmation_type" "public"."enum_forms_confirmation_type" DEFAULT 'message'::"public"."enum_forms_confirmation_type",
    "confirmation_message" "jsonb",
    "redirect_url" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."forms" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_checkbox" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "required" boolean,
    "default_value" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_checkbox" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_country" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_country" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_email" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_email" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_message" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "message" "jsonb",
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_message" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_number" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "default_value" numeric,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_number" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_select" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "default_value" character varying,
    "required" boolean,
    "block_name" character varying,
    "placeholder" character varying
);


ALTER TABLE "public"."forms_blocks_select" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_select_options" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "label" character varying NOT NULL,
    "value" character varying NOT NULL
);


ALTER TABLE "public"."forms_blocks_select_options" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_state" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_state" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "default_value" character varying,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_text" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_blocks_textarea" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying NOT NULL,
    "label" character varying,
    "width" numeric,
    "default_value" character varying,
    "required" boolean,
    "block_name" character varying
);


ALTER TABLE "public"."forms_blocks_textarea" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."forms_emails" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "email_to" character varying,
    "cc" character varying,
    "bcc" character varying,
    "reply_to" character varying,
    "email_from" character varying,
    "subject" character varying DEFAULT 'You''''ve received a new message.'::character varying NOT NULL,
    "message" "jsonb"
);


ALTER TABLE "public"."forms_emails" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."forms_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."forms_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."forms_id_seq" OWNED BY "public"."forms"."id";



CREATE TABLE IF NOT EXISTS "public"."header" (
    "id" integer NOT NULL,
    "updated_at" timestamp(3) with time zone,
    "created_at" timestamp(3) with time zone
);


ALTER TABLE "public"."header" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."header_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."header_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."header_id_seq" OWNED BY "public"."header"."id";



CREATE TABLE IF NOT EXISTS "public"."header_nav_items" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_header_nav_items_link_type" DEFAULT 'reference'::"public"."enum_header_nav_items_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying NOT NULL
);


ALTER TABLE "public"."header_nav_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."header_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer
);


ALTER TABLE "public"."header_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."header_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."header_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."header_rels_id_seq" OWNED BY "public"."header_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."media" (
    "id" integer NOT NULL,
    "alt" character varying,
    "caption" "jsonb",
    "prefix" character varying DEFAULT 'media'::character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "url" character varying,
    "thumbnail_u_r_l" character varying,
    "filename" character varying,
    "mime_type" character varying,
    "filesize" numeric,
    "width" numeric,
    "height" numeric,
    "focal_x" numeric,
    "focal_y" numeric,
    "sizes_thumbnail_url" character varying,
    "sizes_thumbnail_width" numeric,
    "sizes_thumbnail_height" numeric,
    "sizes_thumbnail_mime_type" character varying,
    "sizes_thumbnail_filesize" numeric,
    "sizes_thumbnail_filename" character varying,
    "sizes_square_url" character varying,
    "sizes_square_width" numeric,
    "sizes_square_height" numeric,
    "sizes_square_mime_type" character varying,
    "sizes_square_filesize" numeric,
    "sizes_square_filename" character varying,
    "sizes_small_url" character varying,
    "sizes_small_width" numeric,
    "sizes_small_height" numeric,
    "sizes_small_mime_type" character varying,
    "sizes_small_filesize" numeric,
    "sizes_small_filename" character varying,
    "sizes_medium_url" character varying,
    "sizes_medium_width" numeric,
    "sizes_medium_height" numeric,
    "sizes_medium_mime_type" character varying,
    "sizes_medium_filesize" numeric,
    "sizes_medium_filename" character varying,
    "sizes_large_url" character varying,
    "sizes_large_width" numeric,
    "sizes_large_height" numeric,
    "sizes_large_mime_type" character varying,
    "sizes_large_filesize" numeric,
    "sizes_large_filename" character varying,
    "sizes_xlarge_url" character varying,
    "sizes_xlarge_width" numeric,
    "sizes_xlarge_height" numeric,
    "sizes_xlarge_mime_type" character varying,
    "sizes_xlarge_filesize" numeric,
    "sizes_xlarge_filename" character varying,
    "sizes_og_url" character varying,
    "sizes_og_width" numeric,
    "sizes_og_height" numeric,
    "sizes_og_mime_type" character varying,
    "sizes_og_filesize" numeric,
    "sizes_og_filename" character varying
);


ALTER TABLE "public"."media" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."media_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."media_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."media_id_seq" OWNED BY "public"."media"."id";



CREATE TABLE IF NOT EXISTS "public"."pages" (
    "id" integer NOT NULL,
    "title" character varying,
    "hero_type" "public"."enum_pages_hero_type" DEFAULT 'lowImpact'::"public"."enum_pages_hero_type",
    "hero_rich_text" "jsonb",
    "hero_media_id" integer,
    "meta_title" character varying,
    "meta_image_id" integer,
    "meta_description" character varying,
    "published_at" timestamp(3) with time zone,
    "slug" character varying,
    "slug_lock" boolean DEFAULT true,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "_status" "public"."enum_pages_status" DEFAULT 'draft'::"public"."enum_pages_status",
    "hero_title" character varying
);


ALTER TABLE "public"."pages" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_archive" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "intro_content" "jsonb",
    "populate_by" "public"."enum_pages_blocks_archive_populate_by" DEFAULT 'collection'::"public"."enum_pages_blocks_archive_populate_by",
    "relation_to" "public"."enum_pages_blocks_archive_relation_to" DEFAULT 'posts'::"public"."enum_pages_blocks_archive_relation_to",
    "limit" numeric DEFAULT 10,
    "block_name" character varying,
    "inset" boolean DEFAULT false,
    "heading" character varying
);


ALTER TABLE "public"."pages_blocks_archive" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_background_reading" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "tag" character varying,
    "heading" character varying,
    "description" character varying,
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_background_reading" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_background_reading_quotes" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "author" character varying,
    "text" character varying,
    "context" character varying,
    "image_id" integer,
    "link" character varying
);


ALTER TABLE "public"."pages_blocks_background_reading_quotes" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_content" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_content" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_content_columns" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "size" "public"."enum_pages_blocks_content_columns_size" DEFAULT 'oneThird'::"public"."enum_pages_blocks_content_columns_size",
    "rich_text" "jsonb",
    "enable_link" boolean,
    "link_type" "public"."enum_pages_blocks_content_columns_link_type" DEFAULT 'reference'::"public"."enum_pages_blocks_content_columns_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum_pages_blocks_content_columns_link_appearance" DEFAULT 'default'::"public"."enum_pages_blocks_content_columns_link_appearance"
);


ALTER TABLE "public"."pages_blocks_content_columns" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_cta" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "rich_text" "jsonb",
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_cta" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_cta_links" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_pages_blocks_cta_links_link_type" DEFAULT 'reference'::"public"."enum_pages_blocks_cta_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum_pages_blocks_cta_links_link_appearance" DEFAULT 'default'::"public"."enum_pages_blocks_cta_links_link_appearance"
);


ALTER TABLE "public"."pages_blocks_cta_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_faq" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "column_layout" "public"."enum_pages_blocks_faq_column_layout" DEFAULT 'single'::"public"."enum_pages_blocks_faq_column_layout",
    "block_name" character varying
);


ALTER TABLE "public"."pages_blocks_faq" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_faq_faq_items" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "question" character varying,
    "answer" "jsonb"
);


ALTER TABLE "public"."pages_blocks_faq_faq_items" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_features" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "heading" character varying,
    "block_name" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_features" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_features_features" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "description" character varying,
    "icon_id" integer,
    "rich_text_description" "jsonb"
);


ALTER TABLE "public"."pages_blocks_features_features" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_form_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "form_id" integer,
    "enable_intro" boolean,
    "intro_content" "jsonb",
    "block_name" character varying,
    "inset" boolean DEFAULT false,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "dark_mode" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_form_block" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_highlighted_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT true,
    "style" "public"."enum_pages_blocks_highlighted_text_style" DEFAULT 'gradient'::"public"."enum_pages_blocks_highlighted_text_style",
    "text_content" character varying,
    "text_alignment" "public"."enum_pages_blocks_highlighted_text_text_alignment" DEFAULT 'center'::"public"."enum_pages_blocks_highlighted_text_text_alignment",
    "max_width" "public"."enum_pages_blocks_highlighted_text_max_width" DEFAULT 'large'::"public"."enum_pages_blocks_highlighted_text_max_width",
    "block_name" character varying,
    "color_theme" "public"."enum_pages_blocks_highlighted_text_color_theme" DEFAULT 'brand'::"public"."enum_pages_blocks_highlighted_text_color_theme",
    "animate" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_highlighted_text" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_image_text" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "content" "jsonb",
    "media_id" integer,
    "image_size" "public"."enum_pages_blocks_image_text_image_size" DEFAULT 'medium'::"public"."enum_pages_blocks_image_text_image_size",
    "image_border_radius" "public"."enum_pages_blocks_image_text_image_border_radius" DEFAULT 'medium'::"public"."enum_pages_blocks_image_text_image_border_radius",
    "heading_position" "public"."enum_pages_blocks_image_text_heading_position" DEFAULT 'above'::"public"."enum_pages_blocks_image_text_heading_position",
    "heading_alignment" "public"."enum_pages_blocks_image_text_heading_alignment" DEFAULT 'center'::"public"."enum_pages_blocks_image_text_heading_alignment",
    "se_background" boolean DEFAULT false,
    "se_background_media_id" integer,
    "se_color" "public"."enum_pages_blocks_image_text_se_color" DEFAULT 'default'::"public"."enum_pages_blocks_image_text_se_color",
    "se_enable_shadow" boolean DEFAULT true,
    "se_enable_hover_effect" boolean DEFAULT true,
    "block_name" character varying,
    "layout_style" "public"."enum_pages_blocks_image_text_layout_style" DEFAULT 'textBelow'::"public"."enum_pages_blocks_image_text_layout_style"
);


ALTER TABLE "public"."pages_blocks_image_text" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_media_block" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "media_id" integer,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_media_block" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_newsletter_signup" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying DEFAULT 'Join Our Growing Community'::character varying,
    "subtitle" character varying DEFAULT 'Sign up to stay up to date with the latest news and updates from ekoIntelligence.'::character varying,
    "button_label" character varying DEFAULT 'Sign Up'::character varying,
    "placeholder" character varying DEFAULT 'Enter your email'::character varying,
    "block_name" character varying,
    "background_id" integer,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_newsletter_signup" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_pricing" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "title" character varying,
    "block_name" character varying,
    "intro" "jsonb",
    "inset" boolean DEFAULT false,
    "altcolor" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_pricing" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_pricing_pricing_options" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "plan_name" character varying,
    "price" numeric,
    "price_period" character varying DEFAULT '/month'::character varying,
    "button_text" character varying DEFAULT 'Get Started'::character varying,
    "button_link" character varying,
    "is_recommended" boolean DEFAULT false,
    "sub_title" character varying
);


ALTER TABLE "public"."pages_blocks_pricing_pricing_options" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_pricing_pricing_options_features" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "feature" character varying
);


ALTER TABLE "public"."pages_blocks_pricing_pricing_options_features" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_social_proof" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "inset" boolean DEFAULT false,
    "heading" character varying,
    "subheading" character varying,
    "background" boolean DEFAULT false,
    "background_media_id" integer,
    "monochrome" boolean DEFAULT true,
    "block_name" character varying
);


ALTER TABLE "public"."pages_blocks_social_proof" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_social_proof_logos" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "logo_id" integer,
    "name" character varying,
    "url" character varying
);


ALTER TABLE "public"."pages_blocks_social_proof_logos" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_team" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "_path" "text" NOT NULL,
    "id" character varying NOT NULL,
    "block_name" character varying,
    "inset" boolean DEFAULT false
);


ALTER TABLE "public"."pages_blocks_team" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_blocks_team_team_members" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying,
    "role" character varying,
    "description" "jsonb",
    "media_id" integer,
    "url" character varying
);


ALTER TABLE "public"."pages_blocks_team_team_members" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."pages_hero_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "link_type" "public"."enum_pages_hero_links_link_type" DEFAULT 'reference'::"public"."enum_pages_hero_links_link_type",
    "link_new_tab" boolean,
    "link_url" character varying,
    "link_label" character varying,
    "link_appearance" "public"."enum_pages_hero_links_link_appearance" DEFAULT 'default'::"public"."enum_pages_hero_links_link_appearance"
);


ALTER TABLE "public"."pages_hero_links" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."pages_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."pages_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."pages_id_seq" OWNED BY "public"."pages"."id";



CREATE TABLE IF NOT EXISTS "public"."pages_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer,
    "categories_id" integer
);


ALTER TABLE "public"."pages_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."pages_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."pages_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."pages_rels_id_seq" OWNED BY "public"."pages_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_jobs" (
    "id" integer NOT NULL,
    "input" "jsonb",
    "completed_at" timestamp(3) with time zone,
    "total_tried" numeric DEFAULT 0,
    "has_error" boolean DEFAULT false,
    "error" "jsonb",
    "task_slug" "public"."enum_payload_jobs_task_slug",
    "queue" character varying DEFAULT 'default'::character varying,
    "wait_until" timestamp(3) with time zone,
    "processing" boolean DEFAULT false,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_jobs" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_jobs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_jobs_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_jobs_id_seq" OWNED BY "public"."payload_jobs"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_jobs_log" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "executed_at" timestamp(3) with time zone NOT NULL,
    "completed_at" timestamp(3) with time zone NOT NULL,
    "task_slug" "public"."enum_payload_jobs_log_task_slug" NOT NULL,
    "task_i_d" character varying NOT NULL,
    "input" "jsonb",
    "output" "jsonb",
    "state" "public"."enum_payload_jobs_log_state" NOT NULL,
    "error" "jsonb"
);


ALTER TABLE "public"."payload_jobs_log" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."payload_locked_documents" (
    "id" integer NOT NULL,
    "global_slug" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_locked_documents" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_locked_documents_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_locked_documents_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_locked_documents_id_seq" OWNED BY "public"."payload_locked_documents"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_locked_documents_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer,
    "media_id" integer,
    "categories_id" integer,
    "users_id" integer,
    "company_reports_id" integer,
    "redirects_id" integer,
    "forms_id" integer,
    "form_submissions_id" integer,
    "search_id" integer,
    "payload_jobs_id" integer
);


ALTER TABLE "public"."payload_locked_documents_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_locked_documents_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_locked_documents_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_locked_documents_rels_id_seq" OWNED BY "public"."payload_locked_documents_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_migrations" (
    "id" integer NOT NULL,
    "name" character varying,
    "batch" numeric,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_migrations" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_migrations_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_migrations_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_migrations_id_seq" OWNED BY "public"."payload_migrations"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_preferences" (
    "id" integer NOT NULL,
    "key" character varying,
    "value" "jsonb",
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."payload_preferences" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_preferences_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_preferences_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_preferences_id_seq" OWNED BY "public"."payload_preferences"."id";



CREATE TABLE IF NOT EXISTS "public"."payload_preferences_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "users_id" integer
);


ALTER TABLE "public"."payload_preferences_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."payload_preferences_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."payload_preferences_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."payload_preferences_rels_id_seq" OWNED BY "public"."payload_preferences_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."posts" (
    "id" integer NOT NULL,
    "title" character varying,
    "hero_image_id" integer,
    "content" "jsonb",
    "meta_title" character varying,
    "meta_image_id" integer,
    "meta_description" character varying,
    "published_at" timestamp(3) with time zone,
    "slug" character varying,
    "slug_lock" boolean DEFAULT true,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "_status" "public"."enum_posts_status" DEFAULT 'draft'::"public"."enum_posts_status"
);


ALTER TABLE "public"."posts" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."posts_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."posts_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."posts_id_seq" OWNED BY "public"."posts"."id";



CREATE TABLE IF NOT EXISTS "public"."posts_populated_authors" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "name" character varying
);


ALTER TABLE "public"."posts_populated_authors" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."posts_populated_authors_social_links" (
    "_order" integer NOT NULL,
    "_parent_id" character varying NOT NULL,
    "id" character varying NOT NULL,
    "platform" character varying,
    "url" character varying
);


ALTER TABLE "public"."posts_populated_authors_social_links" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."posts_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "posts_id" integer,
    "categories_id" integer,
    "users_id" integer
);


ALTER TABLE "public"."posts_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."posts_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."posts_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."posts_rels_id_seq" OWNED BY "public"."posts_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."redirects" (
    "id" integer NOT NULL,
    "from" character varying NOT NULL,
    "to_type" "public"."enum_redirects_to_type" DEFAULT 'reference'::"public"."enum_redirects_to_type",
    "to_url" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."redirects" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."redirects_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."redirects_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."redirects_id_seq" OWNED BY "public"."redirects"."id";



CREATE TABLE IF NOT EXISTS "public"."redirects_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "pages_id" integer,
    "posts_id" integer
);


ALTER TABLE "public"."redirects_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."redirects_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."redirects_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."redirects_rels_id_seq" OWNED BY "public"."redirects_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."search" (
    "id" integer NOT NULL,
    "title" character varying,
    "priority" numeric,
    "slug" character varying,
    "meta_title" character varying,
    "meta_description" character varying,
    "meta_image_id" integer,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL
);


ALTER TABLE "public"."search" OWNER TO "postgres";


CREATE TABLE IF NOT EXISTS "public"."search_categories" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "relation_to" character varying,
    "title" character varying
);


ALTER TABLE "public"."search_categories" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."search_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."search_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."search_id_seq" OWNED BY "public"."search"."id";



CREATE TABLE IF NOT EXISTS "public"."search_rels" (
    "id" integer NOT NULL,
    "order" integer,
    "parent_id" integer NOT NULL,
    "path" character varying NOT NULL,
    "posts_id" integer,
    "pages_id" integer,
    "company_reports_id" integer
);


ALTER TABLE "public"."search_rels" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."search_rels_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."search_rels_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."search_rels_id_seq" OWNED BY "public"."search_rels"."id";



CREATE TABLE IF NOT EXISTS "public"."users" (
    "id" integer NOT NULL,
    "name" character varying,
    "updated_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "created_at" timestamp(3) with time zone DEFAULT "now"() NOT NULL,
    "email" character varying NOT NULL,
    "reset_password_token" character varying,
    "reset_password_expiration" timestamp(3) with time zone,
    "salt" character varying,
    "hash" character varying,
    "login_attempts" numeric DEFAULT 0,
    "lock_until" timestamp(3) with time zone
);


ALTER TABLE "public"."users" OWNER TO "postgres";


CREATE SEQUENCE IF NOT EXISTS "public"."users_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER TABLE "public"."users_id_seq" OWNER TO "postgres";


ALTER SEQUENCE "public"."users_id_seq" OWNED BY "public"."users"."id";



CREATE TABLE IF NOT EXISTS "public"."users_social_links" (
    "_order" integer NOT NULL,
    "_parent_id" integer NOT NULL,
    "id" character varying NOT NULL,
    "platform" "public"."enum_users_social_links_platform" NOT NULL,
    "url" character varying NOT NULL
);


ALTER TABLE "public"."users_social_links" OWNER TO "postgres";


ALTER TABLE ONLY "public"."_company_reports_v" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_refs_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_pages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_cta" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_cta_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_graph" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_graph_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_overview_graphs" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_overview_graphs_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_profile_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_domains" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_names" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_form_block" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_form_block_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_highlighted_text" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_company_reports_v_blocks_highlighted_text_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_archive" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_archive_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_background_reading_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading_quotes" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_background_reading_quotes_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_content" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_content_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_content_columns" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_content_columns_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_cta" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_cta_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_cta_links" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_cta_links_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_faq" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_faq_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_faq_faq_items" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_faq_faq_items_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_features" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_features_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_features_features" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_features_features_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_form_block" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_form_block_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_highlighted_text" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_highlighted_text_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_image_text" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_image_text_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_media_block" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_media_block_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_newsletter_signup" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_newsletter_signup_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_pricing_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_pricing_pricing_options_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options_features" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_pricing_pricing_options_features_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_social_proof_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof_logos" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_social_proof_logos_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_team" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_team_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_blocks_team_team_members" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_blocks_team_team_members_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_pages_v_version_hero_links" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_pages_v_version_hero_links_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_posts_v" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_posts_v_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_posts_v_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_posts_v_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_posts_v_version_populated_authors_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors_social_links" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."_posts_v_version_populated_authors_social_links_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."categories_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."company_reports" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."company_reports_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."footer" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."footer_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."footer_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."footer_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."form_submissions" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."form_submissions_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."forms" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."forms_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."header" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."header_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."header_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."header_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."media" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."media_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."pages" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."pages_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."pages_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."pages_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_jobs" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_jobs_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_locked_documents" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_locked_documents_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_locked_documents_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_locked_documents_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_migrations" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_migrations_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_preferences" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_preferences_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."payload_preferences_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."payload_preferences_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."posts" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."posts_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."posts_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."posts_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."redirects" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."redirects_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."redirects_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."redirects_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."search" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."search_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."search_rels" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."search_rels_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."users" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."users_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_domains_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_names_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_pages"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_pages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_cta"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_cta_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_graph"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_graph_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_overview_graphs"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_overview_graphs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_domains"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_company_data_domains_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_names"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_company_data_names_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_form_block"
    ADD CONSTRAINT "_company_reports_v_blocks_form_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_highlighted_text"
    ADD CONSTRAINT "_company_reports_v_blocks_highlighted_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_company_reports_v"
    ADD CONSTRAINT "_company_reports_v_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_archive"
    ADD CONSTRAINT "_pages_v_blocks_archive_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading"
    ADD CONSTRAINT "_pages_v_blocks_background_reading_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading_quotes"
    ADD CONSTRAINT "_pages_v_blocks_background_reading_quotes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_content_columns"
    ADD CONSTRAINT "_pages_v_blocks_content_columns_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_content"
    ADD CONSTRAINT "_pages_v_blocks_content_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_cta_links"
    ADD CONSTRAINT "_pages_v_blocks_cta_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_cta"
    ADD CONSTRAINT "_pages_v_blocks_cta_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_faq_faq_items"
    ADD CONSTRAINT "_pages_v_blocks_faq_faq_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_faq"
    ADD CONSTRAINT "_pages_v_blocks_faq_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_features_features"
    ADD CONSTRAINT "_pages_v_blocks_features_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_features"
    ADD CONSTRAINT "_pages_v_blocks_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_form_block"
    ADD CONSTRAINT "_pages_v_blocks_form_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_highlighted_text"
    ADD CONSTRAINT "_pages_v_blocks_highlighted_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_image_text"
    ADD CONSTRAINT "_pages_v_blocks_image_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_media_block"
    ADD CONSTRAINT "_pages_v_blocks_media_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_newsletter_signup"
    ADD CONSTRAINT "_pages_v_blocks_newsletter_signup_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing"
    ADD CONSTRAINT "_pages_v_blocks_pricing_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options_features"
    ADD CONSTRAINT "_pages_v_blocks_pricing_pricing_options_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options"
    ADD CONSTRAINT "_pages_v_blocks_pricing_pricing_options_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof_logos"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_logos_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_team"
    ADD CONSTRAINT "_pages_v_blocks_team_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_blocks_team_team_members"
    ADD CONSTRAINT "_pages_v_blocks_team_team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_rels"
    ADD CONSTRAINT "_pages_v_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_pages_v_version_hero_links"
    ADD CONSTRAINT "_pages_v_version_hero_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_posts_v"
    ADD CONSTRAINT "_posts_v_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_posts_v_rels"
    ADD CONSTRAINT "_posts_v_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors"
    ADD CONSTRAINT "_posts_v_version_populated_authors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors_social_links"
    ADD CONSTRAINT "_posts_v_version_populated_authors_social_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."categories_breadcrumbs"
    ADD CONSTRAINT "categories_breadcrumbs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors_domains"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_domains_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors_names"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_names_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_pages"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_pages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_cta"
    ADD CONSTRAINT "company_reports_blocks_cr_cta_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_graph"
    ADD CONSTRAINT "company_reports_blocks_cr_graph_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_overview_graphs"
    ADD CONSTRAINT "company_reports_blocks_cr_overview_graphs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile_company_data_domains"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_company_data_domains_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile_company_data_names"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_company_data_names_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_form_block"
    ADD CONSTRAINT "company_reports_blocks_form_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports_blocks_highlighted_text"
    ADD CONSTRAINT "company_reports_blocks_highlighted_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."company_reports"
    ADD CONSTRAINT "company_reports_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_company_links"
    ADD CONSTRAINT "footer_company_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer"
    ADD CONSTRAINT "footer_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_quick_links"
    ADD CONSTRAINT "footer_quick_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_resource_links"
    ADD CONSTRAINT "footer_resource_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_social_links"
    ADD CONSTRAINT "footer_social_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."footer_solution_links"
    ADD CONSTRAINT "footer_solution_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."form_submissions"
    ADD CONSTRAINT "form_submissions_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."form_submissions_submission_data"
    ADD CONSTRAINT "form_submissions_submission_data_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_checkbox"
    ADD CONSTRAINT "forms_blocks_checkbox_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_country"
    ADD CONSTRAINT "forms_blocks_country_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_email"
    ADD CONSTRAINT "forms_blocks_email_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_message"
    ADD CONSTRAINT "forms_blocks_message_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_number"
    ADD CONSTRAINT "forms_blocks_number_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_select_options"
    ADD CONSTRAINT "forms_blocks_select_options_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_select"
    ADD CONSTRAINT "forms_blocks_select_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_state"
    ADD CONSTRAINT "forms_blocks_state_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_text"
    ADD CONSTRAINT "forms_blocks_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_blocks_textarea"
    ADD CONSTRAINT "forms_blocks_textarea_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms_emails"
    ADD CONSTRAINT "forms_emails_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."forms"
    ADD CONSTRAINT "forms_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header_nav_items"
    ADD CONSTRAINT "header_nav_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header"
    ADD CONSTRAINT "header_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."media"
    ADD CONSTRAINT "media_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_archive"
    ADD CONSTRAINT "pages_blocks_archive_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_background_reading"
    ADD CONSTRAINT "pages_blocks_background_reading_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_background_reading_quotes"
    ADD CONSTRAINT "pages_blocks_background_reading_quotes_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_content_columns"
    ADD CONSTRAINT "pages_blocks_content_columns_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_content"
    ADD CONSTRAINT "pages_blocks_content_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_cta_links"
    ADD CONSTRAINT "pages_blocks_cta_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_cta"
    ADD CONSTRAINT "pages_blocks_cta_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_faq_faq_items"
    ADD CONSTRAINT "pages_blocks_faq_faq_items_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_faq"
    ADD CONSTRAINT "pages_blocks_faq_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_features_features"
    ADD CONSTRAINT "pages_blocks_features_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_features"
    ADD CONSTRAINT "pages_blocks_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_form_block"
    ADD CONSTRAINT "pages_blocks_form_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_highlighted_text"
    ADD CONSTRAINT "pages_blocks_highlighted_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_image_text"
    ADD CONSTRAINT "pages_blocks_image_text_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_media_block"
    ADD CONSTRAINT "pages_blocks_media_block_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_newsletter_signup"
    ADD CONSTRAINT "pages_blocks_newsletter_signup_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_pricing"
    ADD CONSTRAINT "pages_blocks_pricing_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_pricing_pricing_options_features"
    ADD CONSTRAINT "pages_blocks_pricing_pricing_options_features_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_pricing_pricing_options"
    ADD CONSTRAINT "pages_blocks_pricing_pricing_options_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_social_proof_logos"
    ADD CONSTRAINT "pages_blocks_social_proof_logos_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_social_proof"
    ADD CONSTRAINT "pages_blocks_social_proof_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_team"
    ADD CONSTRAINT "pages_blocks_team_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_blocks_team_team_members"
    ADD CONSTRAINT "pages_blocks_team_team_members_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_hero_links"
    ADD CONSTRAINT "pages_hero_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."pages_rels"
    ADD CONSTRAINT "pages_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_jobs_log"
    ADD CONSTRAINT "payload_jobs_log_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_jobs"
    ADD CONSTRAINT "payload_jobs_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_locked_documents"
    ADD CONSTRAINT "payload_locked_documents_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_migrations"
    ADD CONSTRAINT "payload_migrations_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_preferences"
    ADD CONSTRAINT "payload_preferences_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."posts"
    ADD CONSTRAINT "posts_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."posts_populated_authors"
    ADD CONSTRAINT "posts_populated_authors_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."posts_populated_authors_social_links"
    ADD CONSTRAINT "posts_populated_authors_social_links_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."posts_rels"
    ADD CONSTRAINT "posts_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."redirects"
    ADD CONSTRAINT "redirects_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."redirects_rels"
    ADD CONSTRAINT "redirects_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."search_categories"
    ADD CONSTRAINT "search_categories_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."search"
    ADD CONSTRAINT "search_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."search_rels"
    ADD CONSTRAINT "search_rels_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users"
    ADD CONSTRAINT "users_pkey" PRIMARY KEY ("id");



ALTER TABLE ONLY "public"."users_social_links"
    ADD CONSTRAINT "users_social_links_pkey" PRIMARY KEY ("id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_order_idx" ON "public"."_company_reports_v_blocks_cr_analysis" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_analysis" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_path_idx" ON "public"."_company_reports_v_blocks_cr_analysis" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_domains_orde" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_domains_pare" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_names_order_" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_names_parent" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_order_idx" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_authors_parent_id_id" ON "public"."_company_reports_v_blocks_cr_analysis_refs_authors" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_order_idx" ON "public"."_company_reports_v_blocks_cr_analysis_refs" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_pages_order_idx" ON "public"."_company_reports_v_blocks_cr_analysis_refs_pages" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_pages_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_analysis_refs_pages" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_analysis_refs_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_analysis_refs" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_cta_order_idx" ON "public"."_company_reports_v_blocks_cr_cta" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_cta_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_cta" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_cta_path_idx" ON "public"."_company_reports_v_blocks_cr_cta" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_cr_graph_order_idx" ON "public"."_company_reports_v_blocks_cr_graph" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_graph_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_graph" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_graph_path_idx" ON "public"."_company_reports_v_blocks_cr_graph" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_cr_overview_graphs_order_idx" ON "public"."_company_reports_v_blocks_cr_overview_graphs" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_overview_graphs_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_overview_graphs" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_overview_graphs_path_idx" ON "public"."_company_reports_v_blocks_cr_overview_graphs" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_cr_profile_company_data_domains_order" ON "public"."_company_reports_v_blocks_cr_profile_company_data_domains" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_profile_company_data_domains_paren" ON "public"."_company_reports_v_blocks_cr_profile_company_data_domains" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_profile_company_data_names_order_i" ON "public"."_company_reports_v_blocks_cr_profile_company_data_names" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_profile_company_data_names_parent_" ON "public"."_company_reports_v_blocks_cr_profile_company_data_names" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_profile_order_idx" ON "public"."_company_reports_v_blocks_cr_profile" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_cr_profile_parent_id_idx" ON "public"."_company_reports_v_blocks_cr_profile" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_cr_profile_path_idx" ON "public"."_company_reports_v_blocks_cr_profile" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_form_block_background_media_idx" ON "public"."_company_reports_v_blocks_form_block" USING "btree" ("background_media_id");



CREATE INDEX "_company_reports_v_blocks_form_block_form_idx" ON "public"."_company_reports_v_blocks_form_block" USING "btree" ("form_id");



CREATE INDEX "_company_reports_v_blocks_form_block_order_idx" ON "public"."_company_reports_v_blocks_form_block" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_form_block_parent_id_idx" ON "public"."_company_reports_v_blocks_form_block" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_form_block_path_idx" ON "public"."_company_reports_v_blocks_form_block" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_blocks_highlighted_text_order_idx" ON "public"."_company_reports_v_blocks_highlighted_text" USING "btree" ("_order");



CREATE INDEX "_company_reports_v_blocks_highlighted_text_parent_id_idx" ON "public"."_company_reports_v_blocks_highlighted_text" USING "btree" ("_parent_id");



CREATE INDEX "_company_reports_v_blocks_highlighted_text_path_idx" ON "public"."_company_reports_v_blocks_highlighted_text" USING "btree" ("_path");



CREATE INDEX "_company_reports_v_created_at_idx" ON "public"."_company_reports_v" USING "btree" ("created_at");



CREATE INDEX "_company_reports_v_latest_idx" ON "public"."_company_reports_v" USING "btree" ("latest");



CREATE INDEX "_company_reports_v_parent_idx" ON "public"."_company_reports_v" USING "btree" ("parent_id");



CREATE INDEX "_company_reports_v_updated_at_idx" ON "public"."_company_reports_v" USING "btree" ("updated_at");



CREATE INDEX "_company_reports_v_version_version__status_idx" ON "public"."_company_reports_v" USING "btree" ("version__status");



CREATE INDEX "_company_reports_v_version_version_created_at_idx" ON "public"."_company_reports_v" USING "btree" ("version_created_at");



CREATE INDEX "_company_reports_v_version_version_slug_idx" ON "public"."_company_reports_v" USING "btree" ("version_slug");



CREATE INDEX "_company_reports_v_version_version_updated_at_idx" ON "public"."_company_reports_v" USING "btree" ("version_updated_at");



CREATE INDEX "_pages_v_autosave_idx" ON "public"."_pages_v" USING "btree" ("autosave");



CREATE INDEX "_pages_v_blocks_archive_order_idx" ON "public"."_pages_v_blocks_archive" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_archive_parent_id_idx" ON "public"."_pages_v_blocks_archive" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_archive_path_idx" ON "public"."_pages_v_blocks_archive" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_background_reading_order_idx" ON "public"."_pages_v_blocks_background_reading" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_background_reading_parent_id_idx" ON "public"."_pages_v_blocks_background_reading" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_background_reading_path_idx" ON "public"."_pages_v_blocks_background_reading" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_background_reading_quotes_image_idx" ON "public"."_pages_v_blocks_background_reading_quotes" USING "btree" ("image_id");



CREATE INDEX "_pages_v_blocks_background_reading_quotes_order_idx" ON "public"."_pages_v_blocks_background_reading_quotes" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_background_reading_quotes_parent_id_idx" ON "public"."_pages_v_blocks_background_reading_quotes" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_content_columns_order_idx" ON "public"."_pages_v_blocks_content_columns" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_content_columns_parent_id_idx" ON "public"."_pages_v_blocks_content_columns" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_content_order_idx" ON "public"."_pages_v_blocks_content" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_content_parent_id_idx" ON "public"."_pages_v_blocks_content" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_content_path_idx" ON "public"."_pages_v_blocks_content" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_cta_links_order_idx" ON "public"."_pages_v_blocks_cta_links" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_cta_links_parent_id_idx" ON "public"."_pages_v_blocks_cta_links" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_cta_order_idx" ON "public"."_pages_v_blocks_cta" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_cta_parent_id_idx" ON "public"."_pages_v_blocks_cta" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_cta_path_idx" ON "public"."_pages_v_blocks_cta" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_faq_background_media_idx" ON "public"."_pages_v_blocks_faq" USING "btree" ("background_media_id");



CREATE INDEX "_pages_v_blocks_faq_faq_items_order_idx" ON "public"."_pages_v_blocks_faq_faq_items" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_faq_faq_items_parent_id_idx" ON "public"."_pages_v_blocks_faq_faq_items" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_faq_order_idx" ON "public"."_pages_v_blocks_faq" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_faq_parent_id_idx" ON "public"."_pages_v_blocks_faq" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_faq_path_idx" ON "public"."_pages_v_blocks_faq" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_features_background_media_idx" ON "public"."_pages_v_blocks_features" USING "btree" ("background_media_id");



CREATE INDEX "_pages_v_blocks_features_features_icon_idx" ON "public"."_pages_v_blocks_features_features" USING "btree" ("icon_id");



CREATE INDEX "_pages_v_blocks_features_features_order_idx" ON "public"."_pages_v_blocks_features_features" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_features_features_parent_id_idx" ON "public"."_pages_v_blocks_features_features" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_features_order_idx" ON "public"."_pages_v_blocks_features" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_features_parent_id_idx" ON "public"."_pages_v_blocks_features" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_features_path_idx" ON "public"."_pages_v_blocks_features" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_form_block_background_media_idx" ON "public"."_pages_v_blocks_form_block" USING "btree" ("background_media_id");



CREATE INDEX "_pages_v_blocks_form_block_form_idx" ON "public"."_pages_v_blocks_form_block" USING "btree" ("form_id");



CREATE INDEX "_pages_v_blocks_form_block_order_idx" ON "public"."_pages_v_blocks_form_block" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_form_block_parent_id_idx" ON "public"."_pages_v_blocks_form_block" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_form_block_path_idx" ON "public"."_pages_v_blocks_form_block" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_highlighted_text_order_idx" ON "public"."_pages_v_blocks_highlighted_text" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_highlighted_text_parent_id_idx" ON "public"."_pages_v_blocks_highlighted_text" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_highlighted_text_path_idx" ON "public"."_pages_v_blocks_highlighted_text" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_image_text_media_idx" ON "public"."_pages_v_blocks_image_text" USING "btree" ("media_id");



CREATE INDEX "_pages_v_blocks_image_text_order_idx" ON "public"."_pages_v_blocks_image_text" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_image_text_parent_id_idx" ON "public"."_pages_v_blocks_image_text" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_image_text_path_idx" ON "public"."_pages_v_blocks_image_text" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_image_text_se_se_background_media_idx" ON "public"."_pages_v_blocks_image_text" USING "btree" ("se_background_media_id");



CREATE INDEX "_pages_v_blocks_media_block_media_idx" ON "public"."_pages_v_blocks_media_block" USING "btree" ("media_id");



CREATE INDEX "_pages_v_blocks_media_block_order_idx" ON "public"."_pages_v_blocks_media_block" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_media_block_parent_id_idx" ON "public"."_pages_v_blocks_media_block" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_media_block_path_idx" ON "public"."_pages_v_blocks_media_block" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_newsletter_signup_background_idx" ON "public"."_pages_v_blocks_newsletter_signup" USING "btree" ("background_id");



CREATE INDEX "_pages_v_blocks_newsletter_signup_order_idx" ON "public"."_pages_v_blocks_newsletter_signup" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_newsletter_signup_parent_id_idx" ON "public"."_pages_v_blocks_newsletter_signup" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_newsletter_signup_path_idx" ON "public"."_pages_v_blocks_newsletter_signup" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_pricing_order_idx" ON "public"."_pages_v_blocks_pricing" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_pricing_parent_id_idx" ON "public"."_pages_v_blocks_pricing" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_pricing_path_idx" ON "public"."_pages_v_blocks_pricing" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_pricing_pricing_options_features_order_idx" ON "public"."_pages_v_blocks_pricing_pricing_options_features" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_pricing_pricing_options_features_parent_id_idx" ON "public"."_pages_v_blocks_pricing_pricing_options_features" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_pricing_pricing_options_order_idx" ON "public"."_pages_v_blocks_pricing_pricing_options" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_pricing_pricing_options_parent_id_idx" ON "public"."_pages_v_blocks_pricing_pricing_options" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_social_proof_background_media_idx" ON "public"."_pages_v_blocks_social_proof" USING "btree" ("background_media_id");



CREATE INDEX "_pages_v_blocks_social_proof_logos_logo_idx" ON "public"."_pages_v_blocks_social_proof_logos" USING "btree" ("logo_id");



CREATE INDEX "_pages_v_blocks_social_proof_logos_order_idx" ON "public"."_pages_v_blocks_social_proof_logos" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_social_proof_logos_parent_id_idx" ON "public"."_pages_v_blocks_social_proof_logos" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_social_proof_order_idx" ON "public"."_pages_v_blocks_social_proof" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_social_proof_parent_id_idx" ON "public"."_pages_v_blocks_social_proof" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_social_proof_path_idx" ON "public"."_pages_v_blocks_social_proof" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_team_order_idx" ON "public"."_pages_v_blocks_team" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_team_parent_id_idx" ON "public"."_pages_v_blocks_team" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_blocks_team_path_idx" ON "public"."_pages_v_blocks_team" USING "btree" ("_path");



CREATE INDEX "_pages_v_blocks_team_team_members_media_idx" ON "public"."_pages_v_blocks_team_team_members" USING "btree" ("media_id");



CREATE INDEX "_pages_v_blocks_team_team_members_order_idx" ON "public"."_pages_v_blocks_team_team_members" USING "btree" ("_order");



CREATE INDEX "_pages_v_blocks_team_team_members_parent_id_idx" ON "public"."_pages_v_blocks_team_team_members" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_created_at_idx" ON "public"."_pages_v" USING "btree" ("created_at");



CREATE INDEX "_pages_v_latest_idx" ON "public"."_pages_v" USING "btree" ("latest");



CREATE INDEX "_pages_v_parent_idx" ON "public"."_pages_v" USING "btree" ("parent_id");



CREATE INDEX "_pages_v_rels_categories_id_idx" ON "public"."_pages_v_rels" USING "btree" ("categories_id");



CREATE INDEX "_pages_v_rels_order_idx" ON "public"."_pages_v_rels" USING "btree" ("order");



CREATE INDEX "_pages_v_rels_pages_id_idx" ON "public"."_pages_v_rels" USING "btree" ("pages_id");



CREATE INDEX "_pages_v_rels_parent_idx" ON "public"."_pages_v_rels" USING "btree" ("parent_id");



CREATE INDEX "_pages_v_rels_path_idx" ON "public"."_pages_v_rels" USING "btree" ("path");



CREATE INDEX "_pages_v_rels_posts_id_idx" ON "public"."_pages_v_rels" USING "btree" ("posts_id");



CREATE INDEX "_pages_v_updated_at_idx" ON "public"."_pages_v" USING "btree" ("updated_at");



CREATE INDEX "_pages_v_version_hero_links_order_idx" ON "public"."_pages_v_version_hero_links" USING "btree" ("_order");



CREATE INDEX "_pages_v_version_hero_links_parent_id_idx" ON "public"."_pages_v_version_hero_links" USING "btree" ("_parent_id");



CREATE INDEX "_pages_v_version_hero_version_hero_media_idx" ON "public"."_pages_v" USING "btree" ("version_hero_media_id");



CREATE INDEX "_pages_v_version_meta_version_meta_image_idx" ON "public"."_pages_v" USING "btree" ("version_meta_image_id");



CREATE INDEX "_pages_v_version_version__status_idx" ON "public"."_pages_v" USING "btree" ("version__status");



CREATE INDEX "_pages_v_version_version_created_at_idx" ON "public"."_pages_v" USING "btree" ("version_created_at");



CREATE INDEX "_pages_v_version_version_slug_idx" ON "public"."_pages_v" USING "btree" ("version_slug");



CREATE INDEX "_pages_v_version_version_updated_at_idx" ON "public"."_pages_v" USING "btree" ("version_updated_at");



CREATE INDEX "_posts_v_autosave_idx" ON "public"."_posts_v" USING "btree" ("autosave");



CREATE INDEX "_posts_v_created_at_idx" ON "public"."_posts_v" USING "btree" ("created_at");



CREATE INDEX "_posts_v_latest_idx" ON "public"."_posts_v" USING "btree" ("latest");



CREATE INDEX "_posts_v_parent_idx" ON "public"."_posts_v" USING "btree" ("parent_id");



CREATE INDEX "_posts_v_rels_categories_id_idx" ON "public"."_posts_v_rels" USING "btree" ("categories_id");



CREATE INDEX "_posts_v_rels_order_idx" ON "public"."_posts_v_rels" USING "btree" ("order");



CREATE INDEX "_posts_v_rels_parent_idx" ON "public"."_posts_v_rels" USING "btree" ("parent_id");



CREATE INDEX "_posts_v_rels_path_idx" ON "public"."_posts_v_rels" USING "btree" ("path");



CREATE INDEX "_posts_v_rels_posts_id_idx" ON "public"."_posts_v_rels" USING "btree" ("posts_id");



CREATE INDEX "_posts_v_rels_users_id_idx" ON "public"."_posts_v_rels" USING "btree" ("users_id");



CREATE INDEX "_posts_v_updated_at_idx" ON "public"."_posts_v" USING "btree" ("updated_at");



CREATE INDEX "_posts_v_version_meta_version_meta_image_idx" ON "public"."_posts_v" USING "btree" ("version_meta_image_id");



CREATE INDEX "_posts_v_version_populated_authors_order_idx" ON "public"."_posts_v_version_populated_authors" USING "btree" ("_order");



CREATE INDEX "_posts_v_version_populated_authors_parent_id_idx" ON "public"."_posts_v_version_populated_authors" USING "btree" ("_parent_id");



CREATE INDEX "_posts_v_version_populated_authors_social_links_order_idx" ON "public"."_posts_v_version_populated_authors_social_links" USING "btree" ("_order");



CREATE INDEX "_posts_v_version_populated_authors_social_links_parent_id_idx" ON "public"."_posts_v_version_populated_authors_social_links" USING "btree" ("_parent_id");



CREATE INDEX "_posts_v_version_version__status_idx" ON "public"."_posts_v" USING "btree" ("version__status");



CREATE INDEX "_posts_v_version_version_created_at_idx" ON "public"."_posts_v" USING "btree" ("version_created_at");



CREATE INDEX "_posts_v_version_version_hero_image_idx" ON "public"."_posts_v" USING "btree" ("version_hero_image_id");



CREATE INDEX "_posts_v_version_version_slug_idx" ON "public"."_posts_v" USING "btree" ("version_slug");



CREATE INDEX "_posts_v_version_version_updated_at_idx" ON "public"."_posts_v" USING "btree" ("version_updated_at");



CREATE INDEX "categories_breadcrumbs_doc_idx" ON "public"."categories_breadcrumbs" USING "btree" ("doc_id");



CREATE INDEX "categories_breadcrumbs_order_idx" ON "public"."categories_breadcrumbs" USING "btree" ("_order");



CREATE INDEX "categories_breadcrumbs_parent_id_idx" ON "public"."categories_breadcrumbs" USING "btree" ("_parent_id");



CREATE INDEX "categories_created_at_idx" ON "public"."categories" USING "btree" ("created_at");



CREATE INDEX "categories_parent_idx" ON "public"."categories" USING "btree" ("parent_id");



CREATE INDEX "categories_slug_idx" ON "public"."categories" USING "btree" ("slug");



CREATE INDEX "categories_updated_at_idx" ON "public"."categories" USING "btree" ("updated_at");



CREATE INDEX "company_reports__status_idx" ON "public"."company_reports" USING "btree" ("_status");



CREATE INDEX "company_reports_blocks_cr_analysis_order_idx" ON "public"."company_reports_blocks_cr_analysis" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_parent_id_idx" ON "public"."company_reports_blocks_cr_analysis" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_analysis_path_idx" ON "public"."company_reports_blocks_cr_analysis" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_domains_order_i" ON "public"."company_reports_blocks_cr_analysis_refs_authors_domains" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_domains_parent_" ON "public"."company_reports_blocks_cr_analysis_refs_authors_domains" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_names_order_idx" ON "public"."company_reports_blocks_cr_analysis_refs_authors_names" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_names_parent_id" ON "public"."company_reports_blocks_cr_analysis_refs_authors_names" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_order_idx" ON "public"."company_reports_blocks_cr_analysis_refs_authors" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_authors_parent_id_idx" ON "public"."company_reports_blocks_cr_analysis_refs_authors" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_order_idx" ON "public"."company_reports_blocks_cr_analysis_refs" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_pages_order_idx" ON "public"."company_reports_blocks_cr_analysis_refs_pages" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_pages_parent_id_idx" ON "public"."company_reports_blocks_cr_analysis_refs_pages" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_analysis_refs_parent_id_idx" ON "public"."company_reports_blocks_cr_analysis_refs" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_cta_order_idx" ON "public"."company_reports_blocks_cr_cta" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_cta_parent_id_idx" ON "public"."company_reports_blocks_cr_cta" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_cta_path_idx" ON "public"."company_reports_blocks_cr_cta" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_cr_graph_order_idx" ON "public"."company_reports_blocks_cr_graph" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_graph_parent_id_idx" ON "public"."company_reports_blocks_cr_graph" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_graph_path_idx" ON "public"."company_reports_blocks_cr_graph" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_cr_overview_graphs_order_idx" ON "public"."company_reports_blocks_cr_overview_graphs" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_overview_graphs_parent_id_idx" ON "public"."company_reports_blocks_cr_overview_graphs" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_overview_graphs_path_idx" ON "public"."company_reports_blocks_cr_overview_graphs" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_cr_profile_company_data_domains_order_id" ON "public"."company_reports_blocks_cr_profile_company_data_domains" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_profile_company_data_domains_parent_i" ON "public"."company_reports_blocks_cr_profile_company_data_domains" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_profile_company_data_names_order_idx" ON "public"."company_reports_blocks_cr_profile_company_data_names" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_profile_company_data_names_parent_id_" ON "public"."company_reports_blocks_cr_profile_company_data_names" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_profile_order_idx" ON "public"."company_reports_blocks_cr_profile" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_cr_profile_parent_id_idx" ON "public"."company_reports_blocks_cr_profile" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_cr_profile_path_idx" ON "public"."company_reports_blocks_cr_profile" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_form_block_background_media_idx" ON "public"."company_reports_blocks_form_block" USING "btree" ("background_media_id");



CREATE INDEX "company_reports_blocks_form_block_form_idx" ON "public"."company_reports_blocks_form_block" USING "btree" ("form_id");



CREATE INDEX "company_reports_blocks_form_block_order_idx" ON "public"."company_reports_blocks_form_block" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_form_block_parent_id_idx" ON "public"."company_reports_blocks_form_block" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_form_block_path_idx" ON "public"."company_reports_blocks_form_block" USING "btree" ("_path");



CREATE INDEX "company_reports_blocks_highlighted_text_order_idx" ON "public"."company_reports_blocks_highlighted_text" USING "btree" ("_order");



CREATE INDEX "company_reports_blocks_highlighted_text_parent_id_idx" ON "public"."company_reports_blocks_highlighted_text" USING "btree" ("_parent_id");



CREATE INDEX "company_reports_blocks_highlighted_text_path_idx" ON "public"."company_reports_blocks_highlighted_text" USING "btree" ("_path");



CREATE INDEX "company_reports_created_at_idx" ON "public"."company_reports" USING "btree" ("created_at");



CREATE UNIQUE INDEX "company_reports_slug_idx" ON "public"."company_reports" USING "btree" ("slug");



CREATE INDEX "company_reports_updated_at_idx" ON "public"."company_reports" USING "btree" ("updated_at");



CREATE INDEX "footer_company_links_order_idx" ON "public"."footer_company_links" USING "btree" ("_order");



CREATE INDEX "footer_company_links_parent_id_idx" ON "public"."footer_company_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_quick_links_order_idx" ON "public"."footer_quick_links" USING "btree" ("_order");



CREATE INDEX "footer_quick_links_parent_id_idx" ON "public"."footer_quick_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_rels_order_idx" ON "public"."footer_rels" USING "btree" ("order");



CREATE INDEX "footer_rels_pages_id_idx" ON "public"."footer_rels" USING "btree" ("pages_id");



CREATE INDEX "footer_rels_parent_idx" ON "public"."footer_rels" USING "btree" ("parent_id");



CREATE INDEX "footer_rels_path_idx" ON "public"."footer_rels" USING "btree" ("path");



CREATE INDEX "footer_rels_posts_id_idx" ON "public"."footer_rels" USING "btree" ("posts_id");



CREATE INDEX "footer_resource_links_order_idx" ON "public"."footer_resource_links" USING "btree" ("_order");



CREATE INDEX "footer_resource_links_parent_id_idx" ON "public"."footer_resource_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_social_links_order_idx" ON "public"."footer_social_links" USING "btree" ("_order");



CREATE INDEX "footer_social_links_parent_id_idx" ON "public"."footer_social_links" USING "btree" ("_parent_id");



CREATE INDEX "footer_solution_links_order_idx" ON "public"."footer_solution_links" USING "btree" ("_order");



CREATE INDEX "footer_solution_links_parent_id_idx" ON "public"."footer_solution_links" USING "btree" ("_parent_id");



CREATE INDEX "form_submissions_created_at_idx" ON "public"."form_submissions" USING "btree" ("created_at");



CREATE INDEX "form_submissions_form_idx" ON "public"."form_submissions" USING "btree" ("form_id");



CREATE INDEX "form_submissions_submission_data_order_idx" ON "public"."form_submissions_submission_data" USING "btree" ("_order");



CREATE INDEX "form_submissions_submission_data_parent_id_idx" ON "public"."form_submissions_submission_data" USING "btree" ("_parent_id");



CREATE INDEX "form_submissions_updated_at_idx" ON "public"."form_submissions" USING "btree" ("updated_at");



CREATE INDEX "forms_blocks_checkbox_order_idx" ON "public"."forms_blocks_checkbox" USING "btree" ("_order");



CREATE INDEX "forms_blocks_checkbox_parent_id_idx" ON "public"."forms_blocks_checkbox" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_checkbox_path_idx" ON "public"."forms_blocks_checkbox" USING "btree" ("_path");



CREATE INDEX "forms_blocks_country_order_idx" ON "public"."forms_blocks_country" USING "btree" ("_order");



CREATE INDEX "forms_blocks_country_parent_id_idx" ON "public"."forms_blocks_country" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_country_path_idx" ON "public"."forms_blocks_country" USING "btree" ("_path");



CREATE INDEX "forms_blocks_email_order_idx" ON "public"."forms_blocks_email" USING "btree" ("_order");



CREATE INDEX "forms_blocks_email_parent_id_idx" ON "public"."forms_blocks_email" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_email_path_idx" ON "public"."forms_blocks_email" USING "btree" ("_path");



CREATE INDEX "forms_blocks_message_order_idx" ON "public"."forms_blocks_message" USING "btree" ("_order");



CREATE INDEX "forms_blocks_message_parent_id_idx" ON "public"."forms_blocks_message" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_message_path_idx" ON "public"."forms_blocks_message" USING "btree" ("_path");



CREATE INDEX "forms_blocks_number_order_idx" ON "public"."forms_blocks_number" USING "btree" ("_order");



CREATE INDEX "forms_blocks_number_parent_id_idx" ON "public"."forms_blocks_number" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_number_path_idx" ON "public"."forms_blocks_number" USING "btree" ("_path");



CREATE INDEX "forms_blocks_select_options_order_idx" ON "public"."forms_blocks_select_options" USING "btree" ("_order");



CREATE INDEX "forms_blocks_select_options_parent_id_idx" ON "public"."forms_blocks_select_options" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_select_order_idx" ON "public"."forms_blocks_select" USING "btree" ("_order");



CREATE INDEX "forms_blocks_select_parent_id_idx" ON "public"."forms_blocks_select" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_select_path_idx" ON "public"."forms_blocks_select" USING "btree" ("_path");



CREATE INDEX "forms_blocks_state_order_idx" ON "public"."forms_blocks_state" USING "btree" ("_order");



CREATE INDEX "forms_blocks_state_parent_id_idx" ON "public"."forms_blocks_state" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_state_path_idx" ON "public"."forms_blocks_state" USING "btree" ("_path");



CREATE INDEX "forms_blocks_text_order_idx" ON "public"."forms_blocks_text" USING "btree" ("_order");



CREATE INDEX "forms_blocks_text_parent_id_idx" ON "public"."forms_blocks_text" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_text_path_idx" ON "public"."forms_blocks_text" USING "btree" ("_path");



CREATE INDEX "forms_blocks_textarea_order_idx" ON "public"."forms_blocks_textarea" USING "btree" ("_order");



CREATE INDEX "forms_blocks_textarea_parent_id_idx" ON "public"."forms_blocks_textarea" USING "btree" ("_parent_id");



CREATE INDEX "forms_blocks_textarea_path_idx" ON "public"."forms_blocks_textarea" USING "btree" ("_path");



CREATE INDEX "forms_created_at_idx" ON "public"."forms" USING "btree" ("created_at");



CREATE INDEX "forms_emails_order_idx" ON "public"."forms_emails" USING "btree" ("_order");



CREATE INDEX "forms_emails_parent_id_idx" ON "public"."forms_emails" USING "btree" ("_parent_id");



CREATE INDEX "forms_updated_at_idx" ON "public"."forms" USING "btree" ("updated_at");



CREATE INDEX "header_nav_items_order_idx" ON "public"."header_nav_items" USING "btree" ("_order");



CREATE INDEX "header_nav_items_parent_id_idx" ON "public"."header_nav_items" USING "btree" ("_parent_id");



CREATE INDEX "header_rels_order_idx" ON "public"."header_rels" USING "btree" ("order");



CREATE INDEX "header_rels_pages_id_idx" ON "public"."header_rels" USING "btree" ("pages_id");



CREATE INDEX "header_rels_parent_idx" ON "public"."header_rels" USING "btree" ("parent_id");



CREATE INDEX "header_rels_path_idx" ON "public"."header_rels" USING "btree" ("path");



CREATE INDEX "header_rels_posts_id_idx" ON "public"."header_rels" USING "btree" ("posts_id");



CREATE INDEX "media_created_at_idx" ON "public"."media" USING "btree" ("created_at");



CREATE UNIQUE INDEX "media_filename_idx" ON "public"."media" USING "btree" ("filename");



CREATE INDEX "media_sizes_large_sizes_large_filename_idx" ON "public"."media" USING "btree" ("sizes_large_filename");



CREATE INDEX "media_sizes_medium_sizes_medium_filename_idx" ON "public"."media" USING "btree" ("sizes_medium_filename");



CREATE INDEX "media_sizes_og_sizes_og_filename_idx" ON "public"."media" USING "btree" ("sizes_og_filename");



CREATE INDEX "media_sizes_small_sizes_small_filename_idx" ON "public"."media" USING "btree" ("sizes_small_filename");



CREATE INDEX "media_sizes_square_sizes_square_filename_idx" ON "public"."media" USING "btree" ("sizes_square_filename");



CREATE INDEX "media_sizes_thumbnail_sizes_thumbnail_filename_idx" ON "public"."media" USING "btree" ("sizes_thumbnail_filename");



CREATE INDEX "media_sizes_xlarge_sizes_xlarge_filename_idx" ON "public"."media" USING "btree" ("sizes_xlarge_filename");



CREATE INDEX "media_updated_at_idx" ON "public"."media" USING "btree" ("updated_at");



CREATE INDEX "pages__status_idx" ON "public"."pages" USING "btree" ("_status");



CREATE INDEX "pages_blocks_archive_order_idx" ON "public"."pages_blocks_archive" USING "btree" ("_order");



CREATE INDEX "pages_blocks_archive_parent_id_idx" ON "public"."pages_blocks_archive" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_archive_path_idx" ON "public"."pages_blocks_archive" USING "btree" ("_path");



CREATE INDEX "pages_blocks_background_reading_order_idx" ON "public"."pages_blocks_background_reading" USING "btree" ("_order");



CREATE INDEX "pages_blocks_background_reading_parent_id_idx" ON "public"."pages_blocks_background_reading" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_background_reading_path_idx" ON "public"."pages_blocks_background_reading" USING "btree" ("_path");



CREATE INDEX "pages_blocks_background_reading_quotes_image_idx" ON "public"."pages_blocks_background_reading_quotes" USING "btree" ("image_id");



CREATE INDEX "pages_blocks_background_reading_quotes_order_idx" ON "public"."pages_blocks_background_reading_quotes" USING "btree" ("_order");



CREATE INDEX "pages_blocks_background_reading_quotes_parent_id_idx" ON "public"."pages_blocks_background_reading_quotes" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_content_columns_order_idx" ON "public"."pages_blocks_content_columns" USING "btree" ("_order");



CREATE INDEX "pages_blocks_content_columns_parent_id_idx" ON "public"."pages_blocks_content_columns" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_content_order_idx" ON "public"."pages_blocks_content" USING "btree" ("_order");



CREATE INDEX "pages_blocks_content_parent_id_idx" ON "public"."pages_blocks_content" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_content_path_idx" ON "public"."pages_blocks_content" USING "btree" ("_path");



CREATE INDEX "pages_blocks_cta_links_order_idx" ON "public"."pages_blocks_cta_links" USING "btree" ("_order");



CREATE INDEX "pages_blocks_cta_links_parent_id_idx" ON "public"."pages_blocks_cta_links" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_cta_order_idx" ON "public"."pages_blocks_cta" USING "btree" ("_order");



CREATE INDEX "pages_blocks_cta_parent_id_idx" ON "public"."pages_blocks_cta" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_cta_path_idx" ON "public"."pages_blocks_cta" USING "btree" ("_path");



CREATE INDEX "pages_blocks_faq_background_media_idx" ON "public"."pages_blocks_faq" USING "btree" ("background_media_id");



CREATE INDEX "pages_blocks_faq_faq_items_order_idx" ON "public"."pages_blocks_faq_faq_items" USING "btree" ("_order");



CREATE INDEX "pages_blocks_faq_faq_items_parent_id_idx" ON "public"."pages_blocks_faq_faq_items" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_faq_order_idx" ON "public"."pages_blocks_faq" USING "btree" ("_order");



CREATE INDEX "pages_blocks_faq_parent_id_idx" ON "public"."pages_blocks_faq" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_faq_path_idx" ON "public"."pages_blocks_faq" USING "btree" ("_path");



CREATE INDEX "pages_blocks_features_background_media_idx" ON "public"."pages_blocks_features" USING "btree" ("background_media_id");



CREATE INDEX "pages_blocks_features_features_icon_idx" ON "public"."pages_blocks_features_features" USING "btree" ("icon_id");



CREATE INDEX "pages_blocks_features_features_order_idx" ON "public"."pages_blocks_features_features" USING "btree" ("_order");



CREATE INDEX "pages_blocks_features_features_parent_id_idx" ON "public"."pages_blocks_features_features" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_features_order_idx" ON "public"."pages_blocks_features" USING "btree" ("_order");



CREATE INDEX "pages_blocks_features_parent_id_idx" ON "public"."pages_blocks_features" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_features_path_idx" ON "public"."pages_blocks_features" USING "btree" ("_path");



CREATE INDEX "pages_blocks_form_block_background_media_idx" ON "public"."pages_blocks_form_block" USING "btree" ("background_media_id");



CREATE INDEX "pages_blocks_form_block_form_idx" ON "public"."pages_blocks_form_block" USING "btree" ("form_id");



CREATE INDEX "pages_blocks_form_block_order_idx" ON "public"."pages_blocks_form_block" USING "btree" ("_order");



CREATE INDEX "pages_blocks_form_block_parent_id_idx" ON "public"."pages_blocks_form_block" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_form_block_path_idx" ON "public"."pages_blocks_form_block" USING "btree" ("_path");



CREATE INDEX "pages_blocks_highlighted_text_order_idx" ON "public"."pages_blocks_highlighted_text" USING "btree" ("_order");



CREATE INDEX "pages_blocks_highlighted_text_parent_id_idx" ON "public"."pages_blocks_highlighted_text" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_highlighted_text_path_idx" ON "public"."pages_blocks_highlighted_text" USING "btree" ("_path");



CREATE INDEX "pages_blocks_image_text_media_idx" ON "public"."pages_blocks_image_text" USING "btree" ("media_id");



CREATE INDEX "pages_blocks_image_text_order_idx" ON "public"."pages_blocks_image_text" USING "btree" ("_order");



CREATE INDEX "pages_blocks_image_text_parent_id_idx" ON "public"."pages_blocks_image_text" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_image_text_path_idx" ON "public"."pages_blocks_image_text" USING "btree" ("_path");



CREATE INDEX "pages_blocks_image_text_se_se_background_media_idx" ON "public"."pages_blocks_image_text" USING "btree" ("se_background_media_id");



CREATE INDEX "pages_blocks_media_block_media_idx" ON "public"."pages_blocks_media_block" USING "btree" ("media_id");



CREATE INDEX "pages_blocks_media_block_order_idx" ON "public"."pages_blocks_media_block" USING "btree" ("_order");



CREATE INDEX "pages_blocks_media_block_parent_id_idx" ON "public"."pages_blocks_media_block" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_media_block_path_idx" ON "public"."pages_blocks_media_block" USING "btree" ("_path");



CREATE INDEX "pages_blocks_newsletter_signup_background_idx" ON "public"."pages_blocks_newsletter_signup" USING "btree" ("background_id");



CREATE INDEX "pages_blocks_newsletter_signup_order_idx" ON "public"."pages_blocks_newsletter_signup" USING "btree" ("_order");



CREATE INDEX "pages_blocks_newsletter_signup_parent_id_idx" ON "public"."pages_blocks_newsletter_signup" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_newsletter_signup_path_idx" ON "public"."pages_blocks_newsletter_signup" USING "btree" ("_path");



CREATE INDEX "pages_blocks_pricing_order_idx" ON "public"."pages_blocks_pricing" USING "btree" ("_order");



CREATE INDEX "pages_blocks_pricing_parent_id_idx" ON "public"."pages_blocks_pricing" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_pricing_path_idx" ON "public"."pages_blocks_pricing" USING "btree" ("_path");



CREATE INDEX "pages_blocks_pricing_pricing_options_features_order_idx" ON "public"."pages_blocks_pricing_pricing_options_features" USING "btree" ("_order");



CREATE INDEX "pages_blocks_pricing_pricing_options_features_parent_id_idx" ON "public"."pages_blocks_pricing_pricing_options_features" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_pricing_pricing_options_order_idx" ON "public"."pages_blocks_pricing_pricing_options" USING "btree" ("_order");



CREATE INDEX "pages_blocks_pricing_pricing_options_parent_id_idx" ON "public"."pages_blocks_pricing_pricing_options" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_social_proof_background_media_idx" ON "public"."pages_blocks_social_proof" USING "btree" ("background_media_id");



CREATE INDEX "pages_blocks_social_proof_logos_logo_idx" ON "public"."pages_blocks_social_proof_logos" USING "btree" ("logo_id");



CREATE INDEX "pages_blocks_social_proof_logos_order_idx" ON "public"."pages_blocks_social_proof_logos" USING "btree" ("_order");



CREATE INDEX "pages_blocks_social_proof_logos_parent_id_idx" ON "public"."pages_blocks_social_proof_logos" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_social_proof_order_idx" ON "public"."pages_blocks_social_proof" USING "btree" ("_order");



CREATE INDEX "pages_blocks_social_proof_parent_id_idx" ON "public"."pages_blocks_social_proof" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_social_proof_path_idx" ON "public"."pages_blocks_social_proof" USING "btree" ("_path");



CREATE INDEX "pages_blocks_team_order_idx" ON "public"."pages_blocks_team" USING "btree" ("_order");



CREATE INDEX "pages_blocks_team_parent_id_idx" ON "public"."pages_blocks_team" USING "btree" ("_parent_id");



CREATE INDEX "pages_blocks_team_path_idx" ON "public"."pages_blocks_team" USING "btree" ("_path");



CREATE INDEX "pages_blocks_team_team_members_media_idx" ON "public"."pages_blocks_team_team_members" USING "btree" ("media_id");



CREATE INDEX "pages_blocks_team_team_members_order_idx" ON "public"."pages_blocks_team_team_members" USING "btree" ("_order");



CREATE INDEX "pages_blocks_team_team_members_parent_id_idx" ON "public"."pages_blocks_team_team_members" USING "btree" ("_parent_id");



CREATE INDEX "pages_created_at_idx" ON "public"."pages" USING "btree" ("created_at");



CREATE INDEX "pages_hero_hero_media_idx" ON "public"."pages" USING "btree" ("hero_media_id");



CREATE INDEX "pages_hero_links_order_idx" ON "public"."pages_hero_links" USING "btree" ("_order");



CREATE INDEX "pages_hero_links_parent_id_idx" ON "public"."pages_hero_links" USING "btree" ("_parent_id");



CREATE INDEX "pages_meta_meta_image_idx" ON "public"."pages" USING "btree" ("meta_image_id");



CREATE INDEX "pages_rels_categories_id_idx" ON "public"."pages_rels" USING "btree" ("categories_id");



CREATE INDEX "pages_rels_order_idx" ON "public"."pages_rels" USING "btree" ("order");



CREATE INDEX "pages_rels_pages_id_idx" ON "public"."pages_rels" USING "btree" ("pages_id");



CREATE INDEX "pages_rels_parent_idx" ON "public"."pages_rels" USING "btree" ("parent_id");



CREATE INDEX "pages_rels_path_idx" ON "public"."pages_rels" USING "btree" ("path");



CREATE INDEX "pages_rels_posts_id_idx" ON "public"."pages_rels" USING "btree" ("posts_id");



CREATE INDEX "pages_slug_idx" ON "public"."pages" USING "btree" ("slug");



CREATE INDEX "pages_updated_at_idx" ON "public"."pages" USING "btree" ("updated_at");



CREATE INDEX "payload_jobs_completed_at_idx" ON "public"."payload_jobs" USING "btree" ("completed_at");



CREATE INDEX "payload_jobs_created_at_idx" ON "public"."payload_jobs" USING "btree" ("created_at");



CREATE INDEX "payload_jobs_has_error_idx" ON "public"."payload_jobs" USING "btree" ("has_error");



CREATE INDEX "payload_jobs_log_order_idx" ON "public"."payload_jobs_log" USING "btree" ("_order");



CREATE INDEX "payload_jobs_log_parent_id_idx" ON "public"."payload_jobs_log" USING "btree" ("_parent_id");



CREATE INDEX "payload_jobs_processing_idx" ON "public"."payload_jobs" USING "btree" ("processing");



CREATE INDEX "payload_jobs_queue_idx" ON "public"."payload_jobs" USING "btree" ("queue");



CREATE INDEX "payload_jobs_task_slug_idx" ON "public"."payload_jobs" USING "btree" ("task_slug");



CREATE INDEX "payload_jobs_total_tried_idx" ON "public"."payload_jobs" USING "btree" ("total_tried");



CREATE INDEX "payload_jobs_updated_at_idx" ON "public"."payload_jobs" USING "btree" ("updated_at");



CREATE INDEX "payload_jobs_wait_until_idx" ON "public"."payload_jobs" USING "btree" ("wait_until");



CREATE INDEX "payload_locked_documents_created_at_idx" ON "public"."payload_locked_documents" USING "btree" ("created_at");



CREATE INDEX "payload_locked_documents_global_slug_idx" ON "public"."payload_locked_documents" USING "btree" ("global_slug");



CREATE INDEX "payload_locked_documents_rels_categories_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("categories_id");



CREATE INDEX "payload_locked_documents_rels_company_reports_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("company_reports_id");



CREATE INDEX "payload_locked_documents_rels_form_submissions_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("form_submissions_id");



CREATE INDEX "payload_locked_documents_rels_forms_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("forms_id");



CREATE INDEX "payload_locked_documents_rels_media_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("media_id");



CREATE INDEX "payload_locked_documents_rels_order_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("order");



CREATE INDEX "payload_locked_documents_rels_pages_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("pages_id");



CREATE INDEX "payload_locked_documents_rels_parent_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("parent_id");



CREATE INDEX "payload_locked_documents_rels_path_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("path");



CREATE INDEX "payload_locked_documents_rels_payload_jobs_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("payload_jobs_id");



CREATE INDEX "payload_locked_documents_rels_posts_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("posts_id");



CREATE INDEX "payload_locked_documents_rels_redirects_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("redirects_id");



CREATE INDEX "payload_locked_documents_rels_search_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("search_id");



CREATE INDEX "payload_locked_documents_rels_users_id_idx" ON "public"."payload_locked_documents_rels" USING "btree" ("users_id");



CREATE INDEX "payload_locked_documents_updated_at_idx" ON "public"."payload_locked_documents" USING "btree" ("updated_at");



CREATE INDEX "payload_migrations_created_at_idx" ON "public"."payload_migrations" USING "btree" ("created_at");



CREATE INDEX "payload_migrations_updated_at_idx" ON "public"."payload_migrations" USING "btree" ("updated_at");



CREATE INDEX "payload_preferences_created_at_idx" ON "public"."payload_preferences" USING "btree" ("created_at");



CREATE INDEX "payload_preferences_key_idx" ON "public"."payload_preferences" USING "btree" ("key");



CREATE INDEX "payload_preferences_rels_order_idx" ON "public"."payload_preferences_rels" USING "btree" ("order");



CREATE INDEX "payload_preferences_rels_parent_idx" ON "public"."payload_preferences_rels" USING "btree" ("parent_id");



CREATE INDEX "payload_preferences_rels_path_idx" ON "public"."payload_preferences_rels" USING "btree" ("path");



CREATE INDEX "payload_preferences_rels_users_id_idx" ON "public"."payload_preferences_rels" USING "btree" ("users_id");



CREATE INDEX "payload_preferences_updated_at_idx" ON "public"."payload_preferences" USING "btree" ("updated_at");



CREATE INDEX "posts__status_idx" ON "public"."posts" USING "btree" ("_status");



CREATE INDEX "posts_created_at_idx" ON "public"."posts" USING "btree" ("created_at");



CREATE INDEX "posts_hero_image_idx" ON "public"."posts" USING "btree" ("hero_image_id");



CREATE INDEX "posts_meta_meta_image_idx" ON "public"."posts" USING "btree" ("meta_image_id");



CREATE INDEX "posts_populated_authors_order_idx" ON "public"."posts_populated_authors" USING "btree" ("_order");



CREATE INDEX "posts_populated_authors_parent_id_idx" ON "public"."posts_populated_authors" USING "btree" ("_parent_id");



CREATE INDEX "posts_populated_authors_social_links_order_idx" ON "public"."posts_populated_authors_social_links" USING "btree" ("_order");



CREATE INDEX "posts_populated_authors_social_links_parent_id_idx" ON "public"."posts_populated_authors_social_links" USING "btree" ("_parent_id");



CREATE INDEX "posts_rels_categories_id_idx" ON "public"."posts_rels" USING "btree" ("categories_id");



CREATE INDEX "posts_rels_order_idx" ON "public"."posts_rels" USING "btree" ("order");



CREATE INDEX "posts_rels_parent_idx" ON "public"."posts_rels" USING "btree" ("parent_id");



CREATE INDEX "posts_rels_path_idx" ON "public"."posts_rels" USING "btree" ("path");



CREATE INDEX "posts_rels_posts_id_idx" ON "public"."posts_rels" USING "btree" ("posts_id");



CREATE INDEX "posts_rels_users_id_idx" ON "public"."posts_rels" USING "btree" ("users_id");



CREATE INDEX "posts_slug_idx" ON "public"."posts" USING "btree" ("slug");



CREATE INDEX "posts_updated_at_idx" ON "public"."posts" USING "btree" ("updated_at");



CREATE INDEX "redirects_created_at_idx" ON "public"."redirects" USING "btree" ("created_at");



CREATE INDEX "redirects_from_idx" ON "public"."redirects" USING "btree" ("from");



CREATE INDEX "redirects_rels_order_idx" ON "public"."redirects_rels" USING "btree" ("order");



CREATE INDEX "redirects_rels_pages_id_idx" ON "public"."redirects_rels" USING "btree" ("pages_id");



CREATE INDEX "redirects_rels_parent_idx" ON "public"."redirects_rels" USING "btree" ("parent_id");



CREATE INDEX "redirects_rels_path_idx" ON "public"."redirects_rels" USING "btree" ("path");



CREATE INDEX "redirects_rels_posts_id_idx" ON "public"."redirects_rels" USING "btree" ("posts_id");



CREATE INDEX "redirects_updated_at_idx" ON "public"."redirects" USING "btree" ("updated_at");



CREATE INDEX "search_categories_order_idx" ON "public"."search_categories" USING "btree" ("_order");



CREATE INDEX "search_categories_parent_id_idx" ON "public"."search_categories" USING "btree" ("_parent_id");



CREATE INDEX "search_created_at_idx" ON "public"."search" USING "btree" ("created_at");



CREATE INDEX "search_meta_meta_image_idx" ON "public"."search" USING "btree" ("meta_image_id");



CREATE INDEX "search_rels_company_reports_id_idx" ON "public"."search_rels" USING "btree" ("company_reports_id");



CREATE INDEX "search_rels_order_idx" ON "public"."search_rels" USING "btree" ("order");



CREATE INDEX "search_rels_pages_id_idx" ON "public"."search_rels" USING "btree" ("pages_id");



CREATE INDEX "search_rels_parent_idx" ON "public"."search_rels" USING "btree" ("parent_id");



CREATE INDEX "search_rels_path_idx" ON "public"."search_rels" USING "btree" ("path");



CREATE INDEX "search_rels_posts_id_idx" ON "public"."search_rels" USING "btree" ("posts_id");



CREATE INDEX "search_slug_idx" ON "public"."search" USING "btree" ("slug");



CREATE INDEX "search_updated_at_idx" ON "public"."search" USING "btree" ("updated_at");



CREATE INDEX "users_created_at_idx" ON "public"."users" USING "btree" ("created_at");



CREATE UNIQUE INDEX "users_email_idx" ON "public"."users" USING "btree" ("email");



CREATE INDEX "users_social_links_order_idx" ON "public"."users_social_links" USING "btree" ("_order");



CREATE INDEX "users_social_links_parent_id_idx" ON "public"."users_social_links" USING "btree" ("_parent_id");



CREATE INDEX "users_updated_at_idx" ON "public"."users" USING "btree" ("updated_at");



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_domains_pare" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_analysis_refs_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_names_parent" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_analysis_refs_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_authors"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_authors_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_analysis_refs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs_pages"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_pages_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_analysis_refs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_analysis_refs"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_analysis_refs_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_analysis"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_cta"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_cta_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_graph"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_graph_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_overview_graphs"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_overview_graphs_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_domains"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_company_data_domains_paren" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile_company_data_names"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_company_data_names_parent_" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v_blocks_cr_profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_cr_profile"
    ADD CONSTRAINT "_company_reports_v_blocks_cr_profile_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_form_block"
    ADD CONSTRAINT "_company_reports_v_blocks_form_block_background_media_id_media_" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_form_block"
    ADD CONSTRAINT "_company_reports_v_blocks_form_block_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_form_block"
    ADD CONSTRAINT "_company_reports_v_blocks_form_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v_blocks_highlighted_text"
    ADD CONSTRAINT "_company_reports_v_blocks_highlighted_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_company_reports_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_company_reports_v"
    ADD CONSTRAINT "_company_reports_v_parent_id_company_reports_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."company_reports"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_archive"
    ADD CONSTRAINT "_pages_v_blocks_archive_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading"
    ADD CONSTRAINT "_pages_v_blocks_background_reading_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading_quotes"
    ADD CONSTRAINT "_pages_v_blocks_background_reading_quotes_image_id_media_id_fk" FOREIGN KEY ("image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_background_reading_quotes"
    ADD CONSTRAINT "_pages_v_blocks_background_reading_quotes_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_background_reading"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_content_columns"
    ADD CONSTRAINT "_pages_v_blocks_content_columns_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_content"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_content"
    ADD CONSTRAINT "_pages_v_blocks_content_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_cta_links"
    ADD CONSTRAINT "_pages_v_blocks_cta_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_cta"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_cta"
    ADD CONSTRAINT "_pages_v_blocks_cta_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_faq"
    ADD CONSTRAINT "_pages_v_blocks_faq_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_faq_faq_items"
    ADD CONSTRAINT "_pages_v_blocks_faq_faq_items_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_faq"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_faq"
    ADD CONSTRAINT "_pages_v_blocks_faq_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_features"
    ADD CONSTRAINT "_pages_v_blocks_features_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_features_features"
    ADD CONSTRAINT "_pages_v_blocks_features_features_icon_id_media_id_fk" FOREIGN KEY ("icon_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_features_features"
    ADD CONSTRAINT "_pages_v_blocks_features_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_features"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_features"
    ADD CONSTRAINT "_pages_v_blocks_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_form_block"
    ADD CONSTRAINT "_pages_v_blocks_form_block_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_form_block"
    ADD CONSTRAINT "_pages_v_blocks_form_block_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_form_block"
    ADD CONSTRAINT "_pages_v_blocks_form_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_highlighted_text"
    ADD CONSTRAINT "_pages_v_blocks_highlighted_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_image_text"
    ADD CONSTRAINT "_pages_v_blocks_image_text_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_image_text"
    ADD CONSTRAINT "_pages_v_blocks_image_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_image_text"
    ADD CONSTRAINT "_pages_v_blocks_image_text_se_background_media_id_media_id_fk" FOREIGN KEY ("se_background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_media_block"
    ADD CONSTRAINT "_pages_v_blocks_media_block_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_media_block"
    ADD CONSTRAINT "_pages_v_blocks_media_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_newsletter_signup"
    ADD CONSTRAINT "_pages_v_blocks_newsletter_signup_background_id_media_id_fk" FOREIGN KEY ("background_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_newsletter_signup"
    ADD CONSTRAINT "_pages_v_blocks_newsletter_signup_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing"
    ADD CONSTRAINT "_pages_v_blocks_pricing_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options_features"
    ADD CONSTRAINT "_pages_v_blocks_pricing_pricing_options_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_pricing_pricing_options"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_pricing_pricing_options"
    ADD CONSTRAINT "_pages_v_blocks_pricing_pricing_options_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_pricing"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof_logos"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_logos_logo_id_media_id_fk" FOREIGN KEY ("logo_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof_logos"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_logos_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_social_proof"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_social_proof"
    ADD CONSTRAINT "_pages_v_blocks_social_proof_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_team"
    ADD CONSTRAINT "_pages_v_blocks_team_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_blocks_team_team_members"
    ADD CONSTRAINT "_pages_v_blocks_team_team_members_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_blocks_team_team_members"
    ADD CONSTRAINT "_pages_v_blocks_team_team_members_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v_blocks_team"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_parent_id_pages_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v_rels"
    ADD CONSTRAINT "_pages_v_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_rels"
    ADD CONSTRAINT "_pages_v_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_rels"
    ADD CONSTRAINT "_pages_v_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_rels"
    ADD CONSTRAINT "_pages_v_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v_version_hero_links"
    ADD CONSTRAINT "_pages_v_version_hero_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_pages_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_version_hero_media_id_media_id_fk" FOREIGN KEY ("version_hero_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_pages_v"
    ADD CONSTRAINT "_pages_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_posts_v"
    ADD CONSTRAINT "_posts_v_parent_id_posts_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."posts"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_posts_v_rels"
    ADD CONSTRAINT "_posts_v_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_posts_v_rels"
    ADD CONSTRAINT "_posts_v_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."_posts_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_posts_v_rels"
    ADD CONSTRAINT "_posts_v_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_posts_v_rels"
    ADD CONSTRAINT "_posts_v_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_posts_v"
    ADD CONSTRAINT "_posts_v_version_hero_image_id_media_id_fk" FOREIGN KEY ("version_hero_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_posts_v"
    ADD CONSTRAINT "_posts_v_version_meta_image_id_media_id_fk" FOREIGN KEY ("version_meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors"
    ADD CONSTRAINT "_posts_v_version_populated_authors_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_posts_v"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."_posts_v_version_populated_authors_social_links"
    ADD CONSTRAINT "_posts_v_version_populated_authors_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."_posts_v_version_populated_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."categories_breadcrumbs"
    ADD CONSTRAINT "categories_breadcrumbs_doc_id_categories_id_fk" FOREIGN KEY ("doc_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."categories_breadcrumbs"
    ADD CONSTRAINT "categories_breadcrumbs_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_parent_id_categories_id_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."categories"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors_domains"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_domains_parent_" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_analysis_refs_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors_names"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_names_parent_id" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_analysis_refs_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_authors"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_authors_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_analysis_refs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs_pages"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_pages_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_analysis_refs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_analysis_refs"
    ADD CONSTRAINT "company_reports_blocks_cr_analysis_refs_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_analysis"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_cta"
    ADD CONSTRAINT "company_reports_blocks_cr_cta_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_graph"
    ADD CONSTRAINT "company_reports_blocks_cr_graph_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_overview_graphs"
    ADD CONSTRAINT "company_reports_blocks_cr_overview_graphs_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile_company_data_domains"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_company_data_domains_parent_i" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile_company_data_names"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_company_data_names_parent_id_" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports_blocks_cr_profile"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_cr_profile"
    ADD CONSTRAINT "company_reports_blocks_cr_profile_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_form_block"
    ADD CONSTRAINT "company_reports_blocks_form_block_background_media_id_media_id_" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."company_reports_blocks_form_block"
    ADD CONSTRAINT "company_reports_blocks_form_block_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."company_reports_blocks_form_block"
    ADD CONSTRAINT "company_reports_blocks_form_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."company_reports_blocks_highlighted_text"
    ADD CONSTRAINT "company_reports_blocks_highlighted_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_company_links"
    ADD CONSTRAINT "footer_company_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_quick_links"
    ADD CONSTRAINT "footer_quick_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_rels"
    ADD CONSTRAINT "footer_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_resource_links"
    ADD CONSTRAINT "footer_resource_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_social_links"
    ADD CONSTRAINT "footer_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."footer_solution_links"
    ADD CONSTRAINT "footer_solution_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."footer"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."form_submissions"
    ADD CONSTRAINT "form_submissions_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."form_submissions_submission_data"
    ADD CONSTRAINT "form_submissions_submission_data_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."form_submissions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_checkbox"
    ADD CONSTRAINT "forms_blocks_checkbox_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_country"
    ADD CONSTRAINT "forms_blocks_country_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_email"
    ADD CONSTRAINT "forms_blocks_email_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_message"
    ADD CONSTRAINT "forms_blocks_message_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_number"
    ADD CONSTRAINT "forms_blocks_number_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_select_options"
    ADD CONSTRAINT "forms_blocks_select_options_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms_blocks_select"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_select"
    ADD CONSTRAINT "forms_blocks_select_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_state"
    ADD CONSTRAINT "forms_blocks_state_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_text"
    ADD CONSTRAINT "forms_blocks_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_blocks_textarea"
    ADD CONSTRAINT "forms_blocks_textarea_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."forms_emails"
    ADD CONSTRAINT "forms_emails_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_nav_items"
    ADD CONSTRAINT "header_nav_items_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."header"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."header"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."header_rels"
    ADD CONSTRAINT "header_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_archive"
    ADD CONSTRAINT "pages_blocks_archive_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_background_reading"
    ADD CONSTRAINT "pages_blocks_background_reading_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_background_reading_quotes"
    ADD CONSTRAINT "pages_blocks_background_reading_quotes_image_id_media_id_fk" FOREIGN KEY ("image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_background_reading_quotes"
    ADD CONSTRAINT "pages_blocks_background_reading_quotes_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_background_reading"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_content_columns"
    ADD CONSTRAINT "pages_blocks_content_columns_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_content"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_content"
    ADD CONSTRAINT "pages_blocks_content_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_cta_links"
    ADD CONSTRAINT "pages_blocks_cta_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_cta"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_cta"
    ADD CONSTRAINT "pages_blocks_cta_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_faq"
    ADD CONSTRAINT "pages_blocks_faq_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_faq_faq_items"
    ADD CONSTRAINT "pages_blocks_faq_faq_items_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_faq"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_faq"
    ADD CONSTRAINT "pages_blocks_faq_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_features"
    ADD CONSTRAINT "pages_blocks_features_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_features_features"
    ADD CONSTRAINT "pages_blocks_features_features_icon_id_media_id_fk" FOREIGN KEY ("icon_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_features_features"
    ADD CONSTRAINT "pages_blocks_features_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_features"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_features"
    ADD CONSTRAINT "pages_blocks_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_form_block"
    ADD CONSTRAINT "pages_blocks_form_block_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_form_block"
    ADD CONSTRAINT "pages_blocks_form_block_form_id_forms_id_fk" FOREIGN KEY ("form_id") REFERENCES "public"."forms"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_form_block"
    ADD CONSTRAINT "pages_blocks_form_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_highlighted_text"
    ADD CONSTRAINT "pages_blocks_highlighted_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_image_text"
    ADD CONSTRAINT "pages_blocks_image_text_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_image_text"
    ADD CONSTRAINT "pages_blocks_image_text_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_image_text"
    ADD CONSTRAINT "pages_blocks_image_text_se_background_media_id_media_id_fk" FOREIGN KEY ("se_background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_media_block"
    ADD CONSTRAINT "pages_blocks_media_block_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_media_block"
    ADD CONSTRAINT "pages_blocks_media_block_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_newsletter_signup"
    ADD CONSTRAINT "pages_blocks_newsletter_signup_background_id_media_id_fk" FOREIGN KEY ("background_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_newsletter_signup"
    ADD CONSTRAINT "pages_blocks_newsletter_signup_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_pricing"
    ADD CONSTRAINT "pages_blocks_pricing_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_pricing_pricing_options_features"
    ADD CONSTRAINT "pages_blocks_pricing_pricing_options_features_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_pricing_pricing_options"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_pricing_pricing_options"
    ADD CONSTRAINT "pages_blocks_pricing_pricing_options_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_pricing"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_social_proof"
    ADD CONSTRAINT "pages_blocks_social_proof_background_media_id_media_id_fk" FOREIGN KEY ("background_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_social_proof_logos"
    ADD CONSTRAINT "pages_blocks_social_proof_logos_logo_id_media_id_fk" FOREIGN KEY ("logo_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_social_proof_logos"
    ADD CONSTRAINT "pages_blocks_social_proof_logos_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_social_proof"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_social_proof"
    ADD CONSTRAINT "pages_blocks_social_proof_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_team"
    ADD CONSTRAINT "pages_blocks_team_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_blocks_team_team_members"
    ADD CONSTRAINT "pages_blocks_team_team_members_media_id_media_id_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_blocks_team_team_members"
    ADD CONSTRAINT "pages_blocks_team_team_members_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages_blocks_team"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_hero_links"
    ADD CONSTRAINT "pages_hero_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_hero_media_id_media_id_fk" FOREIGN KEY ("hero_media_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages"
    ADD CONSTRAINT "pages_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."pages_rels"
    ADD CONSTRAINT "pages_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_rels"
    ADD CONSTRAINT "pages_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_rels"
    ADD CONSTRAINT "pages_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."pages_rels"
    ADD CONSTRAINT "pages_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_jobs_log"
    ADD CONSTRAINT "payload_jobs_log_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."payload_jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_company_reports_fk" FOREIGN KEY ("company_reports_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_form_submissions_fk" FOREIGN KEY ("form_submissions_id") REFERENCES "public"."form_submissions"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_forms_fk" FOREIGN KEY ("forms_id") REFERENCES "public"."forms"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_media_fk" FOREIGN KEY ("media_id") REFERENCES "public"."media"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_locked_documents"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_payload_jobs_fk" FOREIGN KEY ("payload_jobs_id") REFERENCES "public"."payload_jobs"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_redirects_fk" FOREIGN KEY ("redirects_id") REFERENCES "public"."redirects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_search_fk" FOREIGN KEY ("search_id") REFERENCES "public"."search"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_locked_documents_rels"
    ADD CONSTRAINT "payload_locked_documents_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."payload_preferences"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."payload_preferences_rels"
    ADD CONSTRAINT "payload_preferences_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts"
    ADD CONSTRAINT "posts_hero_image_id_media_id_fk" FOREIGN KEY ("hero_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."posts"
    ADD CONSTRAINT "posts_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."posts_populated_authors"
    ADD CONSTRAINT "posts_populated_authors_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts_populated_authors_social_links"
    ADD CONSTRAINT "posts_populated_authors_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."posts_populated_authors"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts_rels"
    ADD CONSTRAINT "posts_rels_categories_fk" FOREIGN KEY ("categories_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts_rels"
    ADD CONSTRAINT "posts_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts_rels"
    ADD CONSTRAINT "posts_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."posts_rels"
    ADD CONSTRAINT "posts_rels_users_fk" FOREIGN KEY ("users_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."redirects_rels"
    ADD CONSTRAINT "redirects_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."redirects_rels"
    ADD CONSTRAINT "redirects_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."redirects"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."redirects_rels"
    ADD CONSTRAINT "redirects_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."search_categories"
    ADD CONSTRAINT "search_categories_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."search"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."search"
    ADD CONSTRAINT "search_meta_image_id_media_id_fk" FOREIGN KEY ("meta_image_id") REFERENCES "public"."media"("id") ON DELETE SET NULL;



ALTER TABLE ONLY "public"."search_rels"
    ADD CONSTRAINT "search_rels_company_reports_fk" FOREIGN KEY ("company_reports_id") REFERENCES "public"."company_reports"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."search_rels"
    ADD CONSTRAINT "search_rels_pages_fk" FOREIGN KEY ("pages_id") REFERENCES "public"."pages"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."search_rels"
    ADD CONSTRAINT "search_rels_parent_fk" FOREIGN KEY ("parent_id") REFERENCES "public"."search"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."search_rels"
    ADD CONSTRAINT "search_rels_posts_fk" FOREIGN KEY ("posts_id") REFERENCES "public"."posts"("id") ON DELETE CASCADE;



ALTER TABLE ONLY "public"."users_social_links"
    ADD CONSTRAINT "users_social_links_parent_id_fk" FOREIGN KEY ("_parent_id") REFERENCES "public"."users"("id") ON DELETE CASCADE;





ALTER PUBLICATION "supabase_realtime" OWNER TO "postgres";


GRANT USAGE ON SCHEMA "api" TO "anon";
GRANT USAGE ON SCHEMA "api" TO "authenticated";
GRANT USAGE ON SCHEMA "api" TO "service_role";



GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";



































































































































































































GRANT ALL ON TABLE "public"."_company_reports_v" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domains" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_domai_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_authors_names_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_pages" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_pages" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_analysis_refs_pages" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_analysis_refs_pages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_cta" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_cta" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_cta" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_cta_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_cta_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_cta_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_graph" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_graph" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_graph" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_graph_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_graph_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_graph_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_overview_graphs" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_overview_graphs" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_overview_graphs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_overview_graphs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_domains" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_domains" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_domains" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_domain_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_names" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_names" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_cr_profile_company_data_names" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_company_data_names_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_cr_profile_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_form_block" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_form_block" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_form_block" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_form_block_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_form_block_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_form_block_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_company_reports_v_blocks_highlighted_text" TO "anon";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_highlighted_text" TO "authenticated";
GRANT ALL ON TABLE "public"."_company_reports_v_blocks_highlighted_text" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_highlighted_text_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_highlighted_text_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_blocks_highlighted_text_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_company_reports_v_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_company_reports_v_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_archive" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_archive" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_archive" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_archive_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_archive_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_archive_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading_quotes" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading_quotes" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_background_reading_quotes" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_quotes_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_quotes_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_background_reading_quotes_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_content" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_content" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_content" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_content_columns" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_content_columns" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_content_columns" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_columns_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_columns_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_columns_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_content_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_cta" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_cta" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_cta" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_cta_links" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_cta_links" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_cta_links" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_links_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_links_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_cta_links_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_faq" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_faq" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_faq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_faq_faq_items" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_faq_faq_items" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_faq_faq_items" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_faq_items_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_faq_items_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_faq_items_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_faq_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_features" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_features" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_features" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_features_features" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_features_features" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_features_features" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_features_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_features_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_features_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_features_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_form_block" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_form_block" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_form_block" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_form_block_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_form_block_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_form_block_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_highlighted_text" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_highlighted_text" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_highlighted_text" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_highlighted_text_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_highlighted_text_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_highlighted_text_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_image_text" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_image_text" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_image_text" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_image_text_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_image_text_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_image_text_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_media_block" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_media_block" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_media_block" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_media_block_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_media_block_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_media_block_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_newsletter_signup" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_newsletter_signup" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_newsletter_signup" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_newsletter_signup_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_newsletter_signup_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_newsletter_signup_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options_features" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options_features" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_pricing_pricing_options_features" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_features_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_pricing_pricing_options_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof_logos" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof_logos" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_social_proof_logos" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_logos_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_logos_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_social_proof_logos_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_team" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_team" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_team" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_blocks_team_team_members" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_blocks_team_team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_blocks_team_team_members" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_team_members_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_team_members_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_blocks_team_team_members_id_seq" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_rels" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_pages_v_version_hero_links" TO "anon";
GRANT ALL ON TABLE "public"."_pages_v_version_hero_links" TO "authenticated";
GRANT ALL ON TABLE "public"."_pages_v_version_hero_links" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_pages_v_version_hero_links_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_pages_v_version_hero_links_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_pages_v_version_hero_links_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_posts_v" TO "anon";
GRANT ALL ON TABLE "public"."_posts_v" TO "authenticated";
GRANT ALL ON TABLE "public"."_posts_v" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_posts_v_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_posts_v_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_posts_v_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_posts_v_rels" TO "anon";
GRANT ALL ON TABLE "public"."_posts_v_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."_posts_v_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_posts_v_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_posts_v_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_posts_v_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors" TO "anon";
GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors" TO "authenticated";
GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors_social_links" TO "anon";
GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors_social_links" TO "authenticated";
GRANT ALL ON TABLE "public"."_posts_v_version_populated_authors_social_links" TO "service_role";



GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_social_links_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_social_links_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."_posts_v_version_populated_authors_social_links_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."categories" TO "anon";
GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";



GRANT ALL ON TABLE "public"."categories_breadcrumbs" TO "anon";
GRANT ALL ON TABLE "public"."categories_breadcrumbs" TO "authenticated";
GRANT ALL ON TABLE "public"."categories_breadcrumbs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports" TO "anon";
GRANT ALL ON TABLE "public"."company_reports" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_domains" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_domains" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_domains" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_names" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_names" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_authors_names" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_pages" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_pages" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_analysis_refs_pages" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_cta" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_cta" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_cta" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_graph" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_graph" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_graph" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_overview_graphs" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_overview_graphs" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_overview_graphs" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_domains" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_domains" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_domains" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_names" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_names" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_cr_profile_company_data_names" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_form_block" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_form_block" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_form_block" TO "service_role";



GRANT ALL ON TABLE "public"."company_reports_blocks_highlighted_text" TO "anon";
GRANT ALL ON TABLE "public"."company_reports_blocks_highlighted_text" TO "authenticated";
GRANT ALL ON TABLE "public"."company_reports_blocks_highlighted_text" TO "service_role";



GRANT ALL ON SEQUENCE "public"."company_reports_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."company_reports_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."company_reports_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer" TO "anon";
GRANT ALL ON TABLE "public"."footer" TO "authenticated";
GRANT ALL ON TABLE "public"."footer" TO "service_role";



GRANT ALL ON TABLE "public"."footer_company_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_company_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_company_links" TO "service_role";



GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."footer_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer_quick_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_quick_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_quick_links" TO "service_role";



GRANT ALL ON TABLE "public"."footer_rels" TO "anon";
GRANT ALL ON TABLE "public"."footer_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."footer_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."footer_resource_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_resource_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_resource_links" TO "service_role";



GRANT ALL ON TABLE "public"."footer_social_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_social_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_social_links" TO "service_role";



GRANT ALL ON TABLE "public"."footer_solution_links" TO "anon";
GRANT ALL ON TABLE "public"."footer_solution_links" TO "authenticated";
GRANT ALL ON TABLE "public"."footer_solution_links" TO "service_role";



GRANT ALL ON TABLE "public"."form_submissions" TO "anon";
GRANT ALL ON TABLE "public"."form_submissions" TO "authenticated";
GRANT ALL ON TABLE "public"."form_submissions" TO "service_role";



GRANT ALL ON SEQUENCE "public"."form_submissions_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."form_submissions_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."form_submissions_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."form_submissions_submission_data" TO "anon";
GRANT ALL ON TABLE "public"."form_submissions_submission_data" TO "authenticated";
GRANT ALL ON TABLE "public"."form_submissions_submission_data" TO "service_role";



GRANT ALL ON TABLE "public"."forms" TO "anon";
GRANT ALL ON TABLE "public"."forms" TO "authenticated";
GRANT ALL ON TABLE "public"."forms" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_checkbox" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_checkbox" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_checkbox" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_country" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_country" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_country" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_email" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_email" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_email" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_message" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_message" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_message" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_number" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_number" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_number" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_select" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_select" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_select" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_select_options" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_select_options" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_select_options" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_state" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_state" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_state" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_text" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_text" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_text" TO "service_role";



GRANT ALL ON TABLE "public"."forms_blocks_textarea" TO "anon";
GRANT ALL ON TABLE "public"."forms_blocks_textarea" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_blocks_textarea" TO "service_role";



GRANT ALL ON TABLE "public"."forms_emails" TO "anon";
GRANT ALL ON TABLE "public"."forms_emails" TO "authenticated";
GRANT ALL ON TABLE "public"."forms_emails" TO "service_role";



GRANT ALL ON SEQUENCE "public"."forms_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."forms_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."forms_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."header" TO "anon";
GRANT ALL ON TABLE "public"."header" TO "authenticated";
GRANT ALL ON TABLE "public"."header" TO "service_role";



GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."header_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."header_nav_items" TO "anon";
GRANT ALL ON TABLE "public"."header_nav_items" TO "authenticated";
GRANT ALL ON TABLE "public"."header_nav_items" TO "service_role";



GRANT ALL ON TABLE "public"."header_rels" TO "anon";
GRANT ALL ON TABLE "public"."header_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."header_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."header_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."media" TO "anon";
GRANT ALL ON TABLE "public"."media" TO "authenticated";
GRANT ALL ON TABLE "public"."media" TO "service_role";



GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."media_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."pages" TO "anon";
GRANT ALL ON TABLE "public"."pages" TO "authenticated";
GRANT ALL ON TABLE "public"."pages" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_archive" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_archive" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_archive" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_background_reading" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_background_reading" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_background_reading" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_background_reading_quotes" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_background_reading_quotes" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_background_reading_quotes" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_content" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_content" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_content" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_content_columns" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_content_columns" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_content_columns" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_cta" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_cta" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_cta" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_cta_links" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_cta_links" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_cta_links" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_faq" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_faq" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_faq" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_faq_faq_items" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_faq_faq_items" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_faq_faq_items" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_features" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_features" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_features" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_features_features" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_features_features" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_features_features" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_form_block" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_form_block" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_form_block" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_highlighted_text" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_highlighted_text" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_highlighted_text" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_image_text" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_image_text" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_image_text" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_media_block" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_media_block" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_media_block" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_newsletter_signup" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_newsletter_signup" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_newsletter_signup" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_pricing" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_pricing" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_pricing" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options_features" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options_features" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_pricing_pricing_options_features" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_social_proof" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_social_proof" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_social_proof" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_social_proof_logos" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_social_proof_logos" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_social_proof_logos" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_team" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_team" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_team" TO "service_role";



GRANT ALL ON TABLE "public"."pages_blocks_team_team_members" TO "anon";
GRANT ALL ON TABLE "public"."pages_blocks_team_team_members" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_blocks_team_team_members" TO "service_role";



GRANT ALL ON TABLE "public"."pages_hero_links" TO "anon";
GRANT ALL ON TABLE "public"."pages_hero_links" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_hero_links" TO "service_role";



GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."pages_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."pages_rels" TO "anon";
GRANT ALL ON TABLE "public"."pages_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."pages_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."pages_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."pages_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."pages_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_jobs" TO "anon";
GRANT ALL ON TABLE "public"."payload_jobs" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_jobs" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_jobs_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_jobs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_jobs_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_jobs_log" TO "anon";
GRANT ALL ON TABLE "public"."payload_jobs_log" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_jobs_log" TO "service_role";



GRANT ALL ON TABLE "public"."payload_locked_documents" TO "anon";
GRANT ALL ON TABLE "public"."payload_locked_documents" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_locked_documents" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "anon";
GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_locked_documents_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_locked_documents_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_migrations" TO "anon";
GRANT ALL ON TABLE "public"."payload_migrations" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_migrations" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_migrations_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_preferences" TO "anon";
GRANT ALL ON TABLE "public"."payload_preferences" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_preferences" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_preferences_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "anon";
GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."payload_preferences_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."payload_preferences_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."posts" TO "anon";
GRANT ALL ON TABLE "public"."posts" TO "authenticated";
GRANT ALL ON TABLE "public"."posts" TO "service_role";



GRANT ALL ON SEQUENCE "public"."posts_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."posts_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."posts_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."posts_populated_authors" TO "anon";
GRANT ALL ON TABLE "public"."posts_populated_authors" TO "authenticated";
GRANT ALL ON TABLE "public"."posts_populated_authors" TO "service_role";



GRANT ALL ON TABLE "public"."posts_populated_authors_social_links" TO "anon";
GRANT ALL ON TABLE "public"."posts_populated_authors_social_links" TO "authenticated";
GRANT ALL ON TABLE "public"."posts_populated_authors_social_links" TO "service_role";



GRANT ALL ON TABLE "public"."posts_rels" TO "anon";
GRANT ALL ON TABLE "public"."posts_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."posts_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."posts_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."posts_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."posts_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."redirects" TO "anon";
GRANT ALL ON TABLE "public"."redirects" TO "authenticated";
GRANT ALL ON TABLE "public"."redirects" TO "service_role";



GRANT ALL ON SEQUENCE "public"."redirects_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."redirects_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."redirects_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."redirects_rels" TO "anon";
GRANT ALL ON TABLE "public"."redirects_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."redirects_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."redirects_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."redirects_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."redirects_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."search" TO "anon";
GRANT ALL ON TABLE "public"."search" TO "authenticated";
GRANT ALL ON TABLE "public"."search" TO "service_role";



GRANT ALL ON TABLE "public"."search_categories" TO "anon";
GRANT ALL ON TABLE "public"."search_categories" TO "authenticated";
GRANT ALL ON TABLE "public"."search_categories" TO "service_role";



GRANT ALL ON SEQUENCE "public"."search_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."search_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."search_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."search_rels" TO "anon";
GRANT ALL ON TABLE "public"."search_rels" TO "authenticated";
GRANT ALL ON TABLE "public"."search_rels" TO "service_role";



GRANT ALL ON SEQUENCE "public"."search_rels_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."search_rels_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."search_rels_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."users" TO "anon";
GRANT ALL ON TABLE "public"."users" TO "authenticated";
GRANT ALL ON TABLE "public"."users" TO "service_role";



GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."users_id_seq" TO "service_role";



GRANT ALL ON TABLE "public"."users_social_links" TO "anon";
GRANT ALL ON TABLE "public"."users_social_links" TO "authenticated";
GRANT ALL ON TABLE "public"."users_social_links" TO "service_role";



ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";






ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";






























RESET ALL;
