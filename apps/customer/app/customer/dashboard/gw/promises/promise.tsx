import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@ui/components/ui/card'
import { EkoMarkdown } from '@/components/markdown/eko-markdown'
import { CitationType } from '@/components/citation'
import React from 'react'
import { PromiseTypeV2 } from '@/types'
import { CheckCircle2, CircleXIcon, HelpCircle } from 'lucide-react'
import { Badge } from '@ui/components/ui/badge'

// New component that works directly with PromiseTypeV2
export function PromiseCardV2({item, admin}: { item: PromiseTypeV2, admin: boolean }) {
    const model = item.model;

    // Get the first citation if available
    const firstCitation = model.citations && model.citations.length > 0 ? model.citations[0] : null;
    const authors = firstCitation?.authors?.join(", ") || '';
    const docTitle = firstCitation?.title || model.promise_doc || '';
    const docYear = firstCitation?.year || model.promise_doc_year || new Date().getFullYear();
    const docUrl = firstCitation?.public_url || '';

    // Determine the promise status icon and color
    let statusIcon;
    let statusColor;
    let statusText;

    if (item.kept === true) {
        statusIcon = <CheckCircle2 className="w-8 h-8 inline-block"/>;
        statusColor = "bg-green-50 border-green-200 dark:bg-green-950 dark:border-green-800";
        statusText = "Promise Kept";
    } else if (item.kept === false) {
        statusIcon = <CircleXIcon className="w-8 h-8 inline-block"/>;
        statusColor = "bg-red-50 border-red-200 dark:bg-red-950 dark:border-red-800";
        statusText = "Promise Not Kept";
    } else {
        statusIcon = <HelpCircle className="w-8 h-8 inline-block"/>;
        statusColor = "bg-yellow-50 border-yellow-200 dark:bg-yellow-950 dark:border-yellow-800";
        statusText = "Status Uncertain";
    }

    // Parse evidence if available
    const evidence = typeof model.evidence === 'object' ? model.evidence : {};
    const evidenceEntries = Object.entries(evidence);

    return (
        <Card key={item.id} className="mb-6 overflow-hidden">
            {/* Status Banner */}
            <div className={`w-full p-2 flex items-center justify-between ${statusColor} border-b`}>
                <div className="flex items-center gap-2">
                    <span className={`${item.kept === true ? 'text-green-500' : item.kept === false ? 'text-red-500' : 'text-yellow-500'}`}>
                        {statusIcon}
                    </span>
                    <span className="font-semibold">{statusText}</span>
                </div>
                <Badge variant="secondary">
                    Confidence: {model.confidence}%
                </Badge>
            </div>

            <CardHeader className="pb-2">
                <CardTitle className="text-xl flex items-start gap-2">
                    <div className="flex-1">
                        <span className="text-sm text-muted-foreground block mb-1">The Promise:</span>
                        "<em>{model.text}"</em>
                    </div>
                </CardTitle>
                <CardDescription>
                    <div className="flex justify-between items-center mt-2">
                        <div>
                            <span className="text-sm font-medium">Made in {docYear}</span>
                            {model.esg_promise &&
                                <Badge variant="outline" className="ml-2">ESG Promise</Badge>
                            }
                        </div>
                        <div>
                            {docUrl ? (
                                <a href={docUrl} target="_blank" rel="noopener noreferrer" className="text-sm hover:underline">
                                    {authors && <span>{authors}, </span>}
                                    {docTitle} ({docYear})
                                </a>
                            ) : (
                                <span className="text-sm">
                                    {authors && <span>{authors}, </span>}
                                    {docTitle} ({docYear})
                                </span>
                            )}
                        </div>
                    </div>
                </CardDescription>
            </CardHeader>

            <CardContent>
                {/* Summary Section */}
                <div className="mb-6 p-4 border rounded-md bg-slate-50 dark:bg-slate-900">
                    <h3 className="text-lg font-semibold mb-2">Summary</h3>
                    <p>{model.summary}</p>
                </div>

                {/* Conclusion Section */}
                <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-2">Conclusion</h3>
                    <div className="p-4 border rounded-md">
                        <p className="font-medium">{model.conclusion}</p>
                    </div>
                </div>

                {/* Detailed Analysis */}
                <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-2">Detailed Analysis</h3>
                    <div className="p-4 border rounded-md">
                        <EkoMarkdown
                            citations={model.citations as unknown as CitationType[]}
                            admin={admin}
                        >
                            {model.verdict}
                        </EkoMarkdown>
                    </div>
                </div>

                {/* Evidence Section */}
                {evidenceEntries.length > 0 && (
                    <div className="mb-4">
                        <h3 className="text-lg font-semibold mb-2">Supporting Evidence</h3>
                        <div className="space-y-3">
                            {evidenceEntries.slice(0, 3).map(([key, value]: [string, any], index) => (
                                <div key={key} className="p-3 border rounded-md">
                                    <div className="flex justify-between mb-2">
                                        <span className="font-medium">Evidence {index + 1}</span>
                                        {value.doc_year && (
                                            <Badge variant="outline">{value.doc_year}</Badge>
                                        )}
                                    </div>
                                    <p className="text-sm mb-2">{value.text}</p>
                                    {value.summary && (
                                        <div className="text-sm text-muted-foreground">
                                            <span className="font-medium">Analysis: </span>
                                            {value.summary}
                                        </div>
                                    )}
                                    {value.fulfills !== undefined && (
                                        <Badge
                                            variant={value.fulfills ? "default" : "destructive"}
                                            className="mt-2"
                                        >
                                            {value.fulfills ? "Supports Promise" : "Does Not Support Promise"}
                                        </Badge>
                                    )}
                                </div>
                            ))}

                            {evidenceEntries.length > 3 && (
                                <p className="text-sm text-muted-foreground text-center">
                                    + {evidenceEntries.length - 3} more pieces of evidence
                                </p>
                            )}
                        </div>
                    </div>
                )}

                {/* Context Section if available */}
                {model.context && (
                    <div className="mt-4 p-4 border rounded-md bg-slate-50 dark:bg-slate-900">
                        <h3 className="text-lg font-semibold mb-2">Additional Context</h3>
                        <p className="text-sm">{model.context}</p>
                    </div>
                )}
            </CardContent>
        </Card>
    );
}
