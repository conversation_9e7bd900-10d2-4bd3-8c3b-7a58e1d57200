'use client'
import React, { useEffect } from 'react'
import { PromisesListV2 } from './promises-list'
import { EkoPageTitle } from '@/components/page-title'
import NoData from '@/components/no-data'
import { useEntity } from '@/components/context/entity/entity-context'
import { useAuth } from '@/components/context/auth/auth-context'
import { useNav } from '@/components/context/nav/nav-context'

export default function Page() {
    const entityContext = useEntity();
    const auth = useAuth();
    const nav = useNav();

    useEffect(() => {
        nav.changeNavPath([{label: "Dashboard", href: "/customer/dashboard"}, {
            label: "Promises",
            href: "/customer/dashboard/gw/promises"
        }]);
    }, []);

    // Show loading state while promises are being fetched
    if (entityContext.isLoadingPromises) {
        return <div className="container mx-auto p-4">Loading promises...</div>;
    }

    return entityContext.promisesData && entityContext.promisesData.length > 0 ? (
        <div className="container mx-auto p-4">
            <EkoPageTitle title="Promises"/>
            <PromisesListV2 promisesData={entityContext.promisesData} admin={auth.admin}/>
        </div>
    ) : <NoData title="No Promises" description="No promises found for this entity"/>
}
