"use client"
import React, {useEffect, useState} from 'react';
import {<PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger} from '@ui/components/ui/tabs';
import {EntityAnalysisHistory} from "@/app/customer/analysis/entity-ana-history";
import {DocumentAnalysisHistory} from "@/app/customer/analysis/doc-ana-history";
import {EntityAnalysesRunType, QuotaUsedType, SingleDocAnalysesType} from "@/types";
import {runAsync} from "@utils/react-utils";
import {useAuth} from "@/components/context/auth/auth-context";
import {createClient} from "@/app/supabase/client";
import {useToast} from "@ui/hooks/use-toast";
import {DocQuotaCard, EntityAnalysisQuotaCard, EntityQuotaCard} from "@/app/customer/analysis/quotas";
import {PageHeader} from "@/components/page-header";
import {ffDocumentAnalysis} from "@/app/feature-flags";

const AnalysisDashboard = () => {
    const [activeTab, setActiveTab] = useState('companies');
    const {user, admin} = useAuth();
    const {toast} = useToast();
    const [docAnalyses, setDocAnalyses] = useState<SingleDocAnalysesType[]>([]);
    const supabase = createClient();
    const [quotaInfo, setQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalyses, setEntityAnalyses] = useState<EntityAnalysesRunType[]>([]);
    const [entityQuotaInfo, setEntityQuotaInfo] = useState<QuotaUsedType>();
    const [entityAnalysisQuotaInfo, setEntityAnalysisQuotaInfo] = useState<QuotaUsedType>();

    async function updateCompanies() {
        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_entity_analysis_runs').select("*").eq("run_by", user?.id!);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setEntityAnalyses(analyses as EntityAnalysesRunType[]);
        }

        const userId = user?.id;
        if (!userId) return;
        const {
            data: analysisQuota,
            error: analysisQuotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity-analysis").single();
        console.log(analysisQuota);
        if (analysisQuotaError) {
            toast({description: analysisQuotaError.message, variant: 'destructive'});
        } else {
            setEntityAnalysisQuotaInfo(analysisQuota as QuotaUsedType);
        }
        const {
            data: companyQuota,
            error: companyQuotaError
        } = await supabase.from('view_quota_used').select().eq("profile_id", userId).eq("type", "entity").single();
        if (companyQuotaError) {
            toast({description: companyQuotaError.message, variant: 'destructive'});
        } else {
            setEntityQuotaInfo(companyQuota as QuotaUsedType);
        }
    }


    async function updateRuns() {
        const userId = user?.id!;


        const {
            data: analyses,
            error: docAnalysesError
        } = await supabase.from('view_single_doc_runs').select("*").eq("run_by", userId);

        const {
            data: quota,
            error: quotaError
        } = await supabase.from('view_quota_used').select("*").eq("profile_id", userId);

        if (docAnalysesError) {
            toast({description: docAnalysesError.message, variant: 'destructive'});
        } else {
            setDocAnalyses(analyses as SingleDocAnalysesType[]);
        }
        if (quotaError) {
            toast({description: quotaError.message, variant: 'destructive'});
        } else {
            setQuotaInfo(quota.filter(i => i.type === "document-analysis")[0] as QuotaUsedType);
        }

    }


    useEffect(() => {
        runAsync(async () => {
            if (!user) return;
            await updateRuns();
            await updateCompanies();
        });

    }, [user]);


    return (
        <>
            <PageHeader/>
            <div className="p-6 space-y-6 dashboard-container ">
                <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 w-full">

                    {quotaInfo &&  ffDocumentAnalysis() && (<DocQuotaCard quotaInfo={quotaInfo}/>)}
                    {entityAnalysisQuotaInfo &&
                        <EntityAnalysisQuotaCard entityAnalysisQuotaInfo={entityAnalysisQuotaInfo}/>}
                    {entityQuotaInfo && <EntityQuotaCard entityQuotaInfo={entityQuotaInfo}/>}

                </div>

                {/* Main Content Tabs */}
                <Tabs defaultValue="entity_analysis" className="space-y-4 w-full">
                    <TabsList>
                        <TabsTrigger value="entity_analysis">Company Analysis</TabsTrigger>
                        {ffDocumentAnalysis() && (<TabsTrigger value="document_analysis">Document Analysis</TabsTrigger>)}
                    </TabsList>


                    <TabsContent value="entity_analysis" className="space-y-4">
                        <EntityAnalysisHistory entityAnalyses={entityAnalyses}/>
                    </TabsContent>

                    <TabsContent value="document_analysis" className="space-y-4">
                        <DocumentAnalysisHistory docAnalyses={docAnalyses}/>
                    </TabsContent>
                </Tabs>
            </div>
        </>
    );
};

export default AnalysisDashboard;
