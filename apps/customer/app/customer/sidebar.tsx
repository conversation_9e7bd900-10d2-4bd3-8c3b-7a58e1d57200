"use client";
import { Badge<PERSON>heck, BarChart2Icon, Bell, ChevronRight, ChevronsUpDown, CreditCard, LogOut } from 'lucide-react'

import { Avatar, AvatarFallback, AvatarImage } from '@ui/components/ui/avatar'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@ui/components/ui/collapsible'
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from '@ui/components/ui/dropdown-menu'
import {
    Sidebar,
    SidebarContent,
    SidebarFooter,
    SidebarGroup,
    SidebarGroupContent,
    SidebarGroupLabel,
    SidebarHeader,
    SidebarInset,
    SidebarMenu,
    SidebarMenuAction,
    SidebarMenuButton,
    SidebarMenuItem,
    SidebarMenuSub,
    SidebarMenuSubButton,
    SidebarMenuSubItem,
    SidebarProvider,
} from '@ui/components/ui/sidebar'
import React, { useEffect } from 'react'
import { EkoLogoText, EkoSymbolBrand } from '@utils/images'
import { useRouter, useSearchParams } from 'next/navigation'
import { createClient } from '@/app/supabase/client'
import Link from 'next/link'
import { backOfficeListener, messageListener } from '@/components/backoffice'
import { useToast } from '@ui/hooks/use-toast'
import { AccountMessage } from '@/components/account-messages'
import { useAuth } from '@/components/context/auth/auth-context'
import { useEntity } from '@/components/context/entity/entity-context'
import { useLocalStorageValue } from '@react-hookz/web'
import { navigationTree } from '@/app/customer/navigation'

type SidebarState = {
    [key: string]: boolean
}

export function SidebarWithSelectors({admin, children}: { admin: boolean, children: React.ReactNode }) {
    const router = useRouter();
    const supabase = createClient();
    const {toast, dismiss} = useToast()

    const auth = useAuth()
    const entityContext = useEntity()
    const searchParams = useSearchParams();

    const sidebarState = useLocalStorageValue<SidebarState>("sidebar-state", {
        defaultValue: {},
        initializeWithValue: false
    });

    function toggleSidebar(item: string) {
        sidebarState.set(prev => prev ? ({...prev, [item]: !prev[item]}) : {[item]: true});
    }

    function isSidebarOpen(item: string) {
        return sidebarState.value && sidebarState.value[item] !== undefined ? sidebarState.value[item] : true;
    }

    async function signOut() {
        const {error} = await supabase.auth.signOut()
        if (error) {
            toast({description: "" + error});
        } else {
            window.location.href = ('/');
        }
    }


    useEffect(() => {
        if (!auth.user?.id) {
            return
        }

        const channels = backOfficeListener(supabase, (payload, error) => {
            if (error) {
                toast({description: error.message, variant: "destructive"});
            }
            if (payload) {
                toast({description: payload.message, title: "Request Completed", duration: -1});
            }
        });
        return () => {
            channels.unsubscribe();
        };

    }, [auth.user?.id]); // Only depend on user ID, not the entire auth object

    useEffect(() => {

        if (!auth.user?.id) {
            return
        }


        console.log("Listening for messages");
        const channels = messageListener(supabase, auth.user?.id!, (payload) => {
            const toasted: any[] = [];
            console.log("Payload", payload);
            if (payload) {
                toasted.push(toast({
                    description: <AccountMessage notification={payload} onRead={() => toasted[0].dismiss()}
                                                 onDelete={() => toasted[0].dismiss()}/>
                }));
            }
        });
        return () => {
            channels.unsubscribe();
        };

    }, [auth.user?.id]); // Only depend on user ID, not the entire auth object


    if (!sidebarState.value) {
        return null;
    }



    return  (
        <SidebarProvider>
            <Sidebar variant="floating" className="sidebarz-20">
                <SidebarHeader>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <SidebarMenuButton size="lg" asChild>
                                <a href="/">
                                    <div className="">
                                        <EkoSymbolBrand height={26} className="inline-block drop-shadow-lg"/>
                                        <EkoLogoText height={23}
                                                     className="dark:hidden ml-3 mt-0.5 inline-block drop-shadow-sm"
                                                     ekoColor="#666" intelligenceColor="#111"/>
                                        <EkoLogoText height={23}
                                                     className="hidden dark:inline-block ml-3 mt-0.5  drop-shadow-sm"
                                                     ekoColor="#bbb" intelligenceColor="#eee"/>
                                    </div>
                                </a>
                            </SidebarMenuButton>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarHeader>
                <SidebarContent>
                    <SidebarGroup>
                        <SidebarGroupLabel>Analysis</SidebarGroupLabel>
                        <SidebarMenu>
                            {navigationTree.navMain.map((item) => (
                                <Collapsible
                                    key={item.title}
                                    asChild
                                    defaultOpen={isSidebarOpen(item.title)}
                                    onOpenChange={(open) => toggleSidebar(item.title)}
                                >
                                    <SidebarMenuItem>
                                        <SidebarMenuButton asChild tooltip={item.title}>
                                            <div>
                                                {item.icon &&
                                                    <item.icon/>
                                                }
                                                <span>{item.title}</span>
                                            </div>
                                        </SidebarMenuButton>
                                        {item.items?.length ? (
                                            <>
                                                <CollapsibleTrigger asChild>
                                                    <SidebarMenuAction className="data-[state=open]:rotate-90">
                                                        <ChevronRight/>
                                                        <span className="sr-only">Toggle</span>
                                                    </SidebarMenuAction>
                                                </CollapsibleTrigger>
                                                <CollapsibleContent>
                                                    <SidebarMenuSub>
                                                        {item.items?.map((subItem) => { 
                                                            // Skip if hidden
                                                            if (subItem.hidden) return null;
                                                            
                                                            // Skip if feature flag required and not enabled
                                                            if (subItem.requires && !auth.hasFeature(subItem.requires)) return null;
                                                            
                                                            return (
                                                                <SidebarMenuSubItem key={subItem.title}>
                                                                    <SidebarMenuSubButton asChild>
                                                                        <Link
                                                                            href={subItem.url + (entityContext.queryString ? "?" + entityContext.queryString : "")}>
                                                                            {subItem.icon &&
                                                                                <subItem.icon className="opacity-50"/>}
                                                                            <span>{subItem.title}</span>
                                                                        </Link>
                                                                    </SidebarMenuSubButton>
                                                                </SidebarMenuSubItem>
                                                            );
                                                        })}
                                                    </SidebarMenuSub>
                                                </CollapsibleContent>
                                            </>
                                        ) : null}
                                    </SidebarMenuItem>
                                </Collapsible>
                            ))}
                        </SidebarMenu>
                    </SidebarGroup>
                    {/*<SidebarGroup className="group-data-[collapsible=icon]:hidden">*/}
                    {/*    <SidebarGroupLabel>Projects</SidebarGroupLabel>*/}
                    {/*    <SidebarMenu>*/}
                    {/*        {data.projects.map((item) => (*/}
                    {/*            <SidebarMenuItem key={item.name}>*/}
                    {/*                <SidebarMenuButton asChild>*/}
                    {/*                    <a href={item.url}>*/}
                    {/*                        <item.icon/>*/}
                    {/*                        <span>{item.name}</span>*/}
                    {/*                    </a>*/}
                    {/*                </SidebarMenuButton>*/}
                    {/*                <DropdownMenu>*/}
                    {/*                    <DropdownMenuTrigger asChild>*/}
                    {/*                        <SidebarMenuAction showOnHover>*/}
                    {/*                            <MoreHorizontal/>*/}
                    {/*                            <span className="sr-only">More</span>*/}
                    {/*                        </SidebarMenuAction>*/}
                    {/*                    </DropdownMenuTrigger>*/}
                    {/*                    <DropdownMenuContent*/}
                    {/*                        className="w-48"*/}
                    {/*                        side="bottom"*/}
                    {/*                        align="end"*/}
                    {/*                    >*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Folder className="text-muted-foreground"/>*/}
                    {/*                            <span>View Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Share className="text-muted-foreground"/>*/}
                    {/*                            <span>Share Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                        <DropdownMenuSeparator/>*/}
                    {/*                        <DropdownMenuItem>*/}
                    {/*                            <Trash2 className="text-muted-foreground"/>*/}
                    {/*                            <span>Delete Project</span>*/}
                    {/*                        </DropdownMenuItem>*/}
                    {/*                    </DropdownMenuContent>*/}
                    {/*                </DropdownMenu>*/}
                    {/*            </SidebarMenuItem>*/}
                    {/*        ))}*/}
                    {/*        <SidebarMenuItem>*/}
                    {/*            <SidebarMenuButton>*/}
                    {/*                <MoreHorizontal/>*/}
                    {/*                <span>More</span>*/}
                    {/*            </SidebarMenuButton>*/}
                    {/*        </SidebarMenuItem>*/}
                    {/*    </SidebarMenu>*/}
                    {/*</SidebarGroup>*/}
                    <SidebarGroup className="mt-auto">
                        <SidebarGroupContent>
                            <SidebarMenu>
                                {navigationTree.navSecondary.map((item) => (
                                    <SidebarMenuItem key={item.title}>
                                        <SidebarMenuButton asChild size="sm">
                                            <a href={item.url + (entityContext.queryString ? "?" + entityContext.queryString : "")}>
                                                {item.icon &&
                                                    <item.icon/>
                                                }
                                                <span>{item.title}</span>
                                            </a>
                                        </SidebarMenuButton>
                                    </SidebarMenuItem>
                                ))}
                            </SidebarMenu>
                        </SidebarGroupContent>
                    </SidebarGroup>
                </SidebarContent>
                <SidebarFooter>
                    <SidebarMenu>
                        <SidebarMenuItem>
                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <SidebarMenuButton
                                        size="lg"
                                        className="data-[state=open]:glass-effect-brand data-[state=open]:text-sidebar-accent-foreground"
                                    >
                                        {auth.profile &&
                                            <Avatar className="h-8 w-8 rounded-xl glass-effect-subtle">
                                                {auth.profile?.avatar_url &&
                                                    <AvatarImage
                                                        src={auth.profile?.avatar_url}
                                                        alt={auth.profile?.full_name || ''}
                                                    />
                                                }
                                                <AvatarFallback className="rounded-xl">
                                                    {auth.profile?.full_name?.split(" ").map((name) => name[0]).join("")}
                                                </AvatarFallback>
                                            </Avatar>
                                        }
                                        <div className="grid flex-1 text-left text-sm leading-tight">
                                            <span className="truncate font-semibold">{auth.profile?.full_name}</span>
                                            <span className="truncate text-xs">{auth.user?.email}</span>
                                        </div>
                                        <ChevronsUpDown className="ml-auto size-4"/>
                                    </SidebarMenuButton>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent
                                    className="w-[--radix-dropdown-menu-trigger-width] min-w-56 rounded-2xl glass-effect-brand-lit"
                                    side="bottom"
                                    align="end"
                                    sideOffset={4}
                                >
                                    <DropdownMenuLabel className="p-0 font-normal">
                                        <div className="flex items-center gap-2 px-1 py-1.5 text-left text-sm">
                                            {auth.profile &&
                                                <Avatar className="h-8 w-8 rounded-xl glass-effect-subtle">
                                                    {auth.profile?.avatar_url &&
                                                        <AvatarImage
                                                            src={auth.profile?.avatar_url}
                                                            alt={auth.profile?.full_name || ''}
                                                        />
                                                    }
                                                    <AvatarFallback className="rounded-xl">
                                                        {auth.profile?.full_name?.split(" ").map((name: string) => name[0]).join("")}
                                                    </AvatarFallback>
                                                </Avatar>
                                            }
                                            <div className="grid flex-1 text-left text-sm leading-tight">
                        <span className="truncate font-semibold">
                          {auth.profile?.full_name}
                        </span>
                                                <span className="truncate text-xs">
                          {auth.user?.email}
                        </span>
                                            </div>
                                        </div>
                                    </DropdownMenuLabel>
                                    <DropdownMenuSeparator/>

                                    <DropdownMenuGroup>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account" + (entityContext.queryString ? "?" + entityContext.queryString : ""))}>
                                            <BadgeCheck/>
                                            Account
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account/billing" + (entityContext.queryString ? "?" + entityContext.queryString : ""))}>
                                            <CreditCard/>
                                            Billing
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/analysis/usage" + (entityContext.queryString ? "?" + entityContext.queryString : ""))}>
                                            <BarChart2Icon/>
                                            Usage
                                        </DropdownMenuItem>
                                        <DropdownMenuItem
                                            className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-subtle rounded-xl"
                                            onClick={() => router.push("/customer/account/notifications" + (entityContext.queryString ? "?" + entityContext.queryString : ""))}>
                                            <Bell/>
                                            Notifications
                                        </DropdownMenuItem>
                                    </DropdownMenuGroup>
                                    <DropdownMenuSeparator/>
                                    <DropdownMenuItem
                                        className="hover:cursor-pointer hover:text-sidebar-accent-foreground hover:glass-effect-brand-compliment rounded-xl"
                                        onClick={signOut}>
                                        <LogOut/>
                                        Sign out
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>
                        </SidebarMenuItem>
                    </SidebarMenu>
                </SidebarFooter>
            </Sidebar>
            <SidebarInset>
                <div className="flex flex-col items-center bg-transparent justify-center w-full min-h-screen ">
                    {children}
                </div>
            </SidebarInset>
        </SidebarProvider>
    )
}

// <style jsx>{`
//     .select-trigger {
//         @apply h-8 text-sm;
//     }
// `}</style>
