import { LucideIcon } from 'lucide-react'
import { Card, CardContent } from '@ui/components/ui/card'
import { Gauge } from './gauge'
import React from 'react'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@ui/components/ui/tooltip'
import Link from 'next/link'
import { useWindowSize } from '@react-hookz/web'
import { cn } from '@utils/lib/utils'


export function getRatingColor(rating: number | null, inverse: boolean): string {
    let color = "";
    if (rating === null) return "white";
    if (!inverse) {
        if (rating >= 60) color = "text-brand";
        else if (rating >= 30) color = "text-brand-accent";
        else color = "text-brand-accent";
    } else {
        if (rating >= 60) color = "text-brand-compliment";
        else if (rating >= 30) color = "text-brand-accent";
        else color = "text-brand";
    }
    return color;
}

export function ScoreCard({
                              score,
                              label,
                              icon: Icon,
                              subtext,
                              inverse,
                              color,
                              scoreForColor,
                              scoreTooltip,
                              labelTooltip,
                              subTextTooltip,
                              href,
                              percentage
                          }: {
    score: number,
    label: string,
    icon: LucideIcon,
    subtext?: string,
    inverse: boolean,
    color: boolean,
    scoreForColor: number | null,
    scoreTooltip?: string,
    labelTooltip?: string,
    subTextTooltip?: string,
    href: string,
    percentage?: boolean
}) {
    // Determine glass effect class based on score and inverse
    const getGlassEffectClass = () => {
        if (!color || scoreForColor === null) {
            return "glass-effect-neutral-strong-lit";
        }

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) {
            return "glass-effect-neutral-strong-lit";
        }

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value < 60) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value >= 30) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        }
    };

    // Get text color based on glass effect class
    const getTextColor = () => {
        return "text-white";
    };

    const glassEffectClass = getGlassEffectClass();
    const textColor = getTextColor();

    return (
        <Link href={href}>
            <Card className={cn(
                glassEffectClass,
                "hover:shadow-medium transition-all duration-300 hover:-translate-y-1 shadow-soft rounded-2xl overflow-visible"
            )}>
                <CardContent className="pt-6 relative h-full min-h-24  overflow-visible">
                    <div className="absolute top-2 left-2 p-2 bg-white/20 rounded-full backdrop-blur-sm">
                        <Icon className="w-3 h-3 text-white"/>
                    </div>
                    <div className="flex flex-col items-center absolute top-4 left-12 right-12">
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <div className="text-[10px] w-full lg:text-[12px] xl:text-xs font-medium text-white mb-2">
                                        {label}
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent className={glassEffectClass}>
                                    {labelTooltip || label}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <div className={cn(
                                        "text-2xl text-center w-full font-bold",
                                        textColor
                                    )}>
                                        {typeof score === 'number' ? Math.round(score) : score}{percentage && "%"}
                                    </div>
                                </TooltipTrigger>
                                <TooltipContent className={glassEffectClass}>
                                    {scoreTooltip || 'The score'}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    </div>
                    {subtext && (
                        <TooltipProvider>
                            <Tooltip>
                                <TooltipTrigger asChild>
                                    <p className="text-xs italic text-white/90 absolute bottom-2 text-center right-4 hidden md:block">
                                        {subtext}
                                    </p>
                                </TooltipTrigger>
                                <TooltipContent className="glass-effect-subtle">
                                    {subTextTooltip || subtext}
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>
                    )}
                </CardContent>
            </Card>
        </Link>
    );
}


export function GuageCard({
                              score,
                              label,
                              icon: Icon,
                              subtext,
                              inverse,
                              color,
                              scoreForColor,
                              scoreTooltip,
                              labelTooltip,
                              subTextTooltip,
                              percentage = false
                          }: {
    score: number | null,
    label: string,
    icon?: LucideIcon,
    subtext?: string,
    inverse: boolean,
    color: boolean,
    scoreForColor?: number | null,
    percentage?: boolean,
    scoreTooltip?: string,
    labelTooltip?: string,
    subTextTooltip?: string
}) {
    const windowSize = useWindowSize();

    // Determine glass effect class based on score and inverse
    const getGlassEffectClass = () => {
        if (!color || score === null) {
            return "glass-effect-neutral-strong-lit";
        }

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) {
            return "glass-effect-neutral-strong-lit";
        }

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value < 60) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) {
                return "glass-effect-brand-strong-lit"; // Good (green)
            }
            if (value >= 30) {
                return "glass-effect-brand-alt-strong-lit"; // Medium (yellow)
            }
            return "glass-effect-brand-compliment-strong-lit"; // Bad (red)
        }
    };

    // Determine gradient color for the top border
    const getGradientColor = () => {
        if (!color || score === null) return "bg-brand-gradient";

        const value = scoreForColor || score;

        // Handle neutral scores (around 50%)
        if (value >= 45 && value <= 55) return "bg-gradient-to-r from-slate-600 to-slate-700";

        if (inverse) {
            // For inverse metrics (lower is better)
            if (value < 30) return "bg-brand-gradient"; // Good (green)
            if (value < 60) return "bg-brand-gradient-accent"; // Medium (yellow)
            return "bg-brand-gradient-compliment"; // Bad (red)
        } else {
            // For normal metrics (higher is better)
            if (value >= 60) return "bg-brand-gradient"; // Good (green)
            if (value >= 30) return "bg-brand-gradient-accent"; // Medium (yellow)
            return "bg-brand-gradient-compliment"; // Bad (red)
        }
    };

    const gradientColor = getGradientColor();
    const glassEffectClass = getGlassEffectClass();

    return (
        <Card className={cn(
            glassEffectClass,
            "rounded-2xl shadow-medium overflow-hidden"
        )}>
            <CardContent className="pt-6 relative min-h-[320px]">
                <div className={cn(
                    "absolute top-0 left-0 right-0 h-1",
                    gradientColor
                )}></div>

                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <div className="text-sm font-medium text-center  absolute top-5 left-2 right-2 heading-5">
                                {label}
                            </div>
                        </TooltipTrigger>
                        <TooltipContent className="max-w-72 glass-effect-subtle">
                            {labelTooltip || label}
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>

                {subtext && (
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="text-sm text-center absolute top-14 left-2 right-2">
                                    {subtext}
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-72 glass-effect-subtle">
                                {subTextTooltip ? subTextTooltip : subtext}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                )}
                {Icon && (
                    <div className="p-2 bg-primary/10 rounded-full absolute top-2 right-2 md:hidden lg:block">
                        <Icon className="w-6 h-6 text-primary"/>
                    </div>
                )}

                <div className="absolute top-[80px] left-2 right-2">
                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger asChild>
                                <div className="my-4">
                                    <Gauge
                                        value={score}
                                        size={(windowSize.width > 1024 || windowSize.width < 768) ? "xl" : "lg"}
                                        showValue={true}
                                        percentage={percentage}
                                        color={getRatingColor(scoreForColor || score, inverse)}
                                        label={getRatingText(scoreForColor || score, inverse)}
                                    />
                                </div>
                            </TooltipTrigger>
                            <TooltipContent className="max-w-72 glass-effect-subtle">
                                {scoreTooltip || 'The score'}
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </CardContent>
        </Card>
    );
}


export function getRatingText(rating: number | null, inverse = false): string {
    if (rating === null) return "N/A";
    if (inverse) rating = 100 - rating;

    if (rating >= 80) return "Excellent";
    if (rating >= 60) return "Good";
    if (rating >= 40) return "Average";
    if (rating >= 30) return "Poor";
    if (rating >= 20) return "Very Poor";
    return "Terrible";
}
