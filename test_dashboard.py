import time
from playwright.sync_api import sync_playwright


def test_dashboard():
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()

        # Navigate to the dashboard
        print("Navigating to the dashboard...")
        page.goto("http://localhost:8052/")

        # Wait for the page to load
        page.wait_for_selector("#traceability-table-traceability-table-run-dropdown")
        print("Dashboard loaded successfully")

        # Select a run from the dropdown
        print("Selecting a run from the dropdown...")
        page.click("#traceability-table-traceability-table-run-dropdown")
        page.wait_for_selector(".Select-menu-outer", timeout=60000)

        # Wait a bit for the dropdown to fully render
        time.sleep(2)

        # Click the first run option
        page.click(".Select-option:first-child", timeout=60000)
        print("Run selected")

        # Wait for the flag dropdown to be populated
        time.sleep(2)  # Give some time for the callback to complete

        # Check if the flag dropdown is populated
        print("Checking if the flag dropdown is populated...")
        page.click("#traceability-table-traceability-table-flag-dropdown")

        # Wait a bit longer for the flag dropdown to be populated
        time.sleep(5)

        try:
            # Wait for the dropdown menu to appear
            page.wait_for_selector(".Select-menu-outer", timeout=60000)

            # Count the number of flag options
            flag_options = page.query_selector_all(".Select-option")
            print(f"Found {len(flag_options)} flag options")

            # Click the first flag option if available
            if len(flag_options) > 0:
                flag_options[0].click()
                print("Flag selected")
            else:
                print("No flag options found in the dropdown")
        except Exception as e:
            print(f"Error when checking flag dropdown: {e}")
            # Take a screenshot to see what's happening
            page.screenshot(path="flag_dropdown_error.png")
            print("Screenshot saved as flag_dropdown_error.png")

        # Wait to see the results
        time.sleep(5)

        # Close the browser
        browser.close()

if __name__ == "__main__":
    test_dashboard()
