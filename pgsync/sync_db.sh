#!/bin/sh -x
XFER_TABLES='xfer_*'
if [ -f ../.env ]; then
  export $(cat ../.env | xargs)
fi
POSTGRES_URL=${POSTGRES_URL:-postgres://${POSTGRES_USER}:${POSTGRES_PASSWORD}@${POSTGRES_HOST}:${POSTGRES_PORT}/${POSTGRES_DB}}
SUPABASE_URL=${SUPABASE_URL:-postgres://${SUPABASE_USER}:${SUPABASE_PASSWORD}@${SUPABASE_HOST}:${SUPABASE_PORT}/${SUPABASE_DB}}

if [ -f .pgsync.yml ]; then
  echo "Using existing .pgsync.yml"
else
  echo "from: $POSTGRES_URL" >.pgsync.yml
  echo "to: $SUPABASE_URL" >>.pgsync.yml
  echo "to_safe: true" >>.pgsync.yml
fi
pgsync "${XFER_TABLES}" --jobs 1 --disable-user-triggers
sleep 300
