description: Flags
prompts:
  - flag-prompt.txt
providers:
  - id: openai:gpt-4o
    config:
      max_tokens: 4096

  - id: openai:o1-mini
    config:
      max_tokens: 4096

  - google:gemini-2.0-flash-exp

  - id: anthropic:messages:claude-3-5-sonnet-latest
    config:
      max_tokens: 4096

  - id: anthropic:messages:claude-3-5-haiku-20241022
    config:
      max_tokens: 4096

  - id: openai:gpt-4o-mini
    config:
      max_tokens: 4096

  - id: openai:gpt-4o-mini-2024-07-18:eko-intelligence:flag-experiment-2:AaSF6mN2
    config:
      max_tokens: 4096

  - id: openai:nvidia/Llama-3.1-Nemotron-70B-Instruct
    config:
      apiKey: q9m0BNyBATp41nKBdLzsqjdVP0cFbGgD
      apiBaseUrl: https://api.deepinfra.com/v1/openai

  - anthropic:messages:claude-3-5-haiku-latest
  - anthropic:messages:claude-3-5-sonnet-latest
  - ollama:chat:qwen:32b
  - ollama:chat:qwen:32b


tests:
#  - file://pwc.yaml
#  - file://un_women.yaml
  - file://anglian.yaml
#  - file://synthetic1.yaml
