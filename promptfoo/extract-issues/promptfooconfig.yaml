description: Statement and Action Extraction Prompt
prompts:
  - extract-issues.md
#  - improved-prompt.txt
#  - prompt-generated-by-claude.txt
providers:
  - id: openai:gpt-4o
    config:
      max_tokens: 4096

  - id: openai:gpt-4o-mini
    config:
      max_tokens: 4096

  - anthropic:messages:claude-3-5-haiku-latest
  - anthropic:messages:claude-3-5-sonnet-latest
  - id: google:gemini-1.5-flash-8b-latest
  - id: google:gemini-1.5-flash-latest
  - id: google:gemini-1.5-pro
  - groq:gemma2-9b-it
#  - id: groq:llama-3.2-3b-preview
#    config:
#      max_tokens: 8192
  - ollama:chat:llama3
#  - id: openai:chat:LLaMA_CPP
#    config:
#      apiBaseUrl: http://localhost:8080/v1
#      max_tokens: 8192
tests:
  - file://test1.yaml
  - file://test2.yaml
