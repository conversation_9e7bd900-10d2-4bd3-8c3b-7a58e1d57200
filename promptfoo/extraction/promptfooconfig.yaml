description: Statement and Action Extraction Prompt
prompts:
  - flag-prompt.txt
#  - improved-prompt.txt
#  - prompt-generated-by-claude.txt
providers:
  - id: openai:gpt-4o
    config:
      max_tokens: 4096
#  - openai:gpt-4o-mini
#  - google:gemini-1.5-pro-exp-0827

  #  - groq:llama3-groq-70b-8192-tool-use-preview
#  - anthropic:messages:claude-3-haiku-20240307
  - id: anthropic:messages:claude-3-5-sonnet-20240620
    config:
      max_tokens: 4096
#  - groq:gemma2-9b-it
#  - ollama:chat:llama3
tests:
  - file://pwc.yaml
  - file://un_women.yaml
  - file://anglian.yaml
#  - file://synthetic1.yaml
