# Information Extraction Task

## Objective
Extract key information from the given text, focusing on actions, impacts, and various types of statements made by entities.

## Information Types
1. **Action**: Something an entity has done or is doing.
   - Example: "Barclays implemented new policies."

2. **Impact**: An action with a stated consequence.
   - Example: "Barclays' initiative reduced carbon emissions by 20%."

3. **Statement**: A general declaration or remark.
   - Example: "The CEO commented on market trends."

4. **Assertion**: A statement presented as a fact.
   - Example: "Our company is the industry leader in sustainability."

5. **Prediction**: An expectation of future events or actions.
   - Example: "We anticipate market growth in the next quarter."

6. **Intention**: A plan or desire for future action, without firm commitment.
   - Example: "We aim to expand our operations globally."

7. **Commitment**: A promise of future action.
   - Example: "We pledge to achieve carbon neutrality by 2030."

## Output Format
Provide results as a JSON array of objects. Each object should have these fields:
- `company`: Full legal name of the company (if applicable), or null
- `entity`: Name of the entity making the statement or taking the action
- `type`: One of the information types listed above
- `text`: The full extracted text
- `named_entities`: Array of proper nouns mentioned (e.g., locations, people)

## Example Output
```json
[
  {
    "company": "PricewaterhouseCoopers International Limited",
    "entity": "PwC",
    "type": "commitment",
    "text": "We are committed to reducing pollution in Africa",
    "named_entities": ["Africa"]
  }
]
```

## Instructions
1. Analyze the provided text carefully.
2. Identify relevant information based on the defined types.
3. Structure the information into the specified JSON format.
4. Ensure the output is a valid JSON array with no additional text or markup.
5. Use the full legal entity name for the `company` field when applicable.
6. Include all relevant named entities in the `named_entities` array.

The response should be valid JSON array of objects with no other text or markup, it should not start ```json for example.


Now, please process the following text and extract the required information:

{{text}}
