- vars:
    title: "A Global Women’s Safety Framework in Rural Spaces: Informed by Experience in the Tea Sector"
    authors: {"{\"name\": \"UN Women\", \"type\": \"organisation\"}","{\"name\": \"Unilever\", \"type\": \"organisation\"}"}
    date: 2021
    text: >
      
      ## GWSF Strategic Areas of Action: What Can Be Done? 
  
      In reviewing the GWSF action areas, it is important to note that the last three outcome areas are not implemented chronologically. Activities undertaken in the first outcome area (e.g., local assessment, design process, etc.) by a producer in their journey on women’s safety will greatly assist them and their partners in identifying where and when to focus in those areas.
  
      Not all action areas are undertaken at the same time, as this will depend on strong leadership, the level of resources allocated over time, and the support and engagement of partners working within their area of influence to contribute to the expected results of the initiative.
  
      As part of their journey, producers will need to identify entry points to strengthen their work on women and girls’ safety and determine the scope and scale of their interventions as part of the design process.
  assert:
    - type: icontains
      value: UN Women


    - type: icontains
      value: "will need to identify"

    - type: contains-json
    - type: javascript
      value: "JSON.parse(output).statements.length >= 3"
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => !item.company).length == JSON.parse(output).length'
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => item.entity?.startsWith("UN Women")).length == JSON.parse(output).length'
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => item.statement_types?.includes("prediction")).length == 0'
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => item.statement_types?.includes("commitment")).length > 0'
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => item.statement_types?.includes("intention")).length > 0'
    - type: javascript
      value: 'JSON.parse(output).statements.filter(item => item.category === "action").length == 0'
