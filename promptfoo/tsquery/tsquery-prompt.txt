<info>A tsquery value stores lexemes that are to be searched for, and can combine them using the Boolean operators & (AND), | (OR), and ! (NOT), as well as the phrase search operator <-> (FOLLOWED BY). There is also a variant <N> of the FOLLOWED BY operator, where N is an integer constant that specifies the distance between the two lexemes being searched for. <-> is equivalent to <1>.

Parentheses can be used to enforce grouping of these operators. In the absence of parentheses, ! (NOT) binds most tightly, <-> (FOLLOWED BY) next most tightly, then & (AND), with | (OR) binding the least tightly.

</info
<instructions>
Please provide a text phrase to search our documents using to_tsquery(). Please use the format specified. We are searching for any information that **DISPROVES** the ESG claim '{{text}}'
so please provide a search that would match any text that disproves the claim.

Only return the phrase and no other text whatsoever.

Step 1. Think of ways that the claim could be disproved put this in <thoughts></thoughts> tags.
Step 2. Turn these thoughts into a list of possible phrases that could be found in articles and reports, put these between the <phrases></phrases> tags.
Step 3. Return a tsquery that would match these phrases, in the format specified above, between <query></query> tags.
</instructions>

<example>
<thoughts>(1) The company could have a board with less than 50% women. (2) There could be mention of a lack of diversity in the workforce. (3) Information could state that the board composition is predominantly male. (4) It might be noted that the board is not diverse in gender terms. (5) There might be data or statements explicitly stating that women are underrepresented on the board.</thoughts>

<phrases>(1) "less than 50% women on board" (2) "lack of diversity workforce" (3) "board predominantly male" (4) "board not diverse gender" (5) "women underrepresented board"</phrases>

<query>!("diverse workforce" & "50% women board") & ("less than 50% women on board" | "lack of diversity workforce" | "board predominantly male" | "board not diverse gender" | "women underrepresented board")</query>
</example>

<example>
<thoughts>1) Could mention low percentage of women on board 2) Could state male-dominated board 3) Could indicate insufficient female representation 4) Could show actual numbers below 50% 5) Could mention gender imbalance on board</thoughts>
<phrases>1) "women make up 30% of board" 2) "male dominated board leadership" 3) "insufficient female board members" 4) "only 2 women on 10 person board" 5) "gender imbalance board members"</phrases>
<query>("board" <-> "male" | "women" <2> "30%" | "insufficient" <-> "female" | "gender" <-> "imbalance" | "only" <2> "women" <2> "board") & !("50%" <2> "women")</query>
</example>
