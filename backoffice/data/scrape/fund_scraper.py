import json
import os
import sys

import fitz  # PyMuPDF
import requests
from dotenv import load_dotenv
from jsonpointer import resolve_pointer
from openai import OpenAI

# Load environment variables from .env file
load_dotenv()
google_api_key = os.getenv('GOOGLE_API_KEY')
google_cse_id = os.getenv('GOOGLE_CSE_ID')
# Set your OpenAI API key
client = OpenAI(
    # This is the default and can be omitted
    api_key=os.environ.get("OPENAI_API_KEY"),
)

def search_pdfs(query):
    payload = {
        'source': 'google_search',
        'query': query,
        'parse': True,
        'pages': 1,
    }

    response = requests.request(
        'POST',
        'https://realtime.oxylabs.io/v1/queries',
        auth=('neilellis_jOO8H','ubBZX2zfw2quh0kzh'),
        json=payload,
    )

    try:
        # Get the 'organic' array
        organic_results = resolve_pointer(response.json(), '/results/0/content/results/organic')

        if not isinstance(organic_results, list):
            raise ValueError("The 'organic' element is not a list as expected")

        # Extract URLs for all items in the 'organic' array
        urls = []
        for index, item in enumerate(organic_results):
            try:
                url = item['url']
                if not url.endswith('.pdf'):
                    continue
                urls.append(url)
                print(f"Item {index}: {url}")
            except KeyError:
                print(f"Item {index}: No URL found")

        print(f"\nTotal URLs extracted: {len(urls)}")
        print(urls)
        return urls

    except (json.JSONDecodeError, KeyError, IndexError, ValueError) as e:
        print(f"Error processing JSON: {e}")



def download_pdf(url, output_path):
    """Download PDF from a URL."""
    headers = {
        "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
    }
    response = requests.get(url, headers=headers)
    with open(output_path, 'wb') as f:
        f.write(response.content)

def extract_text_from_pdf(pdf_path):
    """Extract text from a PDF file."""
    text = ""
    doc = fitz.open(pdf_path)
    for page in doc:
        text += page.get_text()
    return text

def analyze_text_with_openai(text):
    """Analyze text with OpenAI to find holdings and purpose of the fund."""
    response =  client.chat.completions.create(
        model="gpt-4o",
        messages=[
            {"role": "system", "content": "You are a financial analyst."},
            {"role": "user", "content": f"Analyze the following text to find all the holdings and the purpose of the fund and return the result as a json object:\n\n{text}"}
        ]
    )
    return response.choices[0].message

def main():
    fund_name =  sys.argv[1] + " " + sys.argv[2]    # Step 1: Search for PDFs
    pdf_urls = search_pdfs(fund_name+" report pdf")
    pdf_urls.extend(search_pdfs(sys.argv[2]+" report pdf"))
    pdf_urls.extend(search_pdfs(sys.argv[1]+" report pdf"))
    pdf_urls= list(set(pdf_urls))
    print(pdf_urls)


    # Step 2: Download PDFs and extract text
    for i, url in enumerate(pdf_urls):
        pdf_path = f"files/fund_{sys.argv[1]}_{i}.pdf"
        download_pdf(url, pdf_path)
        text= extract_text_from_pdf(pdf_path)
        analysis_result = analyze_text_with_openai(text)
        print("Analysis Result:")
        print(analysis_result)
        # os.remove(pdf_path)  # Clean up the PDF file after extracting text

    # # Step 3: Analyze extracted text with OpenAI


# Example usage
if __name__ == "__main__":

    main()
