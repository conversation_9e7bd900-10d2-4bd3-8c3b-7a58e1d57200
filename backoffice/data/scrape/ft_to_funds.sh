echo "name,isin,eko_id,url,fund_type,income_treatment,ms_category, ima_sector,launch_date,currency,domicile,manager, ft_symbol" > csv/funds.csv
for file in results/*.json; do
    jq -r '[.name, .id, .ekoId, .url, .profile.fundType, .profile.incomeTreatment, .profile.morningstarCategory, .profile.imaSector, .profile.launchDate, .profile.priceCurrency, .profile.domicile, .profile.manager, .ftSymbol] | @csv' $file >> csv/funds.csv
done
