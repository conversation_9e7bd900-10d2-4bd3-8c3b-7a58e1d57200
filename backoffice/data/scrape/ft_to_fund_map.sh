echo "fund,holding,weight" > csv/holding_map.csv
for file in results/*.json; do
    jq -r --arg ISIN $(jq -r ".id" $file) '.holdings[] | ["eko:sec:isin:\($ISIN)",if .symbol then "eko:sec:ticker:\(.symbol)" else "eko:sec:name:\(.name| ascii_downcase)" end, .weight] | @csv' $file >> csv/holding_map.tmp.csv
done

cat csv/holding_map.tmp.csv | grep -v '^,*$'| sort -u >> csv/holding_map.csv
rm csv/holding_map.tmp.csv
