curl -X GET "https://www.us-api.morningstar.com/sal/sal-service/fund/portfolio/holding/v2/GB00BPBRBX43/data?premiumNum=1000&freeNum=1000&languageId=en-GB&locale=en-GB&clientId=EC&benchmarkId=category&component=sal-mip-holdings&version=4.13.0" \
-H "Accept: */*" \
-H "Accept-Encoding: gzip, deflate, br, zstd" \
-H "Accept-Language: en-GB,en-US;q=0.9,en;q=0.8" \
-H "Authorization: Bearer eyJhbGciOiJSUzI1NiIsInR5cCI6IkpXVCIsImtpZCI6Ik1EY3hOemRHTnpGRFJrSTRPRGswTmtaRU1FSkdOekl5TXpORFJrUTROemd6TWtOR016bEdOdyJ9.**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.AgAT5_cVX1tQgfhaFg1wZczQGC59bXSCp4lt9vxp_Gib1Zxok3T-VqSsz956NjNjJv0orE7Mu42L0zewFEOPtNOmTVheJC2iAZS0jswW4hGKeT1wF5EvcDW_fog4EPVpn6JOi6dxmziMo1gIKt6rRqfnohZOyT5ogj4o8wyg-3Gbr0PiBS62p99h-ENKuMV2K3DLazk9skrn_0stIEsiPArfSrrbUofb6stygC0wtRQDESPxflblyMnF4kK2_kfn51TbT4N8yJ-4Vdb90uOGmSDWOr6tlXIQrVDT0ZSa7JNbLkTBNWcDCMuCrAQ-3f7ZKW7t3lEkQTFVMZEdaneMMA" \
-H "Cache-Control: no-cache" \
-H "Credentials: omit" \
-H "Origin: https://www.morningstar.co.uk" \
-H "Pragma: no-cache" \
-H "Priority: u=1, i" \
-H "Referer: https://www.morningstar.co.uk/" \
-H "Sec-Ch-Ua: \"Not/A)Brand\";v=\"8\", \"Chromium\";v=\"126\", \"Google Chrome\";v=\"126\"" \
-H "Sec-Ch-Ua-Mobile: ?0" \
-H "Sec-Ch-Ua-Platform: \"macOS\"" \
-H "Sec-Fetch-Dest: empty" \
-H "Sec-Fetch-Mode: cors" \
-H "Sec-Fetch-Site: cross-site" \
-H "User-Agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36" \
-H "X-Api-Realtime-E: eyJlbmMiOiJBMTI4R0NNIiwiYWxnIjoiUlNBLU9BRVAifQ.w_Asp1SC3rpVTFxFteM-nW7elbQQUf1gaJ9022ZxGEnDjiJNLk5IX5d05tmUo2n5mvjSUEutNerI6vQ5M8MzKZF2J9_CLz0Q8bV-Ap2NxanoHiTGTFHh7ZBmNqxs5r45ByjxdwWvcTX9rrSNzm6TmCu2T3TcqxCO5j5SU2QEV2o.iresnqY1u5ESb9Xb.2HrxVPSCqEw9PwLsKKeoyEYZiWzq5OEzcNhgt__VllZRYDcp9iW-uq9qu_Ze35-DejBnW0jyr0YHWj0VfPHdRRbMQBjKu4pVQnBMg44MTQRQhKGYo-2p7zaJQEQjUKs5D9ZtmqM4fGtMGU5gawYbpyV1JoHrBCYW0HU3smZzoFdUpDrkjMqfyMN-T-dkTQk7QqMt5CsposMPzmKpJK_I5fHCpW2POADiOWvvw6c.LWLoSQX8YTvoTVq9HAkcTw" \
-H "X-Api-Requestid: 0390a8a1-5ace-9dc4-b304-52d7fbce2b15" \
-H "X-Sal-Contenttype: nNsGdN3REOnPMlKDShOYjlk6VYiEVLSdpfpXAm7o2Tk="
