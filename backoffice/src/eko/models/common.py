import re
from enum import Enum
from typing import Annotated, Literal, Union

from loguru import logger
from pydantic import AfterValidator


# Type validators
def validate_eko_id(value):
    if not value.startswith('eko:'):
        raise ValueError('EkoId must start with eko:')


EkoId = Annotated[str, AfterValidator(validate_eko_id)]
ScoreType = int
YearType = int
ResearchType = Literal["impact", "journalism", "scientific", "disclosure", "regulatory"]
ActionType = Literal[
    "operational", "strategic", "financial", "regulatory", "marketing",
    "ecological", "social", "workforce", "technological", "product",
    "disaster", "lobbying", "compliance", "collaborative", "philanthropic",
    "disciplinary", "transgressive", "corrective", "investigative"
]

OldStatementType = Literal[
    "assertion", "prediction", "intention", "commitment", "assumption",
    "declaration", "directive", "evaluation", "recommendation", "apology",
    "inquiry", "permission", "denial", "hypothesis", "proposition"
]

LocationType = Literal[
    'place', 'street', 'neighborhood', 'city', 'zone', 'region', 'country', 'bloc', 'continent', 'global', 'natural']

_SpecificEntityTypes = Literal[
    # Organizations
    "company",  # Commercial entities
    "organisation",  # Non-profits, NGOs, associations
    "government",  # Government bodies, agencies
    "institution",  # Educational, research, healthcare facilities
    "department",  # Departments within organizations
    # People and Groups
    "person",  # Individual people
    "group_of_people",  # Groups of people
    "role",  # Job titles, positions
    "demographic",  # Groups defined by common characteristics
    # Life
    "animal",  # Animals, wildlife
    "plant",  # Plants, flora
    # Places
    "country",  # Countries
    "city",  # Cities, towns, villages
    "location",  # General locations
    "facility",  # Buildings, infrastructure
    "geo_region",  # Geographic regions
    "bloc",  # A group of countries
    "region",  # States, provinces
    "continent",  # Continents
    "ocean",  # Oceans, seas
    "water_body",  # Lakes, rivers, etc.
    "road",  # Highways, streets
    "landmark",  # Notable landmarks, monuments
    "transport_line",  # Transportation lines (e.g., railways) or flight numbers
    "biome",  # Biomes, ecosystems
    # Objects
    "material",  # Materials, substances
    "vehicle",  # Vehicles, vessels, ships and planes
    "weather",  # Weather phenomena
    # Commercial
    "brand",  # Brand names
    "product",  # Products, services
    "asset",  # Patents, trademarks, properties
    "service",  # Services
    "sector",  # Sectors, industries
    "market",  # Markets, exchanges
    # Finance
    "security",  # Financial instruments
    "fund",  # Collection of investments
    "monetary",  # Money, financial transactions
    # Time-related
    "date",  # Specific dates
    "time_period",  # Periods of time
    "event",  # Events, occurrences
    "recurring_date",  # Recurring dates (e.g., holidays, birthdays)
    # Abstract
    "concept",  # Abstract ideas, theories
    "number",  # Numbers, quantities
    "law",  # Laws, regulations
    "policy",  # Policies, guidelines
    "method",  # Methods, techniques
    "system",  # Systems, frameworks
    "process",  # Processes, procedures
    "problem",  # Problems
    "solution",  # Solutions, remedies
    "project",  # Projects, undertakings
    "standard",  # Standards, benchmarks
    "strategy",  # Strategies, plans
    "bias",  # Biases, prejudices
    "belief",  # Beliefs, convictions
    "paper",  # Academic papers, articles
    "course",  # Courses, educational programs
    "award",  # Awards, Certifications, accreditations, recognitions
    "competition",  # Competitions, contests
    "hashtag",  # Social media hashtags
    "technology",  # Technologies, inventions
    "resource",  # Resources, assets
    "document",  # Documents, reports
    "profession",  # Professions, occupations
    # Cultural
    "nationality",  # National identities
    "religion",  # Religious entities
    "political",  # Political entities
    "creative_work",  # Creative works
    # Other
    "other",  # Fallback category
]
EntityType = str

entity_types = set(_SpecificEntityTypes.__args__)  # pyright: ignore [reportArgumentType]

GreenWashingType = Literal["vague", "misleading", "offsetting", "intentions", "distracting", "org", "fund", "product"]

# Description texts for models
impact_description = """The impact of the flag from 0 to 100 from a CSV file:

Red Flags,,,
,,,
Percentage,Non-Human,Human,Ecological
0,No impact,No impact,No impact
10,Very minor discomfort to a few animals,Minor discomfort to a few individuals,Slight environmental disturbance
20,Minor discomfort to several animals,Moderate discomfort to several individuals,Noticeable environmental impact
30,Moderate discomfort to many animals,Significant discomfort or stress to many individuals,Notable environmental damage
40,Significant stress to a local animal population,Considerable mental/emotional harm or discrimination affecting a community,Substantial environmental harm
50,Animal death,Severe mental/emotional suffering or widespread discrimination,Major environmental damage
60,Multiple animal deaths,Minor physical harm to individuals or severe economic hardship for a community,Critical environmental damage
70,Large number of animal deaths,Moderate physical harm to many individuals,Destruction of local ecosystems
80,Extinction of a local animal species,Severe physical harm to a large population,Widespread environmental devastation
90,Extinction of multiple animal species,Loss of life on a significant scale,Irreversible damage to large ecosystems
100,Mass extinction event,Global catastrophe threatening all human life,Global catastrophe threatening all life on Earth
,,,
,,,
Green Flags,,,
,,,
Percentage,Non-Human,Human,Ecological
0,No impact,No impact,No impact
10,Very minor benefit to a few animals,Minor positive effect on a few individuals,Slight environmental improvement
20,Minor benefit to several animals,Moderate benefit to several individuals,Noticeable environmental enhancement
30,Moderate improvement for many animals,Significant improvement in well-being for many individuals,Notable environmental restoration
40,Significant improvement for a local animal population,Considerable reduction in discrimination or mental/emotional distress for a community,Substantial environmental conservation
50,Major improvement in animal welfare,Major improvement in quality of life or reduction of suffering for a large group,Significant ecosystem restoration
60,Substantial improvement in animal welfare,Tangible physical health benefits or economic upliftment for a community,Critical habitat preservation
70,Prevention of mass animal suffering or death,Substantial improvement in physical and mental health for many,Regeneration of local ecosystems
80,Significant positive impact on multiple species,Transformative positive change for a large population,Widespread environmental rejuvenation
90,Preservation of endangered species,Life-changing benefits on a significant scale,Major positive shift in global environmental trends
100,Global improvement in animal welfare and biodiversity,Global transformation leading to unprecedented human well-being and societal progress,Environmental harmony and ecological balance on a global scale
"""

authentic_description = """

# Score 0 to 100

- 100 is a 100% authentic voluntary attempt to solve real world problems.
- 0 is an exercise in marketing or deflection from real problems the entity is causing. This behavior typically characterizes greenwashing, where an entity undertakes actions that sound important but have minimal actual impact despite having the capacity to make a authentic difference.


Look for:

- **Regulatory Compliance:** Actions taken solely to meet legal requirements i.e. initiatives presented as positive efforts when the entity had no real choice or commitment.
- **Deceptive Intent:** Actions are likely aimed at marketing or greenwashing rather than making a meaningful impact.
- **Minimal Effort:** The entity's actions are superficial or tokenistic, with little authentic commitment.
- **Resource Allocation:** The entity invests minimal resources or effort in the initiative.
- **Disproportionate Scale:** The entity's resources far exceed the initiative's impact, indicating a lack of authentic commitment.
- **Misleading Communication:** The entity's messaging exaggerates the initiative's impact or importance.
- **Lack of Transparency:** The entity fails to provide clear information about the initiative's goals, methods, or outcomes.
- **Inconsistent Behavior:** The entity's actions contradict its stated values or commitments.
- **Actual Impact vs Description ** The difference between the actual impact and the entity's description of its actions.



### Green Flags

**Low Authentic Values:**
- **Regulatory Compliance:** Actions taken solely to meet legal requirements i.e. initiatives presented as positive efforts when the entity had no real choice or commitment.
- **Deceptive Intent:** Actions are likely aimed at marketing or greenwashing rather than making a meaningful impact.
- **Minimal Effort:** The entity's actions are superficial or tokenistic, with little authentic commitment.
- **Resource Allocation:** The entity invests minimal resources or effort in the initiative.
- **Disproportionate Scale:** The entity's resources far exceed the initiative's impact, indicating a lack of authentic commitment.
- **Misleading Communication:** The entity's messaging exaggerates the initiative's impact or importance.
- **Lack of Transparency:** The entity fails to provide clear information about the initiative's goals, methods, or outcomes.
- **Inconsistent Behavior:** The entity's actions contradict its stated values or commitments.

**High Authentic Values:**
- **Voluntary and Authentic Efforts:** The entity has taken proactive, authentic, and transformative steps to address the issue.
- **Significant Resource Allocation:** A reasonable percentage of the entity's resources is dedicated to the initiative.
- **Primary Contributor:** The entity is a main or sole contributor, not acting merely to satisfy regulatory requirements.

---

### Red Flags

**High Authentic Values:**
- **Intentional Negative Actions:** The entity knowingly and maliciously engages in activities that cause harm.

**Low Authentic Values:**
- **Unintentional or Indirect Negative Actions:** The entity is involved in negative impacts accidentally or indirectly, without intent.

---

### Examples

1. **Barclays – Minimal Impact Initiative**
   - **Action:** Participates in an initiative to reduce fossil fuels by providing only educational resources with minimal funding but claims it as a significant step.
   - **Authentic Score:** 10%
   - **Rationale:** Deceptive intent and minimal effort despite significant resources.

2. **Barclays – Direct Investment in Fossil Fuels**
   - **Action:** Intentionally funds fossil fuel companies as a direct investor.
   - **Authentic Score:** 100%
   - **Rationale:** Directly contributes to the problem with full knowledge and intent.

3. **Shell – Green Fuel Branding**
   - **Action:** Sells a small amount of biofuel while continuing to produce and sell large quantities of fossil fuels.
   - **Authentic Score:** 30%
   - **Rationale:** Misleading communication and minimal effort compared to the scale of the problem.
"""

contribution_description = """

### Rating of an Entity's Contribution

# Score 0 to 100

The **contribution** score measures the actual impact of an entity's actions, typically a company, on a specific issue.
This score is **independent** of the issue's inherent impact or the entity's intent. It focuses solely on the entity's contribution. It evaluates:

1. **Impact:** The real effect of the entity's involvement, not the impact of the problem itself.
2. **Proportionality:** The size of the impact relative to the entity's capacity and resources.
3. ** Scope ** How much of the problem the entity is addressing.

**Key Principles:**
- **Scale Matters:** A small initiative (e.g., rewilding one acre) by a large multinational corporation will have a **low impact score** due to the company's extensive resources. Conversely, the same initiative by a small company with limited resources will receive a **high impact score**.
- **Relative Impact:** Small actions by smaller companies are weighted more heavily than similar actions by larger companies.

---

### Green Flags

**Low Contribution Values:**
- **Nominal or Trivial Impact:** The entity's actions are insignificant compared to the overall problem.
- **Minimal Resource Allocation:** The entity invests a small percentage of its resources in the initiative.
- **Marginal Scope:** The initiative addresses only a small part of the issue.
- **Lack of Impact:** The initiative has little to no effect on the issue at hand.
- **Inadequate Scope:** The initiative is too small or limited to address the issue effectively.

**High Contribution Values:**
- **Significant Resource Allocation:** A reasonable percentage of the entity's resources is dedicated to the initiative.
- **Primary Contributor:** The entity is a main or sole contributor, not acting merely to satisfy regulatory requirements.

---

### Red Flags

**High Contribution Values:**
- **Primary Contributor to Negative Impact:** The entity is a major contributor to the problem, no matter what their intention is.

**Low Contribution Values:**
- **Unintentional or Indirect Negative Actions:** The entity is involved in negative impacts accidentally or indirectly, without intent.

---

### Examples

1. **Barclays – Minimal Impact Initiative**
   - **Action:** Participates in an initiative to reduce fossil fuels by providing only educational resources with minimal funding.
   - **Contribution Score:** 10%
   - **Rationale:** Limited impact on global warming despite nominal participation.

2. **Barclays – Direct Investment in Fossil Fuels**
   - **Action:** Funds fossil fuel companies as a direct investor.
   - **Contribution Score:** 60%
   - **Rationale:** Although Barclays does not directly emit greenhouse gases, its financial support actively enables fossil fuel operations which are a major contributor to global warming.

3. **Shell – Continued Fossil Fuel Supply**
   - **Action:** Continues to supply fossil fuels, directly contributing to global warming.
   - **Contribution Score:** 80%
   - **Rationale:** As a major contributor Shell significantly impacts global warming.
"""

entity_type_description = f"""
Highest priorities are **company**, **person**, **organisation**, other categories should be used only if those don't apply: {entity_types}
"""


class RunType(Enum):
    HISTORICAL = "hist"
    HISTORICAL_INCREMENTAL = "hist-inc"
    COMPARATIVE = "comp"
    INCREMENTAL = "inc"
    FULL = "full"
    CLAIMS = "claims"
    PROMISES = "promises"
    CMS = "cms"


def name_to_eko_id(type: EntityType, name: str):
    if type not in entity_types:
        logger.warning(f"Invalid entity type {type}")
        name_subbed = "other"
    else:
        name_subbed = re.sub("_+$", "", re.sub(r"\W+", "_", name.lower()))
    return f'eko:{type}:name:{name_subbed}'


if __name__ == "__main__":
    print(entity_types)
