from datetime import datetime
from pydantic import BaseModel, Field, conint
from typing import List, Literal, Union

from eko.models.common import YearType, ResearchType
from eko.models.simple_entity import Author


class Report(BaseModel):
    """
    Represents a document or report in the system.
    """
    score: int= Field(..., ge=0, le=100,
        description="The score of the report, from 0 to 100 where 0 is not related to any ESG issues and 100 is a perfect score")
    title: str = Field(description="The title of the document")
    year: YearType = Field(datetime.now().year, description=f"The year the document was published, please try and deduce from the information available or if not assume this year {datetime.now().year}")
    publish_date: Union[None, datetime] = Field(description="The exact date the document was published, if known")
    extract: str = Field(description="An approximately 250 word summary of the document")
    authors: List[Author] = Field(description="The authors of the document")
    researchScope: Literal["company", "industry", "city", "country", "region", "global", "other"] = Field(
        description="The scope of the document")
    researchTargets: List[str] = Field(description="The entities that are the subject of the document")
    researchCategories: List[ResearchType] = Field(
        description="""The categories of the document:
    - "impact" Reports or articles provided by third-party pressure groups or NGOs that reports on ESG related matters regarding the entity.
    - "journalism": Reports or articles by media outlets or journalists including news articles, opinion pieces, and investigative journalism.
    - "scientific": Reports or articles by scientific institutions or scientists including papers, studies, and research.
    - "disclosure": Reports or articles by the entity itself or on behalf of the entity. This includes press releases, fluff pieces, audits, annual reports, sustainability reports, and other disclosures by the entity or on behalf of the entity. A disclosure is a document that is partial to the entity and is not an independent analysis.
    - "regulatory": Reports or articles by regulatory bodies or governments including laws, regulations, and official reports.""")
    credibility: int= Field(..., ge=0, le=5,
        description="The credibility of the document from 0 to 5, 0 (untrusted rubbish from social media) to 5 (100% reliable from an established unbiased source)")
