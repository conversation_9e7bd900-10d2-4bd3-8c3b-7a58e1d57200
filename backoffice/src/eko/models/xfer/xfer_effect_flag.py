from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any

from eko.models.citation_model import Citation
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.demise.demise_model import DEMISEModel


class XferEffectFlagModel(BaseModel):
    """
    A model class for transferring effect flag data from the analytics database
    to the customer database with all relevant data bundled as JSON.
    """
    id: int = Field(..., description="The database ID of this effect flag")
    entity_xid: str = Field(..., description="Short ID of the entity this effect relates to")
    flag_type: str = Field(..., description="Type of flag (red=negative, green=positive)")
    flag_title: str = Field(..., description="Title of the flag")
    flag_short_title: str = Field(..., description="A concise 2-3 word title suitable for use as a label in UI elements like badges")
    year: int = Field(..., description="Year of the flag")
    start_year: Optional[int] = Field(None, description="Start year of the action/event")
    end_year: Optional[int] = Field(None, description="End year of the action/event")
    score: float = Field(..., description="Overall score of the flag")
    impact: int = Field(..., description="Impact rating (0-100)")
    authentic: int = Field(..., description="Authenticity rating (0-100)")
    contribution: int = Field(..., description="Contribution rating (0-100)")
    confidence: int = Field(..., description="Confidence in this flag (0-100)")
    credibility: int = Field(0, description="Credibility score (0-100) based on the source report's credibility and domain credibility")

    # Detailed content fields
    flag_summary: Optional[str] = Field(None, description="Summary of the flag analysis")
    flag_analysis: Optional[str] = Field(None, description="Detailed analysis of the flag")
    domains: List[str] = Field(default_factory=list, description="Domains this flag relates to")

    # Citations and statements
    citations:List[Citation] = Field(default_factory=list, description="Citation information for the flag")
    flag_statements: List[StatementAndMetadata] = Field(default_factory=list, description="Statement information for the flag")

    # Impact analysis data
    impact_value_analysis:Dict[str, Any] = Field(default_factory=dict, description="Analysis of impact values")

    # Model section mappings - one section per model
    model_sections: Dict[str, str] = Field(default_factory=dict, description="Mapping of model name to section ID, one section per model")

    # Full model data including DEMISE vectors and other metrics
    full_demise_centroid: DEMISEModel = Field(DEMISEModel.model_construct(), description="The DEMISE model centroid vectors")

    # Flag indicating if all source documents are disclosures
    is_disclosure_only: bool = Field(False, description="Flag indicating if all source documents are disclosures")
