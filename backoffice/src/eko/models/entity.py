from typing import List, Optional, Union

from pydantic import BaseModel, Field

from eko.models.common import EkoId, EntityType, entity_type_description


class Entity(BaseModel):
    """
    Represents an entity in the system, used for companies, people, organizations, etc.
    """
    id: int
    eko_id: EkoId
    name: str
    region_name: Union[str, None]
    common_name: Union[str, None]
    description: Optional[str]
    type: EntityType = Field(description="The type of the entity.\n\n " + entity_type_description)
    short_id: str
    owned_domains: List[str] = Field(description="The domains associated with the entity")
    url: Optional[str] = None
    legal_name: Optional[str] = None