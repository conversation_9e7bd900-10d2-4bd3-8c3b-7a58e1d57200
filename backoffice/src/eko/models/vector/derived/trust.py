from pydantic import Field

from eko.models.vector import ModelMarkdownMetadata
from eko.models.vector.base_vector_model import MatrixModel


# noinspection PyArgumentList
class TrustMatrixModel(MatrixModel):
    """
The Trust Matrix Model classifies the truthfulness of an entity’s past-action statements
and the attitude with which these statements are communicated or maintained.
Each field corresponds to a specific cell in the Claims Matrix,
represented as a float between 0.0 and 1.0 to quantify alignment with that behavior.
    """

    # Row 0: Claim Understated (<PERSON><PERSON><PERSON> did more than claimed)
    humble: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 0),
        keyword="HUMBLE",
        example="“It was just a small success,” though it was major.",
        description="Actively downplays achievements, presenting them as smaller than they are."
    )
    overlooked: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 1),
        keyword="OVERLOOKED",
        example="No PR, so true impact is never publicized.",
        description="Achievements remain understated due to lack of attention or recognition."
    )
    devalue: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 2),
        keyword="DEVALUE",
        example="Internal memos avoid praising the real extent of the accomplishments.",
        description="The entity’s dismissive stance suppresses or denies the significance of the success."
    )

    # Row 1: Accurate Claim (Factually true and complete)
    confirm: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 0),
        keyword="CONFIRM",
        example="“Here’s the data; it’s exactly as we reported.”",
        description="Proactively verifies and provides the correct account of events."
    )
    sustain: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 1),
        keyword="SUSTAIN",
        example="Archived facts remain unchallenged.",
        description="Truth remains intact by default—no one distorts it, so it stays correct."
    )
    maintain_despite: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 2),
        keyword="MAINTAIN DESPITE",
        example="Whistleblowers or audits preserve truthful records.",
        description="Even amid dismissive or obstructive tendencies, others ensure the facts stay accurate."
    )

    # Row 2: Claim Is an Exaggeration (Truth is overstated)
    hype: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 0),
        keyword="HYPE",
        example="“We increased sales by 200%!” when it’s actually 20%.",
        description="Consciously inflates results or achievements."
    )
    drift_up: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 1),
        keyword="DRIFT UP",
        example="Word of mouth expands a minor win into a “major triumph.”",
        description="Small overstatements grow over time without direct correction."
    )
    blow_up: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 2),
        keyword="BLOW UP",
        example="Sensationalized marketing overshadowing factual reports.",
        description="Despite attempts to keep it honest, the narrative gets blown out of proportion."
    )

    # Row 3: Claim Has No Basis (Invented or groundless)
    invent: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 0),
        keyword="INVENT",
        example="“We solved the crisis alone,” with zero proof.",
        description="Deliberately fabricates facts from thin air."
    )
    assume: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 1),
        keyword="ASSUME",
        example="Staffers guess and pass along unverified info.",
        description="A baseless claim spreads by rumor or speculation, without active direction."
    )
    override: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 2),
        keyword="OVERRIDE",
        example="“We’re sticking to our story—facts be damned.”",
        description="Even when contradictory evidence is presented, the entity suppresses or ignores it."
    )

    # Row 4: Claim Is a Misdirection (Used to distract from reality)
    distract: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 0),
        keyword="DISTRACT",
        example="“Look at X!” to hide the real Y.",
        description="Actively shifts attention with a misleading claim."
    )
    let_slide: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 1),
        keyword="LET SLIDE",
        example="Everyone passively moves on to the flashy story.",
        description="The misdirection persists as no one pushes back or corrects it."
    )
    deflect: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 2),
        keyword="DEFLECT",
        example="Ridicules questions and changes the subject.",
        description="The entity brushes off attempts to clarify, letting the misdirection overshadow real issues."
    )

    # Row 5: Claim Is Untrue (False statement, exposed by evidence)
    fabricate: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 0),
        keyword="FABRICATE",
        example="“Our data shows success” when the actual numbers prove failure.",
        description="Knowingly issues a falsehood."
    )
    ignore_facts: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 1),
        keyword="IGNORE FACTS",
        example="Staff sees contradictory data but does nothing.",
        description="Allows inaccuracies to remain unchallenged, refusing to acknowledge proof to the contrary."
    )
    silence_dissent: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 2),
        keyword="SILENCE DISSENT",
        example="“Stop asking questions about that.”",
        description="Even after being shown the truth, the entity denies or punishes anyone who challenges the false claim."
    )

    # Row 6: Claim Is the Opposite (Reality is diametrically reversed)
    doublethink: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 0),
        keyword="DOUBLETHINK",
        example="“We are cutting emissions” while actually increasing them.",
        description="Brazenly states the opposite of the truth."
    )
    blind_eye: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 1),
        keyword="BLIND EYE",
        example="Leadership ignores all evidence and pretends the false story is correct.",
        description="Turns away from blatant contradictions, acting as if the opposite claim is valid."
    )
    gaslight: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 2),
        keyword="GASLIGHT",
        example="“You must be mistaken—our real action is precisely what we claim.”",
        description="Actively manipulates or coerces others to accept the reversed narrative as true."
    )


TrustMatrixModel._markdown_metadata = ModelMarkdownMetadata(  # pyright: ignore [reportAttributeAccessIssue]
    "Trust Matrix Model",
    rows=[
        "Claim Understated",
        "Accurate Claim",
        "Claim Is an Exaggeration",
        "Claim Has No Basis",
        "Claim Is a Misdirection",
        "Claim Is Untrue",
        "Claim Is the Opposite"
    ],
    row_descriptions=[
        "The entity actually did more than claimed.",
        "Factually true and complete claim.",
        "Truth is overstated.",
        "Claim is invented or groundless.",
        "Claim is used to distract from reality.",
        "False statement, exposed by evidence.",
        "Reality is diametrically reversed to the claim."
    ],
    cols=["Active", "Passive", "Dismissive"],
    col_descriptions=[
        "Deliberate shaping of the claim.",
        "Claim persists without direct effort.",
        "Claim persists despite counter-evidence or resistance."
    ],
)

if __name__ == "__main__":
    TrustMatrixModel.display_markdown()