"""
Effect type enum for effect flags.

This module provides the EffectType enum used in effect analysis.
"""

from enum import Enum


class EffectType(Enum):
    """Enum for effect flag types"""
    RED = "red"  # Negative impact
    GREEN = "green"  # Positive impact

    def __str__(self) -> str:
        return self.value
        
    def __repr__(self) -> str:
        return f"EffectType.{self.name}"
    
    def __format__(self, format_spec):
        """Support string formatting"""
        return format(str(self), format_spec)
    
    def __eq__(self, other):
        """Support direct string comparison"""
        if isinstance(other, str):
            return self.value == other
        return super().__eq__(other)
        
    def to_json(self):
        """Make this class JSON serializable"""
        # Add type information for JavaScript to properly handle the object
        return {
            "__typename": "EffectType",
            "value": self.value,
            "name": self.name
        }

    @property
    def is_positive(self) -> bool:
        return self == EffectType.GREEN

    @classmethod
    def from_impact(cls, impact_value: float):
        """Determine effect type based on impact value"""
        return cls.RED if impact_value < 0 else cls.GREEN

    @classmethod
    def from_text(cls, text: str):
        if text.lower() == "red":
            return cls.RED
        elif text.lower() == "green":
            return cls.GREEN
        else:
            raise ValueError(f"Invalid effect type: {text}. Must be 'red' or 'green'.")
