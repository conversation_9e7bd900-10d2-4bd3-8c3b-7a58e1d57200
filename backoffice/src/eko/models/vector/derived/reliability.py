from pydantic import Field

from eko.models.vector import ModelMarkdownMetadata
from eko.models.vector.base_vector_model import MatrixModel


# noinspection PyArgumentList
class ReliabilityMatrixModel(MatrixModel):
    """
The Reliability Matrix Model outlines how promises are handled,
focusing on both the outcome and the manner or attitude behind achieving it.
Each field corresponds to a specific cell in the Promise Matrix,
represented as a float between 0.0 and 1.0 to quantify alignment with that behavior.
    """

    # Row 0: Promise Exceeded
    overdeliver: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 0),
        keyword="OVERDELIVER",
        example="Promised a 10% improvement but achieved 20%.",
        description="Actively invests extra effort/resources to surpass the original promise."
    )
    serendipity: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 1),
        keyword="SERENDIPITY",
        example="A new technology arises that surpasses expectations.",
        description="Promise is exceeded by chance or external factors, not by deliberate effort."
    )
    counteracted: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 2),
        keyword="COUNTERACTED",
        example="Partners or market forces achieve the result anyway.",
        description="Despite the entity’s obstacles or neglect, external forces push the outcome beyond the original promise."
    )

    # Row 1: Promise Kept
    fulfill: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 0),
        keyword="FULFILL",
        example="Dedicate resources and monitor progress until completion.",
        description="Actively ensures the promise is met in full."
    )
    kept: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 1),
        keyword="KEPT",
        example="Third-party steps in to finish the project, meeting the original commitment.",
        description="Promise remains intact without direct effort."
    )
    hinder: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 2),
        keyword="HINDER",
        example="Employees insist on meeting the commitment regardless of leadership neglect.",
        description="Despite the entity’s complacency or subtle pushback, the promise is still fulfilled."
    )

    # Row 2: Lesser Action Substituted
    shortchange: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 0),
        keyword="SHORTCHANGE",
        example="Promised full funding but only provides partial.",
        description="Entity actively opts for a reduced or watered-down outcome."
    )
    drift: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 1),
        keyword="DRIFT",
        example="Quietly cuts scope over time without openly acknowledging it.",
        description="Original promise fades into a lesser version due to inaction or neglect."
    )
    undercut: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 2),
        keyword="UNDERCUT",
        example="Minimal budgets or internal sabotage hamper achieving the true promise.",
        description="Entity’s dismissive or blocking behavior forces a less effective outcome to replace the original pledge."
    )

    # Row 3: Promise Ignored
    reject: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 0),
        keyword="REJECT",
        example="Statement like “We will not fulfill that commitment.”",
        description="Consciously decides not to honor the promise at all."
    )
    let_die: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 1),
        keyword="LET DIE",
        example="No mention or updates until the promise is forgotten.",
        description="Promise quietly lapses with zero follow-through."
    )
    bury: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 2),
        keyword="BURY",
        example="Scrubs any reference to the promise from communications.",
        description="Actively conceals or dismisses the promise’s existence so it appears never to have mattered."
    )

    # Row 4: Promise Broken & Attention Diverted
    misdirect: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 0),
        keyword="MISDIRECT",
        example="“We didn’t do X, but look at shiny new Y!”",
        description="Breaks the promise and overtly shifts focus elsewhere."
    )
    overlook: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 1),
        keyword="OVERLOOK",
        example="Lets a deliverable fail, then spends energy on a different project.",
        description="Promise is broken passively, with attention drifting away to other topics."
    )
    sidestep: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 2),
        keyword="SIDESTEP",
        example="“It doesn’t really matter we didn’t do X; it wasn’t important anyway.”",
        description="Breaks the promise while dismissing concerns and steering focus to a trivial or unrelated matter."
    )

    # Row 5: Promise Broken & Admitted
    confess: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 0),
        keyword="CONFESS",
        example="“We promised X, but we failed. Here’s why.”",
        description="Openly admits failure to keep the promise and takes responsibility."
    )
    accept: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 1),
        keyword="ACCEPT",
        example="“Yes, we let X slip, it happens…”",
        description="Recognizes the broken promise with little to no follow-up or corrective action."
    )
    nullify: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 2),
        keyword="NULLIFY",
        example="“We know we didn’t deliver X, but it’s irrelevant now.”",
        description="Shrugs off or trivializes the broken promise as if it no longer applies."
    )

    # Row 6: Promise Broken & Denied
    repudiate: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 0),
        keyword="REPUDIATE",
        example="“We never agreed to that in the first place.”",
        description="Actively denies or disavows the promise’s existence."
    )
    ignorance: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 1),
        keyword="IGNORANCE",
        example="“We have no record of that commitment.”",
        description="Passively feigns no knowledge of the promise."
    )
    dismiss: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 2),
        keyword="DISMISS",
        example="“That was never a serious pledge to begin with.”",
        description="Writes off the promise as meaningless, denying it was ever valid."
    )


ReliabilityMatrixModel._markdown_metadata = ModelMarkdownMetadata(  # pyright: ignore [reportAttributeAccessIssue]
    "Reliability Matrix Model",
    rows=[
        "Promise Exceeded",
        "Promise Kept",
        "Lesser Action Substituted",
        "Promise Ignored",
        "Promise Broken & Attention Diverted",
        "Promise Broken & Admitted",
        "Promise Broken & Denied"
    ],
    row_descriptions=[
        "Delivered more than pledged.",
        "Delivered exactly as pledged.",
        "Delivered a weaker version of the promise.",
        "No tangible action taken on the pledge.",
        "Did not honor pledge, then distracted observers.",
        "Failed to honor pledge but acknowledges it.",
        "Failed to honor pledge and refuses to acknowledge it."
    ],
    cols=["Active", "Passive", "Dismissive"],
    col_descriptions=[
        "Deliberate action to achieve outcome.",
        "Outcome occurred without direct intervention.",
        "Outcome achieved despite obstruction or dismissive behavior."
    ],
)

if __name__ == "__main__":
    ReliabilityMatrixModel.display_markdown("reliability.html")