from pydantic import Field

from eko.models.vector import ModelMarkdownMetadata
from eko.models.vector.base_vector_model import BaseVectorModel


class StatementTypeModel(BaseVectorModel):
    """
    Categorizes types of statements based on their temporal nature, purpose, and intent.
    
    This model uses a standardized scale from 0.0 to 1.0 for each dimension, where higher values indicate
    stronger alignment with that statement type.
    """

    # Basic statement categories
    describes_action: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement describes an action taken or in progress. This is something done by an entity."
    )

    describes_event: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement describes an event that happened or will happen at a specific time/place."
    )

    describes_fact: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement describes a fact, opinion, or idea it is neither an event nor an action."
    )

    describes_idea: float = Field(0.0, ge=0.0, le=1.0,
                                  description="The statement describes an idea or concept.",
                                  added_date="2025-04-01T00:00:00")  # pyright: ignore 
    
    # Temporal dimensions
    past_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement refers to something that happened in the past."
    )

    present_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement refers to something happening in the present."
    )

    future_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement refers to something that will happen in the future."
    )

    # Impact dimensions
    beneficial_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement is in *itself* positive/beneficial in nature, not that it describes an action that is positive."
    )

    neutral_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement is neutral in nature, not that it describes an action that is neutral."
    )

    harmful_statement: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement is in *itself* negative/harmful, not that it describes an action that is harmful."
    )

    # Statement purpose
    descriptive: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement describes or explains something (e.g., 'Our emissions decreased by 20%')."
    )

    directive: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement directs or proposes action (e.g., 'We will implement new policies', 'You must increase your yield')."
    )

    assertive: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement makes a claim or assertion (e.g., 'We are the market leader')."
    )

    predictive: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement predicts or forecasts outcomes (e.g., 'Sales will increase next year')."
    )

    promissory: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement makes a promise or commitment (e.g., 'We promise to reduce emissions')."
    )

    cautionary: float = Field(
        0.0, ge=0.0, le=1.0,
        description="The statement warns or cautions about something (e.g., 'Failure to act will result in...')."
    )

    def is_factual(self) -> bool:
        """Determines if the statement is primarily factual in nature"""
        return self.describes_fact > 0.3 or self.descriptive > 0.3

    def is_action(self) -> bool:
        """Determines if the statement describes an action"""
        return self.describes_action > 0.3 and self.future_statement < 0.3

    def is_event(self) -> bool:
        """Determines if the statement describes an event"""
        return self.describes_event > 0.3

    def is_claim(self) -> bool:
        """
        Determines if the statement is a claim about past or present situations
        A claim is a statement asserting something about past results or current status
        """
        return (self.assertive > 0.3 or self.descriptive > 0.3) and (
                    self.past_statement > 0.3 or self.present_statement > 0.3)

    def is_promise(self) -> bool:
        """
        Determines if the statement is a promise about future actions
        A promise is a commitment to do something in the future
        """
        return (self.promissory > 0.3 or self.directive > 0.3 or self.predictive > 0.3) and self.future_statement > 0.3


StatementTypeModel._markdown_metadata = ModelMarkdownMetadata(  # pyright: ignore [reportAttributeAccessIssue]
    "Statement Type Model",
    extra_docs="""
## Statement Types Explanation

The simplified Statement Type Model focuses on these key dimensions:

### Basic Statement Categories
- **describes_action**: Statement describes an action with consequences
- **describes_event**: Statement describes an event at a specific time/place
- **describes_fact**: Statement describes a fact, opinion, or idea

### Temporal Dimensions
- **past_statement**: Refers to something that happened in the past
- **present_statement**: Refers to something happening in the present
- **future_statement**: Refers to something that will happen in the future

### Impact Dimensions
- **beneficial_statement**: Describes something positive or beneficial
- **neutral_statement**: Is neutral in nature
- **harmful_statement**: Describes something negative or harmful

### Statement Purpose
- **descriptive**: Describes or explains something
- **directive**: Directs or proposes action
- **assertive**: Makes a strong claim or assertion
- **predictive**: Predicts or forecasts outcomes
- **promissory**: Makes a promise or commitment
- **cautionary**: Warns or cautions about something

### Key Methods
- **is_action()**: Statement describes an action (not future-oriented)
- **is_event()**: Statement describes an event
- **is_claim()**: Statement is asserting something about past results or current status
- **is_promise()**: Statement is a commitment to do something in the future
"""
)
