from typing import Any, Dict, Type

from pydantic import Field, BaseModel

from eko.models.vector import ModelMarkdownMetadata
from eko.models.vector.base_vector_model import MatrixModel


# noinspection PyArgumentList
class EntityTypeModel(MatrixModel):
    """
The Entity Type Model provides a means to categorise the various
entities across multiple domains. It organizes entities into rows representing their type, such as materials,
software, hardware, plants, animals, humans, locality, and immaterial. These categories capture a wide range of interactions,
from elemental and digital components to ecosystems and societal structures.

The columns define levels of complexity, starting with individual units and progressing through aggregates,
complex entities, and interconnected systems. This matrix offers a comprehensive perspective on how different
elements interact and influence one another, enabling a nuanced understanding of their roles and impacts within
ecological, technological, and human systems.
    """

    # Material
    element: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 0),
        keyword="Element",
        example="Iron Ore, Lithium Oxide, Hydrogen",
        description="Elements and their ores"
    )
    chemical: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 1),
        keyword="Chemicals",
        example="Plastics, Water, Hydrogen Sulfide",
        description="Inorganic chemicals."
    )
    organic: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 2),
        keyword="Organic",
        example="Lipids, Carbohydrates",
        description="Complex organic chemicals."
    )
    biological: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(0, 3),
        keyword="Biological",
        example="DNA, Bacteria, Virus, Proteins, Nucleic Acids",
        description="Complex organic chemical systems (life) up to but not including the cell."
    )

    # Software
    media: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 0),
        keyword="Digital Media",
        example="Files, emails, videos, images, songs, etc.",
        description="Digital assets, including files and data."
    )
    software: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 1),
        keyword="Software",
        example="A game, a program, an application, a website, etc.",
        description="Computer programs and associated data."
    )
    ai: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 2),
        keyword="AI",
        example="AI Image Generation, ChatGPT, Skynet, HAL",
        description="Human-like intelligence (chatbots to ASI)."
    )
    software_system: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(1, 3),
        keyword="Software System",
        example="The Internet, Facebook, MacOS, Windows, AWS, Google, etc.",
        description="Complex aggregates of software (OS, internet-scale apps)."
    )

    # Hardware
    electronics: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 0),
        keyword="Electronics",
        example="Resistors, Capacitors, Processors",
        description="Individual electronic components."
    )
    computers: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 1),
        keyword="Computers",
        example="Laptops, Smartphones, Servers",
        description="Whole computers or electronic devices."
    )
    robotics: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 2),
        keyword="Robotics",
        example="Industrial Robots, Drones, Automated Vehicles",
        description="Robotics and automated machinery."
    )
    server_farms: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(2, 3),
        keyword="Server Farms",
        example="Data Centers, Cloud Infrastructure",
        description="Large-scale hardware systems."
    )

    # Plant
    mosses: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 0),
        keyword="Mosses",
        example="Mosses, Algae",
        description="Mosses and similar small plants."
    )
    plants: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 1),
        keyword="Plants",
        example="Roses, Cacti, Ferns",
        description="Plants and flowers."
    )
    trees: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 2),
        keyword="Trees",
        example="Oak, Pine, Redwood",
        description="Large plants and trees."
    )
    biological_systems: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(3, 3),
        keyword="Biological Systems",
        example="Forests, Wetlands, Ecosystems",
        description="Complex plant ecosystems."
    )

    # Animal
    single_cell_organisms: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 0),
        keyword="Single Cell Organisms",
        example="Bacteria, Viruses, Fungi",
        description="Single cell organisms."
    )
    multi_cell_animals: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 1),
        keyword="Multi Cell Animals",
        example="Insects, Molluscs",
        description="Organisms with low or no cognitive abilities."
    )
    higher_animal_life: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 2),
        keyword="Higher Level Animals",
        example="Mammals, Reptiles, Amphibians, Octopuses",
        description="Animals with good cognitive abilities."
    )
    group: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(4, 3),
        keyword="Group",
        example="Hive, Herds, Swarms, Flocks",
        description="Groups of animals working together."
    )

    # Human
    individual: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 0),
        keyword="Individual",
        example="A single person, a customer, a citizen",
        description="Individual humans."
    )
    demographic: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 1),
        keyword="Demographic",
        example="Age groups, ethnic groups, income brackets",
        description="Groups defined by common characteristics."
    )
    organizations: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 2),
        keyword="Companies/NGOs/Charities",
        example="Corporations, Non-Governmental Organizations, Charities",
        description="Business entities and non-profits."
    )
    governments: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(5, 3),
        keyword="Governments",
        example="Local councils, national governments, international organizations",
        description="Governmental bodies and institutions."
    )

    # Locality
    place: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 0),
        keyword="Place",
        example="House, shop, square, road",
        description="A place, a location."
    )
    village_town: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 1),
        keyword="Village/Town",
        example="A town, village, or a national park",
        description="A collection of places."
    )
    cities_provinces: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 2),
        keyword="Cities & Provinces",
        example="A city, a county, a province",
        description="A complex collection of locations."
    )
    geopolitical: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(6, 3),
        keyword="Geo-Political",
        example="USA, UK, EEC, Russian Federation, India, China, etc.",
        description="A country or a combination of countries."
    )

    # Immaterial
    immaterial: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(7, 0),
        keyword="Immaterial",
        example="Money, a law, an idea, a choice, a vote, happiness",
        description="An immaterial idea, concept, or mechanism."
    )
    multiple_immaterials: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(7, 1),
        keyword="Multiple Immaterials",
        example="Votes",
        description="Aggregate concepts such as wealth, voting, laws, etc."
    )
    processes: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(7, 2),
        keyword="Processes",
        example="Economics, knowledge, rule of law, politics, doctrines",
        description="Complex immaterials."
    )
    immaterial_systems: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(7, 3),
        keyword="Systems",
        example="Economy, democracy, education, science, religion, ideology, legal system",
        description="Immaterial systems."
    )

    #Environment
    environment: float = Field(
        0.0,
        ge=0.0,
        le=1.0,
        matrix_position=(8, 0),
        keyword="Environment",
        example="Air, water, soil, ecosystems",
        description="The natural environment.",
        added_date="2025-05-121T11:36:10"  # pyright: ignore
    )

    class Config:
        @classmethod
        def schema_extra(cls, schema: Dict[str, Any], model: Type[BaseModel]) -> None:
            properties = schema.get("properties", {})
            # Remove custom 'matrix_position' metadata from JSON Schema.
            for prop in properties.values():
                prop.pop("matrix_position", None)
                prop.pop("keyword", None)
                prop.pop("example", None)


EntityTypeModel._markdown_metadata = ModelMarkdownMetadata(  # pyright: ignore [reportAttributeAccessIssue]
    "Entity Type Model",
    rows=[
        "Material",
        "Software",
        "Hardware",
        "Plant",
        "Animal",
        "Human",
        "Locality",
        "Immaterial"
    ],
    row_descriptions=[
        "Includes elements, chemicals, and biological materials.",
        "Files, software, AI, and complex systems.",
        "Covers electronics, computers, robotics, and server farms.",
        "Mosses, plants, trees, and ecosystems.",
        "Involves insects, mammals, humans, and human organizations.",
        "Humans as individuals, groups, and institutions.",
        "Places, towns, cities, and countries.",
        "An immaterial idea/concept/mechanism."
    ],
    cols=["Individual", "Aggregate", "Complex", "System"],
    col_descriptions=[
        "Basic units such as ores or files.",
        "Collections or aggregates, like chemicals or software.",
        "Complex structures, such as organic chemicals or AI.",
        "Systems comprising interconnected elements, like ecosystems or operating systems."
    ],
)

if __name__ == "__main__":
    # print(list(EntityTypeModel.construct().to_kv().keys()))
    EntityTypeModel.display_markdown()
