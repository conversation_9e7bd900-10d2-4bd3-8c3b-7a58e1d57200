from typing import Tuple, cast, List, Dict

import json
from pydantic import Field

from eko.models.vector.base_vector_model import BaseVectorModel
from eko.models.vector.base_vector_utils import to_fixed_size_vector
from eko.models.vector.demise.domain import DomainModel
from eko.models.vector.demise.engagement import EngagementModel
from eko.models.vector.demise.entity import EntityModel
from eko.models.vector.demise.impact import ImpactModel
from eko.models.vector.demise.motivation import MotivationModel
from eko.models.vector.demise.statement import StatementTypeModel
from eko.models.vector.to_markdown import view_markdown_in_browser


class DEMISEModel(BaseVectorModel):
    """
DEMISE is a framework for analyzing and understanding the nature of a statement made by any entity about an action or
event that has ethical implications, the action/event can be hypothetical/intended past/present etc.

The model is used to create embeddings for statements that are designed to exist in an ethical latent space that encapsulates the
motivations for the actions described in the statement and their impact.

Each field uses the added_date attribute to ensure consistent ordering when vectorized.
This ensures that vectors maintain backward compatibility when new fields are added.
"""
    domain: DomainModel = Field(
        DomainModel.model_construct(),
        description="The domain to which the action/event/facts/entities relate",
    )

    subject: EntityModel = Field(
        EntityModel.model_construct(),
        description="The subject of the statement, this is the entity about which a statement is made or if about an action then the entity that caused the action. Zeroed if not applicable.",
    )

    object: EntityModel = Field(
        EntityModel.model_construct(),
        description="The object of the statement, this is the entity which has an action done to it. It is the entity impacted by any action or event described in the statement. Zeroed if not applicable.",
    )

    motivation: MotivationModel = Field(
        MotivationModel.model_construct(),
        description="The motivation for the action if an action, otherwise the motivation for the statement.",
    )

    impact: ImpactModel = Field(
        ImpactModel.model_construct(),
        description="The impact of the action/event if an action, otherwise the impact of the statement.",
    )

    statement: StatementTypeModel = Field(
        StatementTypeModel.model_construct(),
        description="The type of statement made, including whether it is a statement of fact or relating to an action/event.",
    )

    # These fields were added later and should appear at the end of vectors
    engagement: EngagementModel = Field(
        EngagementModel.model_construct(),
        description="The engagement patterns shown by the entity in relation to the issues at hand.",
    )

    # Example of how future fields would be added:
    #
    # new_field: float = Field(
    #     0.0,  # Default value
    #     description="Description of the new field",
    #     ge=0.0, le=1.0,  # Add any field constraints
    #     added_date="2023-04-01T00:00:00"  # Later date than existing fields
    # )
    #
    # When adding new fields, always use a date later than existing fields.

    def to_hash(self):
        return self.statement.to_hash() + self.subject.to_hash() + self.object.to_hash() + ":" + self.impact.to_hash() + ":" + self.motivation.to_hash() + self.engagement.to_hash() + self.domain.to_hash()

    def is_environmental(self, threshold: float = 0.2, impact_threshold: float = 0.2) -> bool:
        """
        Determines if the statement is related to environmental topics.

        Args:
            threshold: Threshold value for domain indicators (default: 0.5)
            impact_threshold: Threshold value for impact indicators (default: 0.3)

        Returns:
            True if the statement is related to environmental topics, False otherwise
        """
        return max(max(self.domain.environment.to_vector())
                   , max(self.domain.energy.to_vector())
                   , max(self.domain.pollution.to_vector())
                   ) > threshold or self.is_animal_welfare(threshold=threshold, impact_threshold=impact_threshold)

    def is_social(self, threshold: float = 0.2, impact_threshold: float = 0.2) -> bool:
        """
        Determines if the statement is related to social topics.

        Args:
            threshold: Threshold value for domain indicators (default: 0.5)
            impact_threshold: Threshold value for impact indicators (default: 0.3)

        Returns:
            True if the statement is related to social topics, False otherwise
        """
        return max(max(self.domain.society.to_vector())
                   , max(self.domain.equality.to_vector())
                   ) > threshold

    def is_governance(self, threshold: float = 0.2) -> bool:
        """
        Determines if the statement is related to governance topics.

        Args:
            threshold: Threshold value for domain indicators (default: 0.5)

        Returns:
            True if the statement is related to governance topics, False otherwise
        """
        return max(self.domain.governance.to_vector()) > threshold

    def is_animal_welfare(self, threshold: float = 0.2, impact_threshold: float = 0.2) -> bool:
        """
        Determines if the statement is related to animal welfare topics.

        Args:
            threshold: Threshold value for domain indicators (default: 0.5)
            impact_threshold: Threshold value for impact indicators (default: 0.3)

        Returns:
            True if the statement is related to animal welfare topics, False otherwise
        """
        return max(self.domain.animals.to_vector()) > threshold

    def get_dominant_domains(self, threshold: float = 0.5) -> list[str]:
        """
        Returns a list of dominant domains in the model.

        Args:
            threshold: Threshold value for domain scores (default: 0.5)

        Returns:
            List of dominant domain names as strings
        """
        return [k.split('.')[-1] for k, v in self.domain.to_kv().items() if v > threshold]

    def has_significant_impact(self, threshold: float = 0.3) -> bool:
        """
        Determines if the statement describes an action with significant impact.

        Args:
            threshold: Threshold value for impact score (default: 0.3)

        Returns:
            True if the statement describes an action with significant impact, False otherwise
        """
        return self.statement.describes_action > 0.0 and abs(self.impact.benefit_to_object_entity - self.impact.harm_to_object_entity) > threshold

    def get_impact_value(self):
        return self.impact.get_impact_value()

    def is_claim(self):
        return self.statement.is_claim()

    def is_promise(self):
        return self.statement.is_promise()

    def is_harm(self):
        return self.impact.get_impact_value() < 0.001

    def is_benefit(self):
        return self.impact.get_impact_value() > 0.001

    @classmethod
    def generate_docs(cls, depth=1):
        return f"""

{'#' * (depth)} DEMISE - Domain, Entities, Motivation, Impact, Statement Type and Engagement

{'#' * (depth + 1)} Introduction


**DEMISE** stands for **Domain, Entity, Motivation, Impact, Statement Type, and Engagement**. It is a conceptual framework for analyzing statements that describe or imply an action (past, present, or future), along with ethical considerations and engagement patterns. Here is a quick summary of each DEMISE component:

1. **Domain**
   The general area or topic of the action or statement (e.g. technology, finance, healthcare).
   - *Example:* A statement about "carbon footprint reduction" could have the Domain "climate/energy."

2. **Entity**
   The entity or entities involved in the statement, such as a person, organization, product, location, or concept.
   - *Example:* In "Company X announced a new policy," the main Entity is "Company X."

3. **Motivation**
   The reason or driving factor behind the statement or action (e.g. fear, ambition, kindness, generosity).
   - *Example:* A decision motivated by "greed" versus one motivated by "sustainability."

4. **Impact**
   The effect the action or statement has on any entity, whether beneficial, harmful, or neutral.
   - *Example:* "We caused an increase in local employment" has a positive Impact on the community.

5. **Statement Type**
   The temporal and rhetorical nature of the statement (e.g. a prediction, a promise, an acknowledgement, or a denial).
   - *Example:* "We predict we'll expand next year" is a **Future Benefit**-type statement with a **Prediction** label.

6. **Engagement**
   How a company engages with issues (e.g. preventative, altruistic, proactive, responsive).
   - *Example:* A company that "seeks to prevent issues before they arise" displays a **Preventative** Engagement pattern.


{cls.__doc__}

There are also subsets of the DEMISE Model that are used to create embeddings for different purposes, such as the
Flag Model.

{'#' * (depth + 2)} Ethical Action or 'Flag' Model

The Flag Model is used to create embeddings for flags, that is analysis of good and bad behaviour. It is a
subset of the DEMISE Model that is used to analyze the impact of actions. It consists of the Domain, Entities,
Motivation, Impact and Engagement components. It also spans multiple statements, and potentially pages of text, that is
why there is no Statement Type component.

{StatementTypeModel.generate_docs(depth=depth + 1)}

{EntityModel.generate_docs(depth=depth + 1)}

{DomainModel.generate_docs(depth=depth + 1)}

{EngagementModel.generate_docs(depth=depth + 1)}

{ImpactModel.generate_docs(depth=depth + 1)}

{MotivationModel.generate_docs(depth=depth + 1)}




    """

    def get_effect_vector(self) -> list[float]:
        return self.domain.to_vector() + self.impact.to_vector() + self.engagement.to_vector()

    def get_fixed_size_effect_vector(self, size: int = 1024) -> list[float]:
        return to_fixed_size_vector(self.get_effect_vector(), size)

    def get_claim_vector(self, size: int = 1024) -> list[float]:
        return to_fixed_size_vector(self.domain.to_vector() + self.engagement.to_vector(), size)

    def get_promise_vector(self, size: int = 1024) -> list[float]:
        return to_fixed_size_vector(self.domain.to_vector() + self.engagement.to_vector(), size)

    def get_dso_vector(self, size: int = 1024) -> list[float]:
        return to_fixed_size_vector(self.domain.to_vector() + self.subject.to_vector() + self.object.to_vector(), size)

    @classmethod
    def from_dso_vector(cls, dso_vector: list[float]) -> Tuple[DomainModel, EntityModel, EntityModel]:
        """
        Updates the domain, subject, and object components of the model based on the provided DSO vector.

        Args:
            dso_vector: The DSO vector to update the model with
        """
        domain_vector= dso_vector[:DOMAIN_MODEL_VECTOR_SIZE]
        subject_vector = dso_vector[DOMAIN_MODEL_VECTOR_SIZE:DOMAIN_MODEL_VECTOR_SIZE + ENTITY_MODEL_VECTOR_SIZE]
        object_vector = dso_vector[DOMAIN_MODEL_VECTOR_SIZE + ENTITY_MODEL_VECTOR_SIZE:DOMAIN_MODEL_VECTOR_SIZE + ENTITY_MODEL_VECTOR_SIZE*2]
        return (
            cast(DomainModel,DomainModel.from_vector(domain_vector)),
            cast(EntityModel,EntityModel.from_vector(subject_vector)),
            cast(EntityModel,EntityModel.from_vector(object_vector))
        )

    def get_engagement_dimension(self):
        """
        Returns the dominant engagement dimension based on the engagement model.

        Returns:
            EngagementDimension: The dominant engagement dimension
        """
        return self.engagement.get_dominant_engagement()

    def get_motivation_dimension(self):
        """
        Returns the dominant motivation dimension based on the motivation model.

        Returns:
            MotivationDimension: The dominant motivation dimension
        """
        return self.motivation.get_dominant_motivation()

    @classmethod
    def get_feature_names_llm_friendly(cls) -> str:
        """
        Returns a concise, LLM-friendly representation of the DEMISE model features.

        This method organizes features by their component categories and presents them
        in a structured format optimized for low token usage while maintaining clarity.

        Returns:
            A string containing the organized feature names in a compact format
        """
        # Get all feature names
        all_features = cls.get_feature_names()

        # Organize features by component
        components = {
            "domain": [],
            "subject": [],
            "object": [],
            "motivation": [],
            "impact": [],
            "statement": [],
            "engagement": []
        }

        # Categorize features by their prefix
        for feature in all_features:
            for component in components:
                if feature.startswith(component + "."):
                    # Remove the component prefix to make it more concise
                    components[component].append(feature)
                    break

        # Build the compact representation
        result = []

        # Add domain features with proper nesting
        result.append("domain.*: Main domain categories")

        # Create a nested structure for domain categories
        domain_structure = {}
        for feature in components["domain"]:
            parts = feature.split(".")
            if len(parts) >= 3:  # domain.category.subcategory
                category = parts[1]
                subcategory = parts[2]

                if category not in domain_structure:
                    domain_structure[category] = set()

                domain_structure[category].add(subcategory)

        # Add each domain category with its subcategories
        for category in sorted(domain_structure.keys()):
            subcategories = sorted(domain_structure[category])
            result.append(f"domain.{category}.*: {', '.join(subcategories)}")

        # Add entity features for both subject and object
        result.append("subject/object.entity_type.*: Entity types including:")
        entity_types = set()
        for feature in components["subject"]:
            if "entity_type" in feature:
                parts = feature.split(".")
                if len(parts) > 2:
                    entity_types.add(parts[2])  # Add the entity type
        result.append(", ".join(sorted(entity_types)))

        result.append("subject/object.qualities.*: Entity qualities including:")
        qualities = set()
        for feature in components["subject"]:
            if "qualities" in feature:
                parts = feature.split(".")
                if len(parts) > 2:
                    qualities.add(parts[2])  # Add the quality
        result.append(", ".join(sorted(qualities)))

        # Add motivation dimensions
        result.append("motivation.*: " + ", ".join([f.replace("motivation.", "") for f in components["motivation"]]))

        # Add impact dimensions
        result.append("impact.*: " + ", ".join([f.replace("impact.", "") for f in components["impact"]]))

        # Add statement types
        result.append("statement.*: " + ", ".join([f.replace("statement.", "") for f in components["statement"]]))

        # Add engagement patterns
        result.append("engagement.*: " + ", ".join([f.replace("engagement.", "") for f in components["engagement"]]))

        return "\n".join(result)

DOMAIN_MODEL_VECTOR_SIZE = len(DomainModel.model_construct().to_vector())
ENTITY_MODEL_VECTOR_SIZE = len(EntityModel.model_construct().to_vector())


if __name__ == "__main__":
    print(json.dumps([k for k, _ in DEMISEModel.get_key_descriptions().items()], indent=2))
    print("\n\nLLM-Friendly Feature Names:")
    print(DEMISEModel.get_feature_names_llm_friendly())
    docs = DEMISEModel.generate_docs()
    print(docs)
    view_markdown_in_browser(docs, "DEMISE Model", filename="demise.html")
