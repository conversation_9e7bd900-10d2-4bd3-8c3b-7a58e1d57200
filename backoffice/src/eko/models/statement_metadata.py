from typing import Optional, List

from pydantic import BaseModel, Field
from pydantic.json_schema import SkipJsonSchema

from eko.models.quantified_entity import QuantifiedEntity
from eko.models.simple_entity import Location, SimpleEntity
from eko.models.time import Time
from eko.models.vector import StatementCategory
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.demise.domain import DomainModel


class StatementMetadata(BaseModel):
    """
Extracted from the statement are the following:

- Statement Category - 'fact/opinion', 'event' or 'action'
- Domain - a word describing the domain to which this statement belongs
- Subject (Named) Entity  - the doer.
- Object (Named ) Entity - the done to .
- Quantities - unnamed entities such has dollars, rock etc. along with their quantities, these can only be the done to.
- Impact - the impact of the subject on the object
- Action - the action performed on the Object Entity or the
- Location - the location to which the statement relates, this may or may not be the affected entity.
- Time - the time at which an event occurs
- Impact Value - A calculated value representing the overall impact of the statement
- Start/End Year - the year range this statement is associated with (defaults to document year if not specified)

### Impact Scale

The impact scale is defined as follows:

### Harm

| Value | Animals                                          | Humans                                                   | Environment                                      |
|-------|----------------------------------------------------|--------------------------------------------------------|------------------------------------------------|
| -0.0  | No impact                                        | No impact                                             | No impact                                      |
| -0.1  | Very minor discomfort to a few animals          | Minor discomfort to a few individuals                 | Slight environmental disturbance              |
| -0.2  | Minor discomfort to several animals            | Moderate discomfort to several individuals           | Noticeable environmental impact              |
| -0.3  | Moderate discomfort to many animals            | Significant discomfort or stress to many individuals | Notable environmental damage                 |
| -0.4  | Significant stress to a local animal population | Considerable mental/emotional harm or discrimination affecting a community | Substantial environmental harm   |
| -0.5  | Animal death                                    | Severe mental/emotional suffering or widespread discrimination | Major environmental damage  |
| -0.6  | Multiple animal deaths                         | Minor physical harm to individuals or severe economic hardship for a community | Critical environmental damage |
| -0.7  | Large number of animal deaths                 | Moderate physical harm to many individuals           | Destruction of local ecosystems             |
| -0.8  | Extinction of a local animal species         | Severe physical harm to a large population          | Widespread environmental devastation        |
| -0.9  | Extinction of multiple animal species       | Loss of life on a significant scale                 | Irreversible damage to large ecosystems     |
| -1.0  | Mass extinction event                         | Global catastrophe threatening all human life       | Global catastrophe threatening all life on Earth |

### Benefit

| Value | Animals                                          | Humans                                                   | Environment                                      |
|-------|----------------------------------------------------|--------------------------------------------------------|------------------------------------------------|
| 0.0   | No impact                                        | No impact                                             | No impact                                      |
| 0.1   | Very minor benefit to a few animals            | Minor positive effect on a few individuals           | Slight environmental improvement              |
| 0.2   | Minor benefit to several animals              | Moderate benefit to several individuals              | Noticeable environmental enhancement          |
| 0.3   | Moderate improvement for many animals        | Significant improvement in well-being for many individuals | Notable environmental restoration  |
| 0.4   | Significant improvement for a local animal population | Considerable reduction in discrimination or mental/emotional distress for a community | Substantial environmental conservation |
| 0.5   | Major improvement in animal welfare        | Major improvement in quality of life or reduction of suffering for a large group | Significant ecosystem restoration |
| 0.6   | Substantial improvement in animal welfare   | Tangible physical health benefits or economic upliftment for a community | Critical habitat preservation |
| 0.7   | Prevention of mass animal suffering or death | Substantial improvement in physical and mental health for many | Regeneration of local ecosystems |
| 0.8   | Significant positive impact on multiple species | Transformative positive change for a large population | Widespread environmental rejuvenation |
| 0.9   | Preservation of endangered species         | Life-changing benefits on a significant scale       | Major positive shift in global environmental trends |
| 1.0   | Global improvement in animal welfare and biodiversity | Global transformation leading to unprecedented human well-being and societal progress | Environmental harmony and ecological balance on a global scale |

    """
    company: Optional[str] = Field(None, description="**IMPORTANT** The full legal name of the primary company involved in the statement, i.e. the one making it or the one it is about.")
    subject_entities: List[SimpleEntity] = Field(
        default_factory=list,
        description="The specific entity or entities responsible for taking the action, their name should the entities full canonical/legal name.",
    )
    object_entities: List[SimpleEntity] = Field(default_factory=list,
                                         description="The specific entity or entities upon which the action is taken or the statement is made about, their name should the entities full canonical/legal name.")

    authors: List[SimpleEntity] = Field(
        default_factory=list,
        description="Inferred from the text or if not from the authors of the document. This is who is SAYING the statement. So if the statement says 'Colgate are reducing their cost of living' then the author is NOT Colgate, but the author of the statement. Their name should the entities full canonical/legal name",
    )

    domain: str = Field(description="The domain to which this statement belongs: some examples are: " + str(
        DomainModel.get_feature_names())+" or create one if none of these fit.")

    impact: Optional[str] = Field(None, description="The impact of the subject on the object if applicable")
    impact_value: Optional[float] = Field(
        None,
        description=f"A value representing the overall impact that the subject entity CAUSED, between -1.0 and 1.0. If the subject entity *did not cause* harm/benefit, even if the statement describes harm/benefit then this should be zero. Negative values represent harmful impacts, positive values represent beneficial impacts. NOTE: This is not harm described, it is the harm CAUSED. NOTE: This is not the harm to the object, this is the objective HARM CAUSED BY THE SUBJECT see the Harm/Benefit scales above.",
    )
    action: Optional[str] = Field(None, description="Optional verb/phrase describing the specific action performed (subject does action on object)")
    locations: list[Location] = Field(default_factory=list, description="The named locations related to the action or statement or empty if not named or applicable.")
    time: Optional[Time] = Field(None, description="The time range during which the action or statement occurred. (optional)")
    start_year: Optional[int] = Field(None, description="The start year associated with this statement. Derived from Time field if available, otherwise defaults to document year.")
    end_year: Optional[int] = Field(None, description="The end year associated with this statement. Derived from Time field if available, otherwise defaults to document year.")
    quantities: List[QuantifiedEntity] = Field(default_factory=list,
                                               description="A field specifying quantified entities involved in the action or statement or null if the specifics are not known.")

    # Coarse grained flags for easy searching/filtering
    is_environmental:bool = Field(False, description="Whether the statement is about the environment/ecology/ecosystems or not.")
    is_social: bool = Field(False, description="Whether the statement is about a social issue, human wellbeing, human needs, human rights, social impact etc.")
    is_governance: bool = Field(False, description="Whether the statement is about corporate governance issues or not.")
    is_animal_welfare: bool = Field(False, description="Whether the statement is about animal welfare issues or not.")
    is_vague: bool = Field(False, description="Whether the statement contains vague language, like 'natural', 'eco', 'green' or anything else that can be called vague in the greenwashing sense of the word.")
    is_impactful_action: bool = Field(False, description="Whether the statement refers to an action which has or is still happening which has an impact on a living being, a group of living beings or the environment.")
    is_disclosure: bool = Field(False, description="Whether the statement is a disclosure or not. A disclosure is a statement made by a company about its own actions or behaviour. It is a statement made by a named entity about itself, it's actions or its behaviour. If a document is written by company X and the statement is about company X then it is a disclosure.")

    statement_category: StatementCategory = Field(
        description=""" Coarse grained classification of the statement:
* fact - a fact or *opinion( or any statement with no past/present/future aspect.
* action - an action (past/present) taken by the subject entity
* claim - a statement made by the subject entity / author about itself, it's actions or its behaviour
* promise - a statement made by the subject entity / author about its own future actions or behaviour
* event - a passive description of something that has happened at a time or place or will happen at a time or place and is not a fact/action/claim/promise
* directive - a statement made by the subject entity /  author directing action that should be taken
    """)

    def clean_dump(self):
        return {
            "is_impactful_action": self.is_impactful_action,
            "statement_category": self.statement_category,
            "subject_entities": [{"name": subject.name, "type": subject.type} for subject in self.subject_entities],
            "object_entities": [{"name": object.name, "type": object.type} for object in self.object_entities],
            "domain": self.domain,
            "impact": self.impact,
            "impact_value": self.impact_value,
            "action": self.action,
            "locations": [location.clean_dump() for location in self.locations] if self.locations else None,
            "time": self.time.clean_dump() if self.time else None,
            "start_year": self.start_year,
            "end_year": self.end_year,
            "quantities": [quantity.clean_dump() for quantity in self.quantities],
            "is_environmental": self.is_environmental,
            "is_social": self.is_social,
            "is_governance": self.is_governance,
            "is_animal_welfare": self.is_animal_welfare,
            "is_vague": self.is_vague
        }


class StatementAndMetadata(BaseModel):
    """
    A specific statement made by an entity, including the text and metadata.
    """
    id: Optional[int] = Field(None, description="The database ID of this statement")
    source_text: Optional[str] = Field(None, description="The complete and unaltered text of the statement.")
    statement_text: str = Field(description="An expanded and contextualised version of the statement.")
    context: SkipJsonSchema[Optional[str]] = Field(None, description="Contextual text.")
    # extended_context: SkipJsonSchema[Optional[str]] = Field(None, description="Further contextual text.")
    metadata: StatementMetadata = Field(description="The metadata of the statement")
    demise: DEMISEModel = Field(..., description="The DEMISE model for the statement")
    doc_id: Optional[int] = Field(None, description="The document ID this statement is from")
    page_id: Optional[int] = Field(None, description="The page ID within the document this statement is from")
    doc_year: Optional[int] = Field(None, description="The year of the document this statement is from")


class StatementModelText(BaseModel):
    statement: StatementAndMetadata = Field(description="The statement with extracted metadata")
    model: DEMISEModel = Field(description="The model of the statement")


class StatementList(BaseModel):
    """
    A list of statements.
    """
    thoughts: str = Field(
        description="Your thoughts, think through what you're going to say before you say it.",
        default="",
    )
    statements: list[StatementAndMetadata] = Field(
        description="The original text divided into statements along with metadata.")
    #
    # @model_validator(mode="before")
    # @classmethod
    # def validate_unique_combinations(cls, values):
    #     texts = values.get("texts", [])
    #     seen_combinations = set()
    #     for text in texts:
    #         combination = (text["subject_entity_name"], text["object_entity_name"], text["type"])
    #         if combination in seen_combinations:
    #             logger.warning(f"Duplicate combination of subject, object, and type found: {combination}")
    #             raise ValueError(
    #                 f"Duplicate combination of subject, object, and type found: {combination}. Please make sure the statements are unique, join together statements that have the same combination of subject, object and type."
    #             )
    #         seen_combinations.add(combination)
    #     return values

if __name__ == "__main__":
    print(StatementMetadata.model_json_schema())
