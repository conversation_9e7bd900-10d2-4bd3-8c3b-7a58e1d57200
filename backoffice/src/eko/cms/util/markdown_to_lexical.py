import json
import mistune


def create_text_node(text):
    """Creates a minimal Lexical text node."""
    return {
        "detail": 0,
        "format": 0,
        "mode": "normal",
        "style": "",
        "text": text,
        "type": "text",
        "version": 1
    }


def flatten_children(children):
    """Flattens a list of Lexical text nodes into a single string."""
    texts = []
    for child in children:
        if isinstance(child, dict) and child.get("type") == "text":
            texts.append(child.get("text", ""))
        elif isinstance(child, list):
            texts.append(flatten_children(child))
    return "".join(texts)


def convert_ast_to_lexical(tokens):
    """Converts a list of Mistune AST tokens into Lexical nodes."""
    nodes = []
    for token in tokens:
        node = convert_token(token)
        if node is not None:
            nodes.append(node)
    return nodes


def convert_token(token):
    ttype = token.get("type")

    if ttype == "heading":
        # Convert children and add a tag (e.g., "h1", "h2", etc.)
        children = convert_ast_to_lexical(token.get("children", []))
        if not children:
            children = [create_text_node("")]
        return {
            "children": children,
            "tag": f"h{token.get('level', 1)}",
            "type": "heading",
            "version": 1
        }
    elif ttype == "paragraph":
        children = convert_ast_to_lexical(token.get("children", []))
        if not children:
            children = [create_text_node("")]
        return {
            "children": children,
            "direction": None,
            "format": "",
            "indent": 0,
            "type": "paragraph",
            "version": 1
        }
    elif ttype == "list":
        # 'ordered' indicates numbered vs. bullet lists.
        children = [convert_token(child) for child in token.get("children", []) if convert_token(child) is not None]
        list_type = "number" if token.get("ordered") else "bullet"
        return {
            "children": children,
            "listType": list_type,
            "type": "list",
            "version": 1
        }
    elif ttype == "list_item":
        children = convert_ast_to_lexical(token.get("children", []))
        if not children:
            children = [create_text_node("")]
        return {
            "children": children,
            "type": "listitem",
            "version": 1
        }
    elif ttype == "block_code":
        # Create a code block node (with an optional language).
        code_text = token.get("text", "")
        language = token.get("info") or ""
        return {
            "children": [create_text_node(code_text)],
            "type": "code",
            "language": language,
            "version": 1
        }
    elif ttype == "thematic_break":
        # Represent a horizontal rule as a divider.
        return {
            "type": "divider",
            "version": 1
        }
    elif ttype == "blockquote":
        # Convert blockquotes to a node of type "quote"
        children = convert_ast_to_lexical(token.get("children", []))
        return {
            "children": children,
            "type": "quote",
            "version": 1
        }
    elif ttype == "text":
        print(token)
        return create_text_node(token.get("raw", ""))
    elif ttype == "inline_code":
        return create_text_node(token.get("raw", ""))
    elif ttype in ("strong", "emphasis"):
        # For bold or italic, simply flatten their children.
        children = convert_ast_to_lexical(token.get("children", []))
        return create_text_node(flatten_children(children))
    elif ttype == "link":
        # For links, include the text. (You could add URL data if desired.)
        children = convert_ast_to_lexical(token.get("children", []))
        return create_text_node(flatten_children(children))
    else:
        # Fallback: if token has children, flatten them; otherwise, use its text.
        if "children" in token:
            children = convert_ast_to_lexical(token.get("children", []))
            return create_text_node(flatten_children(children))
        else:
            print ("skipping", token)
            # return create_text_node(token.get("raw", ""))


def markdown_to_lexical(markdown_text):
    # Create a Mistune Markdown instance using the AST renderer from mistune.plugins.ast.
    md = mistune.create_markdown(renderer=None)
    ast = md(markdown_text)
    # Convert the AST (a list of tokens) into Lexical nodes.
    lexical_nodes = convert_ast_to_lexical(ast)
    # Wrap the nodes in a root node representing the editor state.
    editor_state = {
        "root": {
            "children": lexical_nodes,
            "type": "root",
            "direction": "ltr",
            "format": "",
            "indent": 0,
            "version": 1
        }
    }
    print(json.dumps(editor_state, indent=2))
    return editor_state


if __name__ == "__main__":
    sample_markdown = """# Description

This is an example file

# Authors

* Nate Vack
* Vendor Packages
    * docopt
    * CommonMark-py

# Versions

## Version 1

Here's something about Version 1; I said "Hooray!"

## Version 2

Here's something about Version 2
"""
    state = markdown_to_lexical(sample_markdown)
    print(json.dumps(state, indent=2))
