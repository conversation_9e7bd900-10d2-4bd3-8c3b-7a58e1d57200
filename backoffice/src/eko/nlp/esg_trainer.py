import os

import joblib
import numpy as np
import pandas as pd
import psycopg
import torch
from sklearn.metrics import f1_score, classification_report
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import MultiLabelBinarizer
from transformers import DistilBertTokenizerFast, DistilBertForSequenceClassification, TrainingArguments, Trainer


# Define the Dataset class
class ESGDataset(torch.utils.data.Dataset):
    def __init__(self, encodings, labels):
        self.encodings = encodings
        self.labels = labels

    def __getitem__(self, idx):
        item = {
            key: torch.tensor(val[idx]) for key, val in self.encodings.items()
        }
        item['labels'] = torch.tensor(self.labels[idx], dtype=torch.float)
        return item

    def __len__(self):
        return len(self.labels)

# Function to compute metrics
def compute_metrics(pred):
    labels = pred.label_ids
    preds = pred.predictions

    sigmoid = torch.nn.Sigmoid()
    probs = sigmoid(torch.Tensor(preds))
    y_pred = np.zeros(probs.shape)
    y_pred[np.where(probs >= 0.5)] = 1

    f1_micro = f1_score(labels, y_pred, average='micro', zero_division=0)
    f1_macro = f1_score(labels, y_pred, average='macro', zero_division=0)

    return {
        'f1_micro': f1_micro,
        'f1_macro': f1_macro,
    }


# Function to train the model
def train_model(
        db_config,
        model_save_path,
        num_epochs=3,
        batch_size=8,
        max_length=512,
):
    """
    Trains the DistilBERT model for multi-label classification using data from PostgreSQL.

    Parameters:
    - db_config (dict): Database connection parameters.
    - model_save_path (str): Directory to save the trained model and tokenizer.
    - num_epochs (int): Number of training epochs.
    - batch_size (int): Batch size for training and evaluation.
    - max_length (int): Maximum sequence length for tokenization.

    Returns:
    - None
    """
    # Step 1: Connect to the database
    conn = psycopg.connect(**db_config)
    cursor = conn.cursor()

    # Step 2: Retrieve data
    # SQL query to join kg_document_chunks and kg_document_chunk_issue_map
    query = """
    SELECT c.id AS chunk_id, c.chunk_text, m.issue
    FROM public.kg_document_chunks c
    JOIN public.kg_document_chunk_issue_map m ON c.id = m.doc_chunk_id
    WHERE c.chunk_text IS NOT NULL AND c.chunk_text != ''
    LIMIT 100000
    """
    cursor.execute(query)
    records = cursor.fetchall()
    cursor.close()
    conn.close()

    # Step 3: Process data into texts and labels
    # Create a DataFrame from the records
    df = pd.DataFrame(records, columns=['chunk_id', 'chunk_text', 'issue'])

    # Group by chunk_id and aggregate issues
    grouped = df.groupby(['chunk_id', 'chunk_text'])['issue'].apply(list).reset_index()

    texts = grouped['chunk_text'].tolist()
    labels = grouped['issue'].tolist()

    # Binarize labels
    mlb = MultiLabelBinarizer()
    Y = mlb.fit_transform(labels)
    label_names = mlb.classes_

    # Save the MultiLabelBinarizer for later use
    if not os.path.exists(model_save_path):
        os.makedirs(model_save_path)
    joblib.dump(mlb, os.path.join(model_save_path, 'mlb.pkl'))

    # Split data
    X_train, X_test, Y_train, Y_test = train_test_split(
        texts, Y, test_size=0.2, random_state=42
    )

    # Tokenization
    tokenizer = DistilBertTokenizerFast.from_pretrained('distilbert-base-uncased')
    train_encodings = tokenizer(
        X_train, truncation=True, padding=True, max_length=max_length
    )
    test_encodings = tokenizer(
        X_test, truncation=True, padding=True, max_length=max_length
    )

    # Create datasets
    train_dataset = ESGDataset(train_encodings, Y_train)
    test_dataset = ESGDataset(test_encodings, Y_test)

    # Model
    model = DistilBertForSequenceClassification.from_pretrained(
        'distilbert-base-uncased',
        num_labels=Y.shape[1],
        problem_type="multi_label_classification",
    )

    # Training arguments
    training_args = TrainingArguments(
        output_dir=model_save_path,
        num_train_epochs=num_epochs,
        per_device_train_batch_size=batch_size,
        per_device_eval_batch_size=batch_size,
        evaluation_strategy='epoch',
        save_strategy='epoch',
        logging_dir='./logs',
        logging_steps=10,
        load_best_model_at_end=True,
    )

    # Trainer
    trainer = Trainer(
        model=model,
        args=training_args,
        train_dataset=train_dataset,
        eval_dataset=test_dataset,
        compute_metrics=compute_metrics,
    )

    # Train
    trainer.train()

    # Evaluate
    eval_results = trainer.evaluate()
    print("Evaluation results:", eval_results)

    # Detailed report
    predictions = trainer.predict(test_dataset)
    probs = torch.nn.Sigmoid()(torch.Tensor(predictions.predictions))
    y_pred = np.zeros(probs.shape)
    y_pred[np.where(probs >= 0.5)] = 1

    print("Classification Report:")
    print(classification_report(Y_test, y_pred, target_names=label_names, zero_division=0))

    # Save the model and tokenizer
    model.save_pretrained(model_save_path)
    tokenizer.save_pretrained(model_save_path)
    print(f"Model and tokenizer saved to {model_save_path}")
