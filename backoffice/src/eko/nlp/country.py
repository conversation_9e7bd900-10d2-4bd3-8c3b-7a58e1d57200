import functools
import os
import re
import traceback

import pycountry
from loguru import logger

from eko import eko_var_path

missing_countries_list_file = os.path.join(eko_var_path, "missing_countries.txt")

us_states = {
    "alabama": "al", "alaska": "ak", "arizona": "az", "arkansas": "ar",
    "california": "ca", "colorado": "co", "connecticut": "ct", "delaware": "de",
    "florida": "fl", "georgia": "ga", "hawaii": "hi", "idaho": "id",
    "illinois": "il", "indiana": "in", "iowa": "ia", "kansas": "ks",
    "kentucky": "ky", "louisiana": "la", "maine": "me", "maryland": "md",
    "massachusetts": "ma", "michigan": "mi", "minnesota": "mn", "mississippi": "ms",
    "missouri": "mo", "montana": "mt", "nebraska": "ne", "nevada": "nv",
    "new hampshire": "nh", "new jersey": "nj", "new mexico": "nm", "new york": "ny",
    "north carolina": "nc", "north dakota": "nd", "ohio": "oh", "oklahoma": "ok",
    "oregon": "or", "pennsylvania": "pa", "rhode island": "ri",
    "south carolina": "sc", "south dakota": "sd", "tennessee": "tn", "texas": "tx",
    "utah": "ut", "vermont": "vt", "virginia": "va", "washington": "wa",
    "west virginia": "wv", "wisconsin": "wi", "wyoming": "wy"
}

@functools.lru_cache(maxsize=1024)
def is_us_state(text:str)->bool:
    # List of U.S. states and abbreviations
    if text is None:
        return False

    # Normalize the input (case insensitive)
    text = text.strip()
    text=text.lower()
    if text.startswith("us-"):
        text = text[3:]

    # Check if the input is a state name or abbreviation
    if text in us_states.keys() or text in us_states.values():
        logger.info(f"Matched {text}")
        return True
    return False

other_countries = {
    "england and wales": "GB",
    "england & wales": "GB",
    "scotland": "GB",
    "scotland (uk)": "GB",
    "northern ireland": "GB",
    "united kingdom": "GB",
    "great britain": "GB",
    "england": "GB",
    "wales": "GB",
    "bulgarie": "BG",
    "turkey": "TR",
    "hong kong, special administrative region, china": "hk",
    "hong kong, s.a.r": "hk",
    "california, usa": "US",
    "state of delaware": "US",
    "united states - delaware": "US",
    "australian corporation": "AU",
    "german corporation": "DE",
}

for state in us_states.keys():
    other_countries["state of "+state] = "US"
    other_countries["united states - "+state] = "US"
    other_countries[state+" corporation"] = "US"

@functools.lru_cache(maxsize=1024)
def country_name_to_code(country_name:str):

    if country_name is None:
        return None
    if len(country_name) > 2:
        country_name=country_name.title().strip()
    if country_name.endswith("corporation"):
        country_name=country_name[:-11].strip()
    if len(country_name) == 2:
        return country_name.upper()

    try:
        if len(country_name) == 2:
            country = pycountry.countries.get(alpha_2=re.sub("\W+","",country_name))
            if country:
                logger.info(f"The ISO alpha-2 code for '{country_name}' is: { country.alpha_2}")
                return country.alpha_2
        # Look up the country by name
        country = pycountry.countries.get(name=country_name)
        if country:
            logger.info(f"The ISO alpha-2 code for '{country_name}' is: { country.alpha_2}")
            return country.alpha_2
        countries = pycountry.countries.search_fuzzy(country_name)
        if len(countries) > 0:
            logger.info(f"The ISO alpha-2 code for '{country_name}' is: { countries[0].alpha_2}")
            return countries[0].alpha_2
        traceback.print_stack()
        raise ValueError(f"Could not find a country with the name:"+country_name)
    except Exception as e:
        if other_countries.get(country_name.lower()):
            logger.info(f"The ISO alpha-2 code for '{country_name}' is: { other_countries.get(country_name.lower())}")
            return other_countries.get(country_name.lower())
        else:
            logger.exception(e)
            logger.error(f"Could not find a country with the name '{country_name}'")
            with open(missing_countries_list_file, "a") as f:
                f.write(country_name.lower() + "\n")
    return None


# Example usage
if __name__ == "__main__":
    country_name = "United Kingdom"
    country_code = country_name_to_code(country_name)
    print(f"The ISO alpha-2 code for '{country_name}' is: {country_code}")
