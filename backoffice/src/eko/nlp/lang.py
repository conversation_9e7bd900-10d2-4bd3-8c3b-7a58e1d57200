import langdetect
import requests
from bs4 import BeautifulSoup

def common_word_text():
    """
    This function returns a string of text made only from common words. It is used to check against overmatching text.

    """


    return """
The day began like any other. The sun rose in the sky, casting golden light over the land. Birds sang in the trees, their melodies filling the morning air. The air was fresh and clear, carrying the scent of flowers and grass. People woke up, stretched, and got ready for the day ahead. Children prepared for school, packing their bags with books and pencils, and eating breakfast with their families. Parents sipped hot coffee and read the news, thinking about the tasks they had to do. The sound of dishes clinking and soft chatter filled the homes.

In the town, shops opened their doors wide. Workers arrived at their jobs, greeting each other with smiles and friendly words. The streets filled with cars, buses, and bikes, moving people from one place to another. Life moved at its usual pace, a steady flow of activity and motion.

At the school, children sat in their classes, listening to their teachers with eager eyes. They learned about the world, numbers, words, history, and stories. They raised their hands to ask questions and shared ideas with one another. During break time, they ran outside to play games, swing on swings, and laugh with friends. The sound of joy and excitement filled the air.

Meanwhile, in the park, some people walked their dogs, enjoying the companionship and the open space. Others sat on benches, reading books or simply enjoying the warm sun and cool breeze. A group of friends played with a ball, passing it back and forth with cheers. Families had picnics on the green grass, sharing food, stories, and time together. Children chased butterflies and flew kites, their faces lit up with happiness.

In the afternoon, clouds began to gather in the once clear sky. The blue turned to gray, and a soft rain started to fall. People opened umbrellas or sought shelter under trees and roofs. The rain made the air smell fresh and clean, and drops danced on the ground, creating tiny rivers along the streets. Some children splashed in puddles, their laughter brightening the mood despite the wet weather.

As evening came, the rain stopped, and the sun peeked out once more from behind the clouds. A beautiful rainbow appeared, stretching across the sky in a colorful arc. People stopped to look, pointing and smiling at the sight. It was a moment of wonder shared by all, a reminder of the beauty in the world.

At home, families gathered for dinner around the table. They talked about their day, sharing stories, thoughts, and feelings. They ate together, enjoying the warm food and each other’s company. After dinner, some watched TV shows, others read books, and some played games or listened to music. The soft glow of lights shone from the windows, casting a warm glow into the night.

Night fell, and the stars came out, twinkling like tiny diamonds in the dark sky. The moon shone bright, casting a gentle light over the sleeping town. People went to bed, tucking themselves under cozy blankets, closing their eyes and drifting into dreams. The world grew quiet and still, the only sounds the soft rustling of leaves and distant hoots of owls.

In the silence of the night, the cycle of life continued. The wind whispered through the trees, and the animals rested. All was peaceful until the dawn would come again, bringing with it a new day full of hope and possibilities.
"""

def detect_language(url):
    response = requests.get(url)

    detected_language = detect_lang_from_response(url, response.content, response.headers)
    return detected_language


def detect_lang_from_response(url, content, headers):
    # Parse the HTML content using BeautifulSoup
    soup = BeautifulSoup(content, 'html.parser')
    # 1. Check the 'lang' attribute in the <html> tag
    html_language = None
    html_tag = soup.find('html')
    if html_tag and html_tag.has_attr('lang'):
        html_language = html_tag['lang']
        # print(f"Language from <html> tag: {html_language}")
    # 2. Look for <meta> tags related to language
    meta_language = None
    for meta in soup.find_all('meta'):
        if 'content-language' in meta.get('http-equiv', '').lower():
            meta_language = meta['content']
            # print(f"Language from <meta> tag: {meta_language}")
            break
    # 3. Check for any headers related to language in the response (e.g., Content-Language)
    header_language = headers.get('Content-Language')
    # if header_language:
        # print(f"Language from HTTP headers: {header_language}")

    # Combine all possible sources of language information
    if html_language:
        detected_language = html_language
    elif meta_language:
        detected_language = meta_language
    elif header_language:
        detected_language = header_language
    else:
        detected_language = langdetect.detect(content) or "en"
    return detected_language
