import os

import requests


def send_to_slack(message: str):
    webhook_url = os.getenv("SLACK_WEBHOOK_URL", "*********************************************************************************")
    if not webhook_url:
        raise ValueError("Slack webhook URL is not defined in environment variables")

    payload = {
        "text": message,
    }

    response = requests.post(webhook_url, json=payload, headers={"Content-Type": "application/json"})

    if not response.ok:
        error_message = response.text
        raise Exception(f"Slack webhook error: {error_message}")

    return {"success": True}
