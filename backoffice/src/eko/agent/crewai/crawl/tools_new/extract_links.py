"""
Extract links tool for the web crawling crew.
"""

from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from . import key_terms
from .base import web_tools


class ExtractLinksInput(BaseModel):
    """Input schema for the extract_links tool."""
    url: str = Field(..., description="URL of the webpage to extract links from")
    redo: str = Field(..., description="Set to 'yes' to force re-extraction even if this URL was already processed")


class ExtractLinksTool(BaseTool):
    """Tool for extracting links from a webpage."""
    
    name: str = "extract_links"
    description: str = "Extract links from a webpage. Use parameter redo=True to force re-extraction from a previously processed URL."
    args_schema: Type[BaseModel] = ExtractLinksInput
    _memory_manager: Optional[Any] = None
    
    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Cache for URLs we've already processed
        self._processed_urls: Set[str] = set()
    
    def _run(self, url: str, redo: str ) -> str:
        """
        Extract links from a webpage.

        Args:
            url: URL of the webpage to extract links from
            redo: Whether to force re-extraction even if this URL was already processed

        Returns:
            str: The extracted links
        """
        try:
            if not url:
                return "Error: You must provide a URL to extract links from"
            
            # Check if we've already processed this URL and redo is False
            if  redo != "yes" and url in self._processed_urls:
                return "We've already extracted links from this URL before. Use redo=True if you want to extract them again."
            
            # Add to our processed URLs
            self._processed_urls.add(url)
            
            # First fetch the webpage
            page_content = web_tools.fetch_webpage(url)
            if not page_content:
                return f"Failed to fetch content from {url}"
            
            # Then extract links
            links = web_tools.extract_links(page_content)
            
            if not links:
                return "No links found on the page."

            # Categorize links
            pdf_links = []
            doc_links = []
            potential_report_links = []
            other_links = []

            report_keywords = ['report', 'annual', 'sustainability', 'esg', 'financial', 'results',
                              'presentation', 'investor', 'filing', 'statement', 'disclosure'] + list(key_terms)

            for link in links:
                link_url = link.url.lower()
                text = (link.text or "").lower()

                if link_url.endswith('.pdf') or 'pdf' in link_url:
                    pdf_links.append(link)
                # elif any(ext in link_url for ext in ['.doc', '.docx', '.xls', '.xlsx', '.ppt', '.pptx']):
                #     doc_links.append(link)
                elif any(keyword in text or keyword in link_url for keyword in report_keywords):
                    potential_report_links.append(link)
                else:
                    other_links.append(link)

            formatted_links = []

            # Format PDF links (highest priority)
            if pdf_links:
                formatted_links.append("PDF DOCUMENTS (HIGH PRIORITY):")
                for i, link in enumerate(pdf_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format document links (high priority)
            if doc_links:
                formatted_links.append("OTHER DOCUMENTS (HIGH PRIORITY):")
                for i, link in enumerate(doc_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format potential report links (medium priority)
            if potential_report_links:
                formatted_links.append("POTENTIAL REPORT PAGES (MEDIUM PRIORITY):")
                for i, link in enumerate(potential_report_links, 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

            # Format other links (limited to 20)
            if other_links:
                formatted_links.append("OTHER LINKS (LOW PRIORITY):")
                for i, link in enumerate(other_links[:20], 1):
                    formatted_links.append(f"{i}. {link.text}")
                    formatted_links.append(f"   URL: {link.url}")
                    formatted_links.append("")

                if len(other_links) > 20:
                    formatted_links.append(f"[Showing 20 of {len(other_links)} other links]")

            return "\n".join(formatted_links)
        except Exception as e:
            logger.error(f"Error extracting links: {e}")
            return f"Error extracting links: {str(e)}"
