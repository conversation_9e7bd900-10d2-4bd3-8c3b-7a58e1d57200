"""
Search web tool for the web crawling crew.
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.entities.gleif import lookup_leis_by_company_name


class SearchGLEIFInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The company name to search for")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchGLEIFTool(BaseTool):
    """Tool for searching GLEIF."""
    
    name: str = "search_gleif"
    description: str = "Search GLEIF (LEI) database for company information"
    args_schema: Type[BaseModel] = SearchGLEIFInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            return jsonpickle.encode((lookup_leis_by_company_name(query, 3,min(100, num_results), 1)), unpicklable=False)
        except Exception as e:
            logger.error(f"Error searching GLEIF: {e}")
            return f"Error searching GLEIF: {str(e)}"
