"""
Search web tool for the web crawling crew.
"""

from typing import Type

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from .base import web_tools


class SearchWebInput(BaseModel):
    """Input schema for the search_web tool."""
    query: str = Field(..., description="The search query")
    num_results: int = Field(20, description="Number of search results to return (default: 20)")


class SearchWebTool(BaseTool):
    """Tool for searching the web."""
    
    name: str = "search_web"
    description: str = "Search the web for information"
    args_schema: Type[BaseModel] = SearchWebInput
    
    def _run(self, query: str, num_results: int = 20) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            results = web_tools.search_web(query, num_results)
            formatted_results = []

            for i, result in enumerate(results, 1):
                formatted_results.append(f"{i}. {result['title']}")
                formatted_results.append(f"   URL: {result['link']}")
                formatted_results.append(f"   Snippet: {result['snippet']}")
                formatted_results.append("")

            return "\n".join(formatted_results)
        except Exception as e:
            logger.error(f"Error searching web: {e}")
            return f"Error searching web: {str(e)}"
