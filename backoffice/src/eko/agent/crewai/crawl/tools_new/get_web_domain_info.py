"""
Search web tool for the web crawling crew.
"""
from typing import Type

import jsonpickle
from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.domains.domain_categorizer import get_domain_info


class GetWebDomainInfoInput(BaseModel):
    """Input schema for the search_web tool."""
    domain: str = Field(..., description="The domain of a webpage or website")


class GetWebDomainInfoTool(BaseTool):
    """Tool for getting information about a web domain."""
    
    name: str = "get_domain_info"
    description: str = "Get meta information about a web domain"
    args_schema: Type[BaseModel] = GetWebDomainInfoInput
    
    def _run(self, domain: str) -> str:
        """
        Search the web for information.

        Args:
            query: The search query
            num_results: Number of search results to return

        Returns:
            str: The search results
        """
        try:
            results = get_domain_info(domain=domain)
            return jsonpickle.encode(results, unpicklable=False)
        except Exception as e:
            logger.error(f"Error searching web: {e}")
            return f"Error searching web: {str(e)}"
