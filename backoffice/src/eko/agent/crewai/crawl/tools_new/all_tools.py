from .analyze_text_for_relevance import AnalyzeTextForRelevanceTool
from .download_pdf import DownloadTool
from .extract_esg_insights import ExtractESGInsightsTool
from .extract_links import ExtractLinksTool
from .fetch_webpage import FetchWebpageTool
from .generate_search_queries import GenerateSearchQueriesTool
from .get_web_domain_info import GetWebDomainInfoTool
from .search_ch import SearchCHTool
from .search_gleif import SearchGLEIFTool
from .search_sec import SearchSECTool
from .search_web import SearchWebTool
from .search_wikipedia import SearchWikipediaTool
from .summarize_content import SummarizeContentTool
from ..memory import CrewMemoryManager
from ..tools_mem import get_memory_tools


def get_web_tools(memory_manager: CrewMemoryManager):
    """
    Get the tools available to the agents in the web crawling crew.

    Returns:
        List: The tools as BaseTool subclasses
    """
    return [
        # SpiderTool(),
        GetWebDomainInfoTool(),
        SearchWebTool(),
        # NewsSearchTool(),
        SearchCHTool(),
        SearchGLEIFTool(),
        SearchSECTool(),
        SearchWikipediaTool(),
        FetchWebpageTool(memory_manager=memory_manager),
        DownloadTool(memory_manager=memory_manager),
        ExtractLinksTool(memory_manager=memory_manager),
        # TrackDocumentTool(),
        # ListDiscoveredDocumentsTool(),
        GenerateSearchQueriesTool(),
        ExtractESGInsightsTool(memory_manager=memory_manager),
        AnalyzeTextForRelevanceTool(memory_manager=memory_manager),
        SummarizeContentTool(memory_manager=memory_manager),
    ] + get_memory_tools(memory_manager)


def get_analyst_tools(memory_manager: CrewMemoryManager):
    """
    Get the tools available to analyst agents

    Returns:
        List: The tools as BaseTool subclasses
    """
    return [
        SearchWebTool(),
        SearchCHTool(),
        SearchGLEIFTool(),
        SearchSECTool(),
        SearchWikipediaTool(),
        FetchWebpageTool(memory_manager=memory_manager),
        DownloadTool(memory_manager=memory_manager),
        ExtractLinksTool(memory_manager=memory_manager),
        GenerateSearchQueriesTool(),
        ExtractESGInsightsTool(memory_manager=memory_manager),
        AnalyzeTextForRelevanceTool(memory_manager=memory_manager),
        SummarizeContentTool(memory_manager=memory_manager)
    ] + get_memory_tools(memory_manager)
