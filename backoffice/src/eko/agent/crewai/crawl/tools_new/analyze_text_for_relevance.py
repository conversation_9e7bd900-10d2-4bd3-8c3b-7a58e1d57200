"""
Analyze text for relevance tool for the web crawling crew.
"""

import hashlib
from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class AnalyzeTextForRelevanceInput(BaseModel):
    """Input schema for the analyze_text_for_relevance tool."""
    text: str = Field(..., description="The text to analyze")
    topic: str = Field(..., description="The topic to analyze for relevance")
    redo:str = Field(..., description="Set to 'yes'' to force re-analysis even if this text was already analyzed")


class AnalyzeTextForRelevanceTool(BaseTool):
    """Tool for analyzing text for relevance to a specific topic."""
    
    name: str = "analyze_text_for_relevance"
    description: str = "Analyze text for relevance to a specific topic. Use parameter redo=True to force re-analysis of previously analyzed text."
    args_schema: Type[BaseModel] = AnalyzeTextForRelevanceInput
    _memory_manager: Optional[Any] = None
    
    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager')
        super().__init__(**kwargs)
        self._memory_manager = memory_manager
        # Cache for text hashes we've already processed
        self._processed_text_hashes: Set[str] = set()
    
    def _get_text_hash(self, text: str, topic: str) -> str:
        """Generate a hash for the text+topic combo to identify if we've seen it before"""
        # Use first 1000 chars to keep hash computation fast while still being unique enough
        text_sample = text[:1000] if len(text) > 1000 else text
        combined = f"{text_sample}:{topic}"
        return hashlib.md5(combined.encode()).hexdigest()
    
    def _run(self, text: str, topic: str, redo: str) -> str:
        """
        Analyze text for relevance to a specific topic.

        Args:
            text: The text to analyze
            topic: The topic to analyze for relevance
            redo: Whether to force re-analysis even if this text was already analyzed

        Returns:
            str: The analysis results
        """
        try:
            # Check if we've already processed this text with the same parameters
            text_hash = self._get_text_hash(text, topic)
            if  redo !='yes' and text_hash in self._processed_text_hashes:
                return "We've already analyzed this text for this topic before. Use redo=True if you want to analyze it again."
            
            # Add to our processed texts
            self._processed_text_hashes.add(text_hash)
            
            logger.info(f"Analyzing text relevance for topic: {topic}")

            # Truncate if too long
            if len(text) > 50000:
                text = text[:50000] + "...\n[Content truncated due to length]"

            # Simple keyword-based relevance analysis
            topic_keywords = topic.lower().split()

            # Count keyword occurrences
            keyword_count = sum(1 for keyword in topic_keywords if keyword.lower() in text.lower())

            # Calculate a simple relevance score (0-10)
            relevance_score = min(10, keyword_count * 2)

            # Find relevant excerpts
            sentences = text.split(". ")
            relevant_excerpts = [s for s in sentences if any(k.lower() in s.lower() for k in topic_keywords)]

            # Format results
            result = f"Relevance Analysis for Topic: {topic}\n\n"
            result += f"Relevance Score: {relevance_score}/10\n\n"

            if relevance_score > 5:
                result += "Explanation: The text contains multiple references to the topic keywords.\n\n"
            else:
                result += "Explanation: The text contains few or no references to the topic keywords.\n\n"

            result += "Key Excerpts:\n"
            for i, excerpt in enumerate(relevant_excerpts[:5], 1):
                result += f"{i}. {excerpt}.\n"

            return result
        except Exception as e:
            logger.error(f"Error analyzing text relevance: {e}")
            return f"Error analyzing text relevance: {str(e)}"
