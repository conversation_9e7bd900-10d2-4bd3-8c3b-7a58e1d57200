"""
Summarize content tool for the web crawling crew.
"""

import hashlib
from typing import Type, Optional, Any, Set

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field


class SummarizeContentInput(BaseModel):
    """Input schema for the summarize_content tool."""
    text: str = Field(..., description="The text to summarize")
    max_length: int = Field(default=500, description="Maximum length of the summary in words")
    redo: Optional[bool] = Field(False, description="Set to True to force re-summarization even if this text was already summarized")


class SummarizeContentTool(BaseTool):
    """Tool for summarizing content to a specified maximum length."""

    name: str = "summarize_content"
    description: str = "Summarize content to a specified maximum length. Use parameter redo=True to force re-summarization of previously summarized text."
    args_schema: Type[BaseModel] = SummarizeContentInput
    memory_manager: Optional[Any] = None

    def __init__(self, **kwargs):
        memory_manager = kwargs.pop('memory_manager', None) if 'memory_manager' in kwargs else None
        super().__init__(**kwargs)
        # Store reference to memory_manager
        self._memory_manager = memory_manager
        # Cache for text hashes we've already processed
        self._processed_text_hashes: Set[str] = set()

    def _get_text_hash(self, text: str, max_length: int) -> str:
        """Generate a hash for the text+max_length combo to identify if we've seen it before"""
        # Use first 1000 chars to keep hash computation fast while still being unique enough
        text_sample = text[:1000] if len(text) > 1000 else text
        combined = f"{text_sample}:{max_length}"
        return hashlib.md5(combined.encode()).hexdigest()

    def _run(self, text: str, max_length: int = 500, redo: bool = False) -> str:
        """
        Summarize content to a specified maximum length.

        Args:
            text: The text to summarize
            max_length: Maximum length of the summary in words
            redo: Whether to force re-summarization even if this text was already summarized

        Returns:
            str: The summarized content
        """
        try:
            # Check if we've already processed this text with the same parameters
            text_hash = self._get_text_hash(text, max_length)
            if not redo and text_hash in self._processed_text_hashes:
                return "We've already summarized this text before. Use redo=True if you want to summarize it again."
            
            # Add to our processed texts
            self._processed_text_hashes.add(text_hash)
            
            logger.info(f"Summarizing content to {max_length} words")

            # Truncate if too long
            if len(text) > 50000:
                text = text[:50000] + "...\n[Content truncated due to length]"

            # Simple extractive summarization
            sentences = text.split(". ")

            # Calculate the number of sentences to include based on max_length
            # Assuming average sentence length of 20 words
            num_sentences = min(len(sentences), max(1, max_length // 20))

            # Take the first sentence (often contains the main point)
            summary = [sentences[0]]

            # Take evenly distributed sentences from the rest of the text
            if len(sentences) > 1 and num_sentences > 1:
                step = len(sentences) // (num_sentences - 1)
                for i in range(1, num_sentences):
                    idx = min(i * step, len(sentences) - 1)
                    summary.append(sentences[idx])

            # Join the sentences
            summary_text = ". ".join(summary)

            # Add a period at the end if needed
            if not summary_text.endswith("."):
                summary_text += "."

            return f"Summary ({len(summary)} sentences):\n\n{summary_text}"
        except Exception as e:
            logger.error(f"Error summarizing content: {e}")
            return f"Error summarizing content: {str(e)}"
