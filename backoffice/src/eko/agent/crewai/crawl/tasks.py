"""
Task definitions for the web crawling crew.

This module defines the tasks assigned to the agents in the web crawling crew:
- Research tasks: For gathering information about the target company
- Document discovery tasks: For finding and downloading documents
- Analysis tasks: For analyzing the gathered information for ESG insights
- Summary task: For summarizing the findings and generating a final report
"""

from typing import List

from crewai import Task

from eko.agent.crewai.crawl.agents import JournalistAgent, ResearchAgent, AnalysisAgent, SummaryAgent, ReportAgent
from eko.agent.crewai.crawl.memory import CrewMemoryManager
from eko.agent.crewai.crawl.tools_new import company_wrong_doing
from eko.agent.crewai.crawl.tools_new.all_tools import get_web_tools, get_analyst_tools
from eko.domains.domain_queries import get_search_journalism_allow_domains


def journalist_site_list():
    return [ f"site:{domain}" for domain in get_search_journalism_allow_domains()[:5]]

def create_research_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50, ) -> List[
    Task]:
    """
    Create research tasks for gathering information about the target company.

    Args:
        company_name: The name of the target company

    Returns:
        List[Task]: The research tasks
    """
    tasks = [Task(
        description=(
            f"Look for third party reports {company_name} from activist, pressure groups and NGOs. "
            f"Use the `search_web` tool with specific queries targeting: "
            f" {','.join(company_wrong_doing(company_name))} "
            f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
            f"Pay special attention to sustainability sections of the company website and ESG data providers "
            f"or reporting frameworks like GRI, SASB, CDP, etc. "
            f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
            f"Document all your findings, especially noting any downloaded documents."
        ),
        expected_output=(
            "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
            "found for the company, including the URL of each document, its title/description, the source "
            "where it was found, and a brief summary of its content if available."
        ),
        agent=ResearchAgent(get_web_tools(memory_manager)),
        async_execution=False
    ),
        Task(
            description=(
                f"Conduct an exhaustive search for information about {company_name}. "
                f"Use search_ch, search_sec, search_gleif, search_wikipedia to get key business information, "
                f"Track this with track_insight."
                f"Then start with general searches about the company, then progressively refine your searches "
                f"based on what you learn. Your primary goal is to identify as many potential sources of "
                f"documents and reports about the company and it's activities as possible. "
                f"Use the search_web tool extensively with different queries to find various sources. "
                f"For each promising page, use the fetch_webpage tool to visit it and extract_links to "
                f"find more potential document sources. "
                f"Pay special attention to the company's official website, investor relations section, "
                f"sustainability pages, annual reports, and any regulatory filings. "
                f"Document all your findings, especially noting any potential document repositories "
                f"or pages that might contain downloadable files. "
                f"Be relentless and thorough - try at least 15-20 different search queries to ensure "
                f"comprehensive coverage. Use information you discover to generate new search ideas."
            ),
            expected_output=(
                "A comprehensive list of potential document sources, including URLs to the company's "
                "website sections, investor relations pages, sustainability reports, regulatory filings, "
                "and any other pages that might contain downloadable documents. Include a list of at least "
                "15-20 different search queries you used and what you discovered from each. Please run download"
            ),
            agent=AnalysisAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Explore {company_name}'s official website thoroughly to find all downloadable documents. "
                f"Start by searching for the company's main website and navigating to it using the fetch_webpage tool. "
                f"Use extract_links to identify all sections of the website, paying special attention to: "
                f"- Investor Relations section "
                f"- About Us pages "
                f"- Sustainability/ESG/CSR sections "
                f"- Media/Press/News sections "
                f"- Publications/Resources sections "
                f"- Annual Reports pages "
                f"For each relevant section, use `fetch_webpage` to visit it and `extract_links` again to find "
                f"deeper pages. Look specifically for links that might be PDFs or articles. "
                f"For any PDF or article links you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Be extremely thorough - explore every section of the website that might contain documents. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all documents found on the company's official website, "
                "including the URL of each document, its title/description, the section of the website "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Search for {company_name}'s regulatory filings, annual reports, and investor presentations. "
                f"Use the `search_web` tool with specific queries targeting: "
                f"- \"{company_name} annual report\" "
                f"- \"{company_name} investor presentation\" "
                f"- \"{company_name} financial results\" "
                f"- \"{company_name} SEC filings\" (if a US company) "
                f"- \"{company_name} regulatory filings\" "
                f"- \"{company_name} earnings call transcript\" "
                f"- \"{company_name} investor day\" "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to investor relations sections of the company website and financial "
                f"data providers like SEC (for US companies), Companies House (for UK companies), or "
                f"similar regulatory databases. "
                f"For any PDF links you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all regulatory filings, annual reports, and investor presentations "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Search for {company_name}'s sustainability reports, ESG reports, and CSR publications. "
                f"Use the `search_web` tool with specific queries targeting: "
                f"- \"{company_name} sustainability report\" "
                f"- \"{company_name} ESG report\" "
                f"- \"{company_name} corporate social responsibility\" "
                f"- \"{company_name} environmental report\" "
                f"- \"{company_name} social impact\" "
                f"- \"{company_name} climate change\" "
                f"- \"{company_name} carbon footprint\" "
                f"- \"{company_name} diversity inclusion report\" "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to sustainability sections of the company website and ESG data providers "
                f"or reporting frameworks like GRI, SASB, CDP, etc. "
                f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),

        Task(
            description=(
                f"Search for news articles, press releases, and media coverage about {company_name}'s ESG activities. "
                f"Use the `generate_search_queries` to create queries and then the `search_web tool` with the supplied queries."
                f"You can also use the site: prefix to choose the site you want to search, e.g. site:news.bbc.co.uk,site:guardian.co.uk{', '.join(journalist_site_list())} etc." 
                f"For each relevant result, use `fetch_webpage` to visit the page and extract the content. "
                f"Also use `extract_links` to find any related articles or downloadable documents. "
                f"For any PDFs or articles you find, use the `download_pdf_or_article` tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents or significant news stories."
            ),
            expected_output=(
                "A list of downloaded news articles, press releases, and media coverage about "
                "the company's ESG activities and impacts, including the URL of each source, its title/headline, "
                "the publication date if available, and a brief summary of the content."
            ),
            agent=JournalistAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Look for third party reports {company_name} from activist, pressure groups and NGOs. "
                f"Use the `search_web` tool with specific queries targeting: "
                f" {','.join(company_wrong_doing(company_name))} "
                f"For each relevant result, use `fetch_webpage` to visit the page and look for downloadable documents. "
                f"Pay special attention to sustainability sections of the company website and ESG data providers "
                f"or reporting frameworks like GRI, SASB, CDP, etc. "
                f"For any PDF links you find, use the download_pdf tool to retrieve and process them. "
                f"Document all your findings, especially noting any downloaded documents."
            ),
            expected_output=(
                "A comprehensive inventory of all sustainability reports, ESG reports, and CSR publications "
                "found for the company, including the URL of each document, its title/description, the source "
                "where it was found, and a brief summary of its content if available."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Based on all the information you've gathered so far about {company_name}, generate at least "
                f"10 new, highly specific search queries that might lead to additional documents or information. "
                f"Look for patterns, names of specific initiatives, executive names, partnerships, subsidiaries, "
                f"or unique terminology used by the company that could lead to more targeted searches. "
                f"For each new query you generate, use the search_web tool to execute it, then use fetch_webpage "
                f"to visit the most promising results. "
                f"For any new pages you visit, use extract_links to find potential document links, and "
                f"download_pdf for any PDF links you discover. "
                f"Document all your findings, especially noting any new documents or information sources "
                f"that weren't found in previous searches."
            ),
            expected_output=(
                "A list of at least 10 new, highly specific search queries generated based on initial research, "
                "along with the results of each query, including any new documents or information sources discovered. "
                "Include the reasoning behind each new query and how it led to new discoveries."
            ),
            agent=JournalistAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Conduct a final, EXTREMELY THOROUGH search for any remaining documents or information about {company_name} "
                f"that might have been missed in previous searches. "
                f"Review all the information and documents you've gathered so far, and identify any potential gaps "
                f"or areas that might benefit from additional research. "
                f"Generate at least 15 more search queries targeting these gaps, and use the search_web tool to "
                f"execute them. Be creative and exhaustive in your search queries. "
                f"Also, revisit the most promising document sources you've identified and explore them more deeply "
                f"to ensure no documents were missed. Go at least 3 levels deep in any promising website. "
                f"For any new pages you visit, use extract_links to find potential document links, and "
                f"download_pdf for any PDF links you discover. "
                f"Try different search engines and specialized document repositories if available. "
                f"Your goal is to find EVERY POSSIBLE DOCUMENT related to {company_name}. "
                f"Compile a final, comprehensive inventory of all documents and information sources discovered "
                f"throughout the entire research process."
            ),
            expected_output=(
                "A final, comprehensive inventory of all documents and information sources discovered throughout "
                "the entire research process, organized by type (annual reports, sustainability reports, press releases, etc.) "
                "and including the URL, title/description, and a brief summary of each. Also include a list of any "
                "potential gaps or areas where additional research might be beneficial."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"FINAL DOCUMENT HUNT: This is your last chance to find any documents about {company_name} that might have been missed. "
                f"Use everything you've learned about the company to generate at least 10 highly specific, creative search queries "
                f"that might uncover hidden documents. Think about:"
                f"- Specific project names or initiatives mentioned in other documents"
                f"- Names of executives or board members + 'presentation' or 'report'"
                f"- Subsidiary companies or divisions + 'annual report' or 'sustainability'"
                f"- Industry-specific terms + company name + 'pdf'"
                f"- Regulatory filing codes or standards specific to their industry"
                f"- Partner organizations or joint ventures + company name + 'report'"
                f"For each query, use search_web and thoroughly explore the results, going at least 3 pages deep in search results. "
                f"For any promising page, use fetch_webpage and extract_links to find document links. "
                f"Download EVERY PDF you find using download_pdf. "
                f"Be absolutely relentless - your only goal is to find documents that others would miss."
            ),
            expected_output=(
                "A detailed report of your final document hunt, including all search queries used, "
                "all new documents discovered, and a complete inventory of ALL documents found throughout "
                "the entire research process. Include URLs, document titles, and brief descriptions for each."
            ),
            agent=ResearchAgent(get_web_tools(memory_manager)),
            async_execution=False
        )
    ]

    return tasks


def create_analysis_tasks(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> List[Task]:
    """
    Create analysis tasks for analyzing the gathered information for ESG insights.

    Args:
        company_name: The name of the target company

    Returns:
        List[Task]: The analysis tasks
    """
    tasks = [
        Task(
            description=(
                f"Analyze all the documents and information gathered about {company_name} to identify "
                f"patterns and generate ideas for additional document searches. "
                f"Review the content of all downloaded documents and extracted information, looking for: "
                f"- Names of specific ESG initiatives or programs "
                f"- Names of executives or teams responsible for ESG "
                f"- Partnerships with sustainability organizations "
                f"- Subsidiaries or affiliated companies that might have their own reports "
                f"- Specific terminology or frameworks used in ESG reporting "
                f"- References to other documents or reports that might not have been found yet "
                f"Based on this analysis, generate at least 10 new, highly specific search queries "
                f"that might lead to additional documents. "
                f"For each new query, provide a clear rationale based on the information discovered "
                f"in the existing documents."
            ),
            expected_output=(
                "A list of at least 10 new, highly specific search queries generated based on analysis "
                "of the existing documents, along with a clear rationale for each query explaining how "
                "it relates to information found in the existing documents and why it might lead to "
                "additional valuable documents."
            ),
            agent=AnalysisAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        ),
        Task(
            description=(
                f"Create a comprehensive document inventory for {company_name} based on all the research conducted. "
                f"Review all the documents that have been discovered and downloaded, and organize them into a "
                f"structured inventory with the following information for each document: "
                f"1. Document Title/Name "
                f"2. Document Type (Annual Report, Sustainability Report, Press Release, etc.) "
                f"3. Publication Date (if available) "
                f"4. URL/Source "
                f"5. Brief Description of Content "
                f"6. Key ESG Topics Covered "
                f"Organize the inventory by document type and publication date (if available). "
                f"Also identify any notable gaps in the document collection - are there any types of "
                f"documents or time periods that appear to be missing?"
            ),
            expected_output=(
                "A comprehensive, well-organized inventory of all documents discovered for the company, "
                "structured by document type and including all the requested information for each document. "
                "Also include an analysis of any notable gaps in the document collection and recommendations "
                "for additional document searches that might fill these gaps."
            ),
            agent=SummaryAgent(get_analyst_tools(memory_manager)),
            async_execution=False
        )
    ]

    return tasks


def create_summary_task(memory_manager: CrewMemoryManager, company_name: str, max_iterations: int = 50) -> Task:
    """
    Create a summary task for generating a final ESG report.

    Args:
        company_name: The name of the target company

    Returns:
        Task: The summary task
    """
    return Task(
        description=(
            f"Create a comprehensive document discovery report for {company_name} based on all the research "
            f"and analysis conducted. The report should include the following sections:\n"
            f"1. Executive Summary: A brief overview of the document discovery process and key findings\n"
            f"2. Research Methodology: Description of the search strategies and tools used\n"
            f"3. Document Inventory: Comprehensive listing of all documents discovered, organized by type\n"
            f"   - Annual Reports and Financial Filings\n"
            f"   - Sustainability and ESG Reports\n"
            f"   - Press Releases and News Articles\n"
            f"   - Corporate Presentations and Fact Sheets\n"
            f"   - Other Documents\n"
            f"4. Key Insights: Initial observations about the company's ESG practices based on the documents\n"
            f"5. Document Gaps: Analysis of any notable gaps in the document collection\n"
            f"6. Search Queries: List of all search queries used during the research process\n"
            f"7. Recommendations: Suggestions for additional research or document sources\n\n"
            f"For each document in the inventory, include the title, publication date, URL, and a brief "
            f"description of the content. The report should be well-structured, comprehensive, and provide "
            f"a clear overview of all documents discovered during the research process."
        ),
        expected_output=(
            "A comprehensive, well-structured document discovery report that provides a complete inventory "
            "of all documents found during the research process, along with analysis of the document collection "
            "and recommendations for further research."
        ),
        agent=ReportAgent(get_analyst_tools(memory_manager)),
        async_execution=False
    )
