"""Tool for tracking discovered insights."""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class TrackInsightSchema(BaseModel):
    """Input schema for the track insight tool."""
    category: str = Field(description="The category of the insight (e.g., 'Environmental', 'Social', 'Governance')")
    behavior_type: str = Field(description="The type of behavior (e.g., 'Positive', 'Negative', 'Neutral')")
    description: str = Field(description="A detailed description of the insight")
    source_url: str = Field(description="The URL where the insight was found")
    source_title: str = Field(description="The title of the source")
    confidence: float = Field(description="The confidence level (0.0 to 1.0)")


class TrackInsightTool(BaseTool):
    """
    Tool for tracking discovered insights.
    
    This tool records insights found during the research process to build
    a comprehensive record of findings.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_insight"
        description = """
        Track an insight that was discovered during the research.
        Requires these fields:
        - category: The category of the insight (e.g., 'Environmental', 'Social', 'Governance')
        - behavior_type: The type of behavior (e.g., 'Positive', 'Negative', 'Neutral')
        - description: A detailed description of the insight
        - source_url: The URL where the insight was found
        - source_title: The title of the source
        - confidence: The confidence level (0.0 to 1.0)
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=TrackInsightSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _save_insight_to_db(self, insight: Dict[str, Any]):
        """
        Save an insight to the agent_insights table.
        
        Args:
            insight: The insight to save
        """
        try:
            category = insight.get("category", "unknown")
            behavior_type = insight.get("behavior_type", "unknown")
            description = insight.get("description", "")
            source_url = insight.get("source_url", "")
            source_title = insight.get("source_title", "")
            confidence = insight.get("confidence", 0.0)
            metadata = insight.get("metadata", {})
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        INSERT INTO agent_insights 
                        (session_id, company_name, category, behavior_type, description, 
                         source_url, source_title, confidence, metadata, extracted_at, created_at)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                        """,
                        (
                            self.session_id, 
                            self.company_name,
                            category,
                            behavior_type,
                            description,
                            source_url,
                            source_title,
                            confidence,
                            json.dumps(metadata),
                            datetime.now(),
                            datetime.now()
                        )
                    )
                    conn.commit()
        except Exception as e:
            logger.error(f"Error saving insight to database: {e}")
    
    def _track_insight(self, insight: Dict[str, Any]) -> bool:
        """
        Track an insight in memory and database.
        
        Args:
            insight: The insight to track
            
        Returns:
            bool: True if the insight was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Check if this insight already exists (by description)
        if "insights" not in memory:
            memory["insights"] = []
            
        existing_descriptions = [i.get("description") for i in memory["insights"]]
        if insight.get("description") in existing_descriptions:
            return False
            
        # Add timestamp to insight
        insight["discovered_at"] = datetime.now().isoformat()
        
        # Add to memory
        memory["insights"].append(insight)
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        # Also save to agent_insights table
        self._save_insight_to_db(insight)
        
        logger.debug(f"Tracked insight: {insight.get('description', '')[:50]}...")
        return True
    
    def _run(
        self,
        category: str,
        behavior_type: str,
        description: str,
        source_url: str,
        source_title: str,
        confidence: float = 0.8
    ) -> str:
        """
        Run the tool to track a discovered insight.
        
        Args:
            category: The category of the insight
            behavior_type: The type of behavior
            description: A detailed description of the insight
            source_url: The URL where the insight was found
            source_title: The title of the source
            confidence: The confidence level (0.0 to 1.0)
            
        Returns:
            str: Confirmation message
        """
        try:
            
            # If only one parameter is provided and it's a string, it might be a JSON string
            if category and not any([behavior_type, description, source_url, source_title]):
                try:
                    # Try to parse it as JSON
                    if isinstance(category, str) and (category.startswith("{") or category.startswith("[")):
                        parsed_input = json.loads(category)
                        
                        # Handle array of objects
                        if isinstance(parsed_input, list) and len(parsed_input) > 0:
                            results = []
                            for item in parsed_input:
                                if isinstance(item, dict):
                                    success = self._track_insight(item)
                                    results.append(f"{'Successfully tracked' if success else 'Already tracked'} insight: {item.get('description', '')[:50]}...")
                            if results:
                                return "\n".join(results)
                                
                        # Handle single object
                        if isinstance(parsed_input, dict):
                            success = self._track_insight(parsed_input)
                            return f"{'Successfully tracked' if success else 'Already tracked'} insight: {parsed_input.get('description', '')[:50]}..."
                except Exception as e:
                    # Not valid JSON, continue with normal processing
                    logger.warning(f"Error parsing JSON input: {e}")
            
            # Ensure we have all required parameters
            required_fields = {
                "category": category,
                "behavior_type": behavior_type,
                "description": description,
                "source_url": source_url,
                "source_title": source_title
            }
            
            missing_fields = [field for field, value in required_fields.items() if not value]
            if missing_fields:
                return f"Error: Missing required fields: {', '.join(missing_fields)}"
            
            # Default confidence to 0.8 if not provided
            if confidence is None:
                confidence = 0.8
            else:
                try:
                    confidence = float(confidence)
                except ValueError:
                    return f"Error: confidence must be a number between 0.0 and 1.0, got {confidence}"
            
            # Track the insight
            insight = {
                "category": category,
                "behavior_type": behavior_type,
                "description": description,
                "source_url": source_url,
                "source_title": source_title,
                "confidence": confidence,
                "metadata": {}
            }
            
            success = self._track_insight(insight)
            return f"{'Successfully tracked' if success else 'Already tracked'} insight about {behavior_type} {category} behavior from {source_title}"
        except Exception as e:
            logger.exception(f"Error tracking insight: {e}")
            return f"Error tracking insight: {str(e)}"
    
    async def _arun(
        self,
        category: str,
        behavior_type: str,
        description: str,
        source_url: str,
        source_title: str,
        confidence: float = 0.8
    ) -> str:
        """Async implementation of _run."""
        return self._run(
            category=category,
            behavior_type=behavior_type,
            description=description,
            source_url=source_url,
            source_title=source_title,
            confidence=confidence
        )
