"""Tool for tracking search queries."""

import json
from datetime import datetime
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel, Field

from eko.db import get_bo_conn


class SearchQuerySchema(BaseModel):
    """Input schema for the track search query tool."""
    query: str = Field(description="The search query that was used")


class TrackSearchQueryTool(BaseTool):
    """
    Tool for tracking search queries.
    
    This tool records search queries used during the research process
    to avoid duplicate searches and to build a comprehensive record of the crawl.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "track_search_query"
        description = """
        Track a search query that was used during research.
        Call this tool whenever you perform a search to help track research progress.
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=SearchQuerySchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _save_memory(self, memory: Dict[str, Any]):
        """
        Save the memory to the file system.
        
        Args:
            memory: The memory to save
        """
        with open(self.memory_file, "w") as f:
            json.dump(memory, f, indent=2)
    
    def _sync_memory_to_db(self, memory: Dict[str, Any]):
        """
        Sync memory from file system to database.
        
        Args:
            memory: The memory to sync
        """
        try:
            memory["updated_at"] = datetime.now().isoformat()
            
            with get_bo_conn() as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """
                        UPDATE agent_sessions 
                        SET memory_data = %s, updated_at = %s
                        WHERE session_id = %s
                        """,
                        (json.dumps(memory), datetime.now(), self.session_id)
                    )
                    conn.commit()
                    logger.debug(f"Synced memory to database for session {self.session_id}")
        except Exception as e:
            logger.error(f"Error syncing memory to database: {e}")
    
    def _track_search_query(self, query: str) -> bool:
        """
        Track a search query in memory and database.
        
        Args:
            query: The search query to track
            
        Returns:
            bool: True if the query was newly tracked, False if it was already tracked
        """
        memory = self._load_memory()
        
        # Initialize last_position if not present
        if "last_position" not in memory:
            memory["last_position"] = {}
            
        # Initialize search_queries if not present
        if "search_queries" not in memory["last_position"]:
            memory["last_position"]["search_queries"] = []
            
        # Check if query is already tracked
        if query in memory["last_position"]["search_queries"]:
            return False
            
        # Add query to search_queries
        memory["last_position"]["search_queries"].append(query)
        memory["last_position"]["search_query_index"] = len(memory["last_position"]["search_queries"]) - 1
        
        # Save updates
        self._save_memory(memory)
        self._sync_memory_to_db(memory)
        
        logger.debug(f"Tracked search query: {query}")
        return True
    
    def _run(self, query: str) -> str:
        """
        Run the tool to track a search query.
        
        Args:
            query: The search query that was used
            
        Returns:
            str: Confirmation message
        """
        try:
                
            # Handle possible dict input
            if isinstance(query, dict) and "query" in query:
                query = query["query"]
                
            # Handle array input
            if isinstance(query, list):
                results = []
                for single_query in query:
                    if isinstance(single_query, dict) and "query" in single_query:
                        single_query = single_query["query"]
                    success = self._track_search_query(str(single_query).strip())
                    results.append(f"{'Successfully tracked' if success else 'Already tracked'} search query: {single_query}")
                return "\n".join(results)
                
            # Ensure the query is a string
            query = str(query).strip()
            
            # Track the search query
            success = self._track_search_query(query)
            return f"{'Successfully tracked' if success else 'Already tracked'} search query: {query}"
        except Exception as e:
            logger.exception(f"Error tracking search query: {e}")
            return f"Error tracking search query: {str(e)}"
    
    async def _arun(self, query: str) -> str:
        """Async implementation of _run."""
        return self._run(query)
