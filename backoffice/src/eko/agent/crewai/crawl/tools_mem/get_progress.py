"""Tool for getting crawl progress summary."""

import json
from typing import Dict, Any

from crewai.tools import BaseTool
from loguru import logger
from pydantic import BaseModel


class GetProgressSchema(BaseModel):
    """Empty schema for the get progress tool as it takes no parameters."""
    pass


class GetProgressTool(BaseTool):
    """
    Tool for getting crawl progress summary.
    
    This tool provides a summary of the current crawl progress,
    including visited URLs, insights, and task completion status.
    """
    
    company_name: str = None  # Added as a field
    session_id: str = None    # Added as a field
    memory_file: str = None   # Added as a field
    
    def __init__(self, company_name: str, session_id: str, memory_file: str):
        """
        Initialize the tool with company and session information.
        
        Args:
            company_name: The name of the target company
            session_id: The session ID for persistence
            memory_file: Path to the memory file for persistence
        """
        # Define name and description directly here
        name = "get_crawl_progress"
        description = """
        Get a summary of the current crawl progress.
        This tool returns information about visited URLs, downloaded files, 
        discovered insights, and tasks completed so far.
        """
        
        # Initialize the BaseTool with the name and description
        super().__init__(
            name=name, 
            description=description,
            args_schema=GetProgressSchema
        )
        
        # Store the company, session, and memory file information
        self.company_name = company_name
        self.session_id = session_id
        self.memory_file = memory_file
    
    def _load_memory(self) -> Dict[str, Any]:
        """
        Load the memory from the file system.
        
        Returns:
            Dict[str, Any]: The loaded memory
        """
        try:
            with open(self.memory_file, "r") as f:
                return json.load(f)
        except (FileNotFoundError, json.JSONDecodeError) as e:
            logger.error(f"Error loading memory file: {e}")
            return {}
    
    def _get_crawl_progress_summary(self) -> str:
        """
        Get a summary of the crawl progress.
        
        Returns:
            str: A summary of the crawl progress
        """
        memory = self._load_memory()
        
        visited_urls_count = len(memory.get("visited_urls", []))
        downloaded_files_count = len(memory.get("downloaded_files", []))
        insights_count = len(memory.get("insights", []))
        tasks_completed_count = len(memory.get("tasks_completed", []))
        
        # Last positions
        current_task_index = memory.get("last_position", {}).get("task_index", 0)
        current_url_index = memory.get("last_position", {}).get("url_index", 0)
        search_queries = memory.get("last_position", {}).get("search_queries", [])
        
        # Recent URLs (last 5)
        recent_urls = memory.get("visited_urls", [])[-5:] if visited_urls_count > 0 else []
        
        # Top categories of insights
        categories = {}
        for insight in memory.get("insights", []):
            category = insight.get("category", "unknown")
            categories[category] = categories.get(category, 0) + 1
        
        top_categories = sorted(categories.items(), key=lambda x: x[1], reverse=True)[:3]
        
        # Format the summary
        summary = f"Crawl Progress for {self.company_name} (Session: {self.session_id})\n\n"
        summary += f"Progress Summary:\n"
        summary += f"- Visited URLs: {visited_urls_count}\n"
        summary += f"- Downloaded files: {downloaded_files_count}\n"
        summary += f"- Insights discovered: {insights_count}\n"
        summary += f"- Tasks completed: {tasks_completed_count}\n\n"
        
        summary += f"Current Position:\n"
        summary += f"- Current task index: {current_task_index}\n"
        summary += f"- Current URL index: {current_url_index}\n"
        
        if recent_urls:
            summary += f"\nRecent URLs visited:\n"
            for i, url in enumerate(recent_urls):
                summary += f"- {url}\n"
        
        if search_queries:
            summary += f"\nSearch queries used:\n"
            for i, query in enumerate(search_queries[-5:]):  # Show last 5 queries
                summary += f"- {query}\n"
        
        if top_categories:
            summary += f"\nTop insight categories:\n"
            for category, count in top_categories:
                summary += f"- {category}: {count} insights\n"
        
        return summary
    
    def _run(self) -> str:
        """
        Run the tool to get crawl progress summary.
        
        Returns:
            str: Crawl progress summary
        """
        try:
            return self._get_crawl_progress_summary()
        except Exception as e:
            logger.exception(f"Error getting crawl progress: {e}")
            return f"Error getting crawl progress: {str(e)}"
    
    async def _arun(self) -> str:
        """Async implementation of _run."""
        return self._run()