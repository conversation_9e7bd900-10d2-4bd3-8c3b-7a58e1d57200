#!/usr/bin/env python
"""
LLM Comparator <PERSON>t

This script takes entries from a JSONL file containing instruction-response pairs,
sends the first 10 entries to multiple LLMs using call_llms_str, and generates
an HTML comparison report.
"""

import datetime
import json
import webbrowser
from typing import List, Dict, Any

from loguru import logger

from eko.llm import LLMModel
from eko.llm.main import call_llms_str


def load_jsonl_data(file_path: str, limit: int = 10) -> List[Dict[str, Any]]:
    """Load data from a JSONL file with a limit on entries."""
    data = []
    with open(file_path, 'r') as f:
        for i, line in enumerate(f):
            if i >= limit:
                break
            data.append(json.loads(line))
    return data


def extract_instruction(entry: Dict[str, Any]) -> str:
    """Extract the instruction from an entry."""
    return entry.get('instruction', '')


def extract_expected_response(entry: Dict[str, Any]) -> str:
    """Extract the expected response from an entry."""
    return entry.get('response', '')


def process_instruction(instruction: str, models: List[LLMModel]) -> Dict[str, str]:
    """Process an instruction with multiple LLM models."""
    results = {}

    for model in models:
        try:
            # Call the model with the instruction
            from eko.llm.main import LLMOptions
            options = LLMOptions(temperature=0.0, no_cache=True)
            response = call_llms_str(
                llms=[model],
                messages=[{"role": "user", "content": instruction}],
                max_tokens=8000,
                options=options
            )
            results[model.name] = response
        except Exception as e:
            logger.exception(e)
            results[model.name] = f"Error: {str(e)}"

    return results


def process_all_instructions(data: List[Dict[str, Any]], models: List[LLMModel]) -> List[Dict[str, Any]]:
    """Process all instructions with multiple LLM models."""
    results = []

    for i, entry in enumerate(data):
        print(f"Processing entry {i+1}/{len(data)}...")
        instruction = extract_instruction(entry)
        expected_response = extract_expected_response(entry)

        model_responses = process_instruction(instruction, models)

        results.append({
            "entry_number": i + 1,
            "instruction": instruction,
            "expected_response": expected_response,
            "model_responses": model_responses
        })

    return results


def generate_html_report(results: List[Dict[str, Any]], models: List[LLMModel]) -> str:
    """Generate an HTML report from the results."""
    model_names = [model.name for model in models]

    html = f"""
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>LLM Comparison Report</title>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
                margin: 0;
                padding: 20px;
                color: #333;
            }}
            h1 {{
                color: #2c3e50;
                text-align: center;
                margin-bottom: 30px;
            }}
            h2 {{
                color: #3498db;
                border-bottom: 1px solid #eee;
                padding-bottom: 10px;
                margin-top: 30px;
            }}
            .entry {{
                margin-bottom: 40px;
                border: 1px solid #ddd;
                border-radius: 5px;
                padding: 20px;
                background-color: #f9f9f9;
            }}
            .instruction {{
                background-color: #ecf0f1;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                white-space: pre-wrap;
            }}
            .expected-response {{
                background-color: #e8f5e9;
                padding: 15px;
                border-radius: 5px;
                margin-bottom: 20px;
                white-space: pre-wrap;
            }}
            .model-response {{
                margin-bottom: 20px;
            }}
            .model-name {{
                font-weight: bold;
                color: #2980b9;
                margin-bottom: 5px;
            }}
            .response-content {{
                background-color: #fff;
                padding: 15px;
                border-radius: 5px;
                border: 1px solid #ddd;
                white-space: pre-wrap;
            }}
            .highlight {{
                background-color: #fffacd;
            }}
            .stats {{
                margin-top: 30px;
                background-color: #e8f4f8;
                padding: 15px;
                border-radius: 5px;
            }}
            /* Add tabbed interface */
            .tabs {{
                display: flex;
                margin-bottom: 15px;
            }}
            .tab {{
                padding: 10px 15px;
                cursor: pointer;
                background-color: #eee;
                border: 1px solid #ddd;
                border-radius: 5px 5px 0 0;
                margin-right: 5px;
            }}
            .tab.active {{
                background-color: #fff;
                border-bottom: 1px solid #fff;
            }}
            .tab-content {{
                display: none;
            }}
            .tab-content.active {{
                display: block;
            }}
        </style>
        <script>
            document.addEventListener('DOMContentLoaded', function() {{
                document.querySelectorAll('.tab').forEach(tab => {{
                    tab.addEventListener('click', function() {{
                        const targetId = this.getAttribute('data-target');

                        // Hide all tab contents
                        document.querySelectorAll('.tab-content').forEach(content => {{
                            content.classList.remove('active');
                        }});

                        // Deactivate all tabs
                        document.querySelectorAll('.tab').forEach(t => {{
                            t.classList.remove('active');
                        }});

                        // Activate clicked tab and content
                        this.classList.add('active');
                        document.getElementById(targetId).classList.add('active');
                    }});
                }});

                // Activate first tab by default
                document.querySelector('.tab').click();
            }});
        </script>
    </head>
    <body>
        <h1>LLM Comparison Report</h1>
        <div class="stats">
            <h2>Summary</h2>
            <p>Generated on: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>
            <p>Models compared: {', '.join(model_names)}</p>
            <p>Number of entries: {len(results)}</p>
        </div>
    """

    for result in results:
        entry_number = result["entry_number"]
        instruction = result["instruction"]
        expected_response = result["expected_response"]
        model_responses = result["model_responses"]

        html += f"""
        <div class="entry">
            <h2>Entry #{entry_number}</h2>
            <h3>Instruction:</h3>
            <div class="instruction">{instruction}</div>

            <h3>Expected Response:</h3>
            <div class="expected-response">{expected_response}</div>

            <h3>Model Responses:</h3>
            <div class="tabs">
        """

        # Create tabs for each model
        for i, (model_name, _) in enumerate(model_responses.items()):
            is_active = "active" if i == 0 else ""
            html += f'<div class="tab {is_active}" data-target="tab-{entry_number}-{i}">{model_name}</div>'

        html += "</div>"

        # Create tab content for each model
        for i, (model_name, response) in enumerate(model_responses.items()):
            is_active = "active" if i == 0 else ""
            html += f"""
            <div id="tab-{entry_number}-{i}" class="tab-content {is_active}">
                <div class="model-response">
                    <div class="response-content">{response}</div>
                </div>
            </div>
            """

        html += "</div>"

    html += """
    </body>
    </html>
    """

    return html


def save_html_report(html: str, output_path: str) -> None:
    """Save the HTML report to a file."""
    with open(output_path, 'w') as f:
        f.write(html)


def main():
    # Define the input and output paths
    input_file = "/Users/<USER>/IdeaProjects/mono-repo/tmp/demise_and_metadata_training.jsonl"
    output_file = "/tmp/llm_comparison_report.html"

    # Define the models to use
    models = [
        LLMModel.GEMINI_FLASH_LITE_FINETUNED
    ]

    # Load the data
    print(f"Loading data from {input_file}...")
    data = load_jsonl_data(input_file)
    print(f"Loaded {len(data)} entries.")

    # Process the data
    print("Processing instructions with multiple LLMs...")
    results = process_all_instructions(data, models)

    # Generate the HTML report
    print("Generating HTML report...")
    html = generate_html_report(results, models)

    # Save the HTML report
    print(f"Saving HTML report to {output_file}...")
    save_html_report(html, output_file)

    # Open the HTML report in the default browser
    print("Opening HTML report in browser...")
    webbrowser.open(f"file://{output_file}")

    print("Done!")


if __name__ == "__main__":
    main()
