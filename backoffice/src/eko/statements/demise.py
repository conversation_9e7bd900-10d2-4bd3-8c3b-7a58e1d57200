import json
from loguru import logger
from typing import Optional

from eko.llm import LLMModel, PROMPT_JSON, PROMPT_NOT_CONVERSATIONAL
from eko.llm.distil import distil
from eko.llm.main import call_llm_boolean, call_llms_typed
from eko.llm.prompts import prompt
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.demise.domain import DomainModel
from eko.models.vector.demise.engagement import EngagementModel
from eko.models.vector.demise.entity import EntityModel
from eko.models.vector.demise.impact import ImpactModel
from eko.models.vector.demise.motivation import MotivationModel
from eko.models.vector.demise.statement import StatementTypeModel

alpaca_prompt = """
<s>[SYSTEM_PROMPT]You are not conversational you are an API, you just return JSON.[/SYSTEM_PROMPT][INST]Create a DEMISE model from the statement within the <statement> tags, using the details in
 <context> to provide context only. The result should be a JSON map of features and values which form a DEMISE model (values can only be between -1.0 and 1.0):

{}[/INST]{}</s>
"""


# ["<statement>Failure to implement our new biodegradable data storage protocols will result in the release of harmful microplastics into marine ecosystems, destroying billions of phytoplankton colonies and irreversibly damaging the ocean's carbon sequestration capabilities and global climate regulation systems.</statement><context><title>Digital Sustainability Initiative: Transforming Data Storage for Planetary Health</title><extract>This report examines the environmental impact of conventional digital storage technologies and proposes revolutionary biodegradable alternatives that minimize ecological harm while maintaining data integrity.</extract><text>Page 47\n\n4.3 Ecological Implications of Next-Generation Storage Technologies\n\nThe transition to biodegradable digital storage media represents a critical inflection point in sustainable computing. Our research demonstrates that conventional solid-state drives and magnetic storage devices contribute approximately 2.7 million tons of non-degradable waste annually, with microplastic leachate affecting marine ecosystems at unprecedented levels.\n\nThe BioDrive\u2122 technology developed through our consortium utilizes cellulose-based substrates and alginate polymers that maintain data integrity while decomposing harmlessly when decommissioned. Laboratory tests confirm these materials support the same microbial communities found in healthy soil ecosystems rather than releasing toxins.\n\nImplementation Timeline and Compliance Requirements:\n\nAll member organizations must begin phased adoption by Q3 2024, with complete transition required by 2026. Failure to implement our new biodegradable data storage protocols will result in the release of harmful microplastics into marine ecosystems, destroying billions of phytoplankton colonies and irreversibly damaging the ocean's carbon sequestration capabilities and global climate regulation systems.\n\nThe economic analysis in Section 5 demonstrates that while initial implementation costs are 12% higher than conventional storage solutions, the three-year total cost of ownership is actually 8% lower due to reduced cooling requirements and extended service life. Additionally, carbon credit allocations available through the International Digital Sustainability Alliance offset approximately 30% of transition expenses.\n\nFigure 4.3.2 illustrates the projected recovery of affected phytoplankton populations following industry-wide adoption, with corresponding improvements to oceanic carbon capture rates.</text></context><statement>Failure to implement our new biodegradable data storage protocols will result in the release of harmful microplastics into marine ecosystems, destroying billions of phytoplankton colonies and irreversibly damaging the ocean's carbon sequestration capabilities and global climate regulation systems.</statement>", {"domain.environment.biodiversity": 0.9, "domain.environment.climate": 0.8, "domain.environment.climate_change_c_o2": 0.8, "domain.environment.conservation": 0.7, "domain.environment.general": 0.9, "domain.environment.sustainability": 0.9, "domain.environmental_solutions.general": 0.8, "domain.industry.technology": 0.9, "domain.industry.waste_management": 0.7, "domain.pollution.general": 0.7, "domain.pollution.water": 0.9, "domain.realms.sea": 0.9, "ethics.awareness": 0.7, "ethics.biodiversity": 0.8, "ethics.caution": 0.7, "ethics.conservation": 0.9, "ethics.intergenerational_justice": 0.8, "ethics.morality": 0.6, "ethics.responsibility": 0.8, "ethics.sustainability": 0.9, "impact.duration": 0.8, "impact.on_animal_life": -0.8, "impact.on_environment": -0.9, "impact.on_human_life": -0.7, "impact.proportion": 0.8, "motivation.emotional.aversion": 0.7, "motivation.emotional.fear": 0.8, "motivation.emotional.wisdom": 0.6, "motivation.pressure.existential": 0.7, "motivation.pressure.external": 0.8, "motivation.pressure.situational": 0.6, "motivation.pressure.systemic": 0.9, "object.entity_type.biological_systems": 1.0, "object.qualities.quantity": 1.0, "statement.describes_event": 0.3, "statement.describes_fact": 0.2, "statement.future.harm.description": 0.9, "subject.entity_type.immaterial": 0.7, "subject.entity_type.software_system": 1.0}]

def create_prompt(context, extract, statement, title):
    _prompt = [
        {"role": "system", "content": PROMPT_JSON},
        {"role": "user", "content": f"""
Create a DEMISE model from the statement within the <statement> tags, using the details in
 <context> to provide context only. The result should be a JSON map of features and values which form a DEMISE model (values can only be between -1.0 and 1.0):

<statement>{statement}</statement><context><title>{title}</title><extract>{extract}</extract><text>{context}</text></context><statement>{statement}</statement>"""}]
    return _prompt


def create_llm_prompt(context, extract, statement, title, prompt_desc):
    _prompt = [
        {"role": "user", "content": f"""{PROMPT_NOT_CONVERSATIONAL}

The following context and statement came from a report titled <title>{title}</title> and whose extract is <extract>{extract}</extract>
the adjacent text as context is:

<context>
{context}
</context>

<instructions>
For the supplied statement given the context, please return a model.

{prompt_desc}.

You are being asked to analyse the statement, *not the context*. The context is there so you can better understand the motivations and impact of the statement.

If you would set a value to 0.0 just skip it, because 0.0 is the default value.
If a field is not applicable to the statement skip it, don't set it to 0.0.

Please use the thoughts__ fields to think through your answer before giving the numbers, if you don't need to use it just skip it, don't include an empty string value.
</instructions>

In the JSON response please skip any fields that you would set to a value of 0.0, if a field is not applicable to the statement it will default to 0.0, so just include it in the JSON, don't set it to 0.0.

The statement to analyse is:\n\n <statement>{statement}</statement>"""}]
    return _prompt


demise_models = [("statement", StatementTypeModel,
                  f"The model is the type of statement made, including whether it is a statement of fact or relating to an action/event. "),
                 ("subject", EntityModel,
                  "The model is the subject of the statement, this is the entity about which a statement is made or if about an action then the entity that caused the action. Zeroed if not applicable."),
                 ("object", EntityModel,
                  "The model is the object of the statement, this is the entity which has an action done to it. It is the entity impacted by any action or event described in the statement. Zeroed if not applicable."),
                 ("impact", ImpactModel,
                  "The model is the impact of the action/event if an action, otherwise the impact of the statement."),
                 ("motivation", MotivationModel,
                  "fThe model is the motivation for the action if an action, otherwise the motivation for the statement. "),
                 ("engagement", EngagementModel,
                  "The model represents the engagement patterns shown by the entity in relation to the issues at hand."),
                 ("domain", DomainModel,
                  "The model is the domain to which the action/event/facts/entities relate")]


def get_demise_new(statement: str, context: str, title: str, extract: str, version: int = 1):
    demise = DEMISEModel.model_construct()  # Using model_construct instead of deprecated construct
    for attempt in range(3):
        for key, clazz, prompt_desc in demise_models:
            from eko.llm.main import LLMOptions
            options = LLMOptions(
                cache_key="demise:" + key + ":v45:" + context + statement + (
                    (":" + str(attempt)) if attempt > 0 else ""),
                escalate_to=[LLMModel.NORMAL_HQ, LLMModel.NORMAL_HQ_ALT]
            )
            model = call_llms_typed([LLMModel.NORMAL_NEXT, LLMModel.NORMAL_CHEAP],
                                    create_llm_prompt(context, extract, statement, title, prompt_desc),
                                    8000,
                                    response_model=clazz,
                                    options=options)
            setattr(demise, key, model)
        DEMISEModel.model_validate(demise)
        if validate_demise_model(demise, statement):
            distil("demise_model",create_prompt(context, extract, statement, title)[1]['content'], json.dumps(demise.to_kv_sparse()))
            return demise
    logger.info(f"\n{statement}:\n\n{json.dumps(demise.to_kv_sparse(), indent=2)}")
    raise ValueError("Failed to validate DEMISE model for statement"+statement)

def get_demise_full(statement: str, context: str, title: str, extract: str, cheap=False, version: int = 1):
    if cheap:
        from eko.llm.main import LLMOptions
        options = LLMOptions(escalate_to=[LLMModel.NORMAL_HQ, LLMModel.NORMAL_HQ_ALT])
        return  call_llms_typed([LLMModel.NORMAL_NEXT, LLMModel.NORMAL_ALL_ROUNDER],
                                create_llm_prompt(context, extract, statement, title, "Please return the full DEMISE model."),
                                8000, response_model=DEMISEModel, options=options)
    else:
        return get_demise_new(statement, context, title, extract, cheap)



def validate_demise(demise_str: str, statement: str) -> bool:
    demise = DEMISEModel.from_sparse_kv(json.loads(demise_str))
    return validate_demise_model(demise, statement)


def validate_demise_model(demise, statement):
    impact_value = demise.get_impact_value()
    if demise.statement.is_action():
        if not call_llm_boolean([LLMModel.GEMINI_FLASH_LITE],
                                prompt(PROMPT_NOT_CONVERSATIONAL,
                                       f"Does this describe an action with a {'negative/harmful' if impact_value < 0 else 'positive/beneficial'} impact: '{statement}' answer yes or no? ")):
            logger.error(f"LLM did not confirm impact {impact_value} for action statement: {statement}")
            return False
    return True


if __name__ == "__main__":
    model = DEMISEModel.model_construct()
    # Test with a valid field instead of a non-existent 'dummy' field
    model.domain.industry.general = 1.0
    print(model.domain.to_vector())
