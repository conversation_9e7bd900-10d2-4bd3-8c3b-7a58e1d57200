from concurrent.futures import as_completed, ThreadPoolExecutor
from typing import cast

from loguru import logger
from psycopg.rows import dict_row

from eko.db import get_bo_conn
from eko.models import Entity
from eko.statements.extract import extract_statements_from_doc


def extract_statements_for_training_command(entity: Entity, max_workers: int = 1):

    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("SELECT text_searchable_name, name, legal_name, common_name FROM kg_base_entities WHERE id = %s OR canonical_id = %s",
                           (entity.id, entity.id))
            rows = cur.fetchall()
            # Use a set to avoid duplicates, and filter out any None values
            synonyms = sorted(list({name for row in rows for name in row if name}))

        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute("SELECT * FROM kg_documents WHERE raw_text ilike ANY(%s) LIMIT 1", ([f"%{synonym}%" for synonym in synonyms],))
            rows = cur.fetchall()

            futures=[]
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                for row in rows:
                    futures.append(executor.submit(extract_statements_from_doc,  cast(int,row['id']),None))

                # Wait for all futures in this batch to complete before starting the next batch
                for future in as_completed(futures):
                    try:
                        future.result()
                    except Exception as e:
                        logger.error(f"Error extracting statements: {e}")
                        # Continue processing other documents


