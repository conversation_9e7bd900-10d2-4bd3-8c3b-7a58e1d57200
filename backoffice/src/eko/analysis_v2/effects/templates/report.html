{% extends "base.html" %}

{% block title %}Effect Analysis for {{ data.entity }}{% endblock %}

{% block heading %}Effect Analysis Report{% endblock %}

{% block content %}
<div class="row mb-4">
    <div class="col-md-6">
        {% include "components/analysis_details.html" %}
    </div>
    <div class="col-md-6">
        {% include "components/analysis_approach.html" %}
    </div>
</div>

<!-- Enable Bootstrap tooltips -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl)
        });
    });
</script>

<ul class="nav nav-tabs" id="analysisTabs" role="tablist">
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="positive-flags-final-tab" data-bs-toggle="tab" data-bs-target="#green-merged-flags" type="button" role="tab" aria-controls="positive-flags-final" aria-selected="false">
            Final Green Flags <span class="badge bg-success">{{ data.process_flow.green.merged_flags|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="negative-flags-final-tab" data-bs-toggle="tab" data-bs-target="#red-merged-flags" type="button" role="tab" aria-controls="negative-flags-final" aria-selected="false">
            Final Red Flags <span class="badge bg-danger">{{ data.process_flow.red.merged_flags|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="positive-flags-tab" data-bs-toggle="tab" data-bs-target="#green-flags" type="button" role="tab" aria-controls="positive-flags-final" aria-selected="false">
            Green Flags <span class="badge bg-success">{{ data.flags.green|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="negative-flags-tab" data-bs-toggle="tab" data-bs-target="#red-flags" type="button" role="tab" aria-controls="negative-flags-final" aria-selected="false">
            Red Flags <span class="badge bg-danger">{{ data.flags.red|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link active" id="visualization-tab" data-bs-toggle="tab" data-bs-target="#visualization" type="button" role="tab" aria-controls="visualization" aria-selected="true">
            Visualizations
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="clusters-tab" data-bs-toggle="tab" data-bs-target="#clusters" type="button" role="tab" aria-controls="clusters" aria-selected="false">
            All Clusters <span class="badge bg-secondary">{{ data.effects.green|length + data.effects.red|length }}</span>
        </button>
    </li>
    <li class="nav-item" role="presentation">
        <button class="nav-link" id="process-flow-tab" data-bs-toggle="tab" data-bs-target="#process-flow" type="button" role="tab" aria-controls="process-flow" aria-selected="false">
            Process Flow <span class="badge bg-info">Visualize</span>
        </button>
    </li>
</ul>

<div class="tab-content" id="analysisTabsContent">

    <!-- Visualizations Tab -->
    {% include "tabs/visualizations_tab.html" %}
    
    <!-- Green Flags Tab -->
    {% set flags=data.process_flow.green.merged_flags %}
    {% set flags_tab_id="green-merged-flags" %}
    {% include "tabs/green_flags_tab.html" %}
    
    <!-- Red Flags Tab -->
    {% set flags=data.process_flow.red.merged_flags %}
    {% set flags_tab_id="red-merged-flags" %}
    {% include "tabs/red_flags_tab.html" %}

    <!-- Green Flags Tab -->
    {% set flags=data.process_flow.green.initial_flags %}
    {% set flags_tab_id="green-flags" %}
    {% include "tabs/green_flags_tab.html" %}

    <!-- Red Flags Tab -->
    {% set flags=data.process_flow.red.initial_flags %}
    {% set flags_tab_id="red-flags" %}
    {% include "tabs/red_flags_tab.html" %}
    <!-- Clusters Tab -->
    {% include "tabs/clusters_tab.html" %}
    
    <!-- Process Flow Tab -->
    {% include "tabs/process_flow_tab.html" %}
</div>
{% endblock %}