<div class="tab-pane fade" id="{{ flags_tab_id }}" role="tabpanel" aria-labelledby="green-flags-tab">
    <h2 class="mb-3">Positive Effect Flags</h2>
    <div class="row">
        {% for flag in flags %}
        <div class="col-md-6 mb-4">
            <div class="card green-flag h-100">
                <div class="card-header d-flex justify-content-between">
                    <div>
                        <i class="bi-arrow-up-circle-fill text-success me-2"></i>
                        <strong>Positive Effect</strong>
                    </div>
                    <div>
                        <span class="badge bg-primary score-badge" title="Impact Score">Impact: {{ flag.impact }}</span>
                        <span class="badge bg-secondary score-badge" title="Authenticity Score">Auth: {{ flag.authentic }}</span>
                        <span class="badge bg-info score-badge" title="Contribution Score">Contrib: {{ flag.contribution }}</span>
                        <span class="badge bg-warning score-badge" title="Confidence Score">Conf: {{ flag.confidence }}</span>
                    </div>
                </div>
                <div class="card-body mb-2">
                    <h5 class="card-title">{{ flag.title }}</h5>
                    <p class="card-text">{{ flag.description }}</p>
                    <div class="mb-3">
                        <strong>Reason:</strong>
                        <p>{{ flag.reason }}</p>
                    </div>
                                        {%  set centroid=flag.full_demise_centroid %}
                                        {% include "centroid_details.html" %}

                    </div>
            </div>
        </div>
        {% endfor %}
    </div>
</div>