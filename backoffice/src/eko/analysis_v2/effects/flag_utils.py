"""
Utility functions for effect flags.

This module provides utility functions for processing effect flags.
"""

from typing import Dict, Any, List, Callable

from loguru import logger

from eko.analysis_v2.effects.effect_flags import create_effect_flags_from_effects
from eko.analysis_v2.effects.effect_models import create_effects
from eko.analysis_v2.pipeline_tracker_extended import TraceabilityTracker
from eko.db import get_bo_conn
from eko.db.data.run import RunData
from eko.models.vector.derived.effect import EffectModel, EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.virtual_entity import VirtualEntityExpandedModel


def create_effects_for_both_types(
    conn,
    run_id: int,
    virtual_entity: VirtualEntityExpandedModel,
    start_year: int,
    end_year: int,
    tracker: TraceabilityTracker
) -> List[EffectModel]:
    """
    Create effects for both RED and GREEN types.

    Args:
        conn: Database connection
        run_id: Run ID
        virtual_entity: The expanded virtual entity model containing base entities
        start_year: Start year for analysis
        end_year: End year for analysis
        tracker: Traceability tracker instance

    Returns:
        List of effect IDs created
    """
    # Extract entity name from virtual entity
    name = virtual_entity.name
    # We now use the virtual entity directly instead of base entities
    try:
        # First, create effects for RED flags
        red_effects = create_effects(
            conn,
            run_id,
            virtual_entity,
            start_year,
            end_year,
            EffectType.RED,
            tracker
        )

        # Then create effects for GREEN flags
        green_effects = create_effects(
            conn,
            run_id,
            virtual_entity,
            start_year,
            end_year,
            EffectType.GREEN,
            tracker
        )

        # Combine both sets of effect IDs
        effects = red_effects + green_effects

        # Update run status
        RunData.mark_completed(conn, run_id)

        logger.info(f"Created {len(effects)} effect records in database")
        return effects
    except Exception as e:
        logger.error(f"Error creating effects: {e}")
        # Update run status to failed
        RunData.mark_failed(conn, run_id, str(e))
        raise


def create_flags_from_effects(
    effects: List[EffectModel],
    virtual_entity: VirtualEntityExpandedModel,
    run_id: int,
    max_workers: int = 4
) -> List[int]:
    """
    Create flags from effects using LLM.

    Args:
        effects: List of effect IDs
        virtual_entity: The expanded virtual entity model containing base entities
        run_id: Run ID
        max_workers: Maximum number of worker threads for parallel processing

    Returns:
        List of created flags
    """
        # Get a fresh connection for creating flags to avoid potential deadlocks
    with get_bo_conn() as flag_conn:
        flags = create_effect_flags_from_effects(
            flag_conn,
            effects,
            run_id,
            virtual_entity,
            max_workers
        )

        logger.info(f"Created {len(flags)} effect flags in database")
        return flags

def initialize_analysis_data(
    name: str,
    start_year: int,
    end_year: int,
    run_id: int
) -> Dict[str, Any]:
    """
    Initialize the analysis data structure.

    Args:
        name: Entity name
        start_year: Start year for analysis
        end_year: End year for analysis
        run_id: Run ID

    Returns:
        Initialized analysis data structure
    """
    return {
        "entity": name,
        "start_year": start_year,
        "end_year": end_year,
        "run_id": run_id,
        "effects": {
            "red": [],
            "green": []
        },
        "flags": {
            "red": [],
            "green": []
        },
        "process_flow": {
            "red": {
                "initial_flags": [],
                "merged_flags": []
            },
            "green": {
                "initial_flags": [],
                "merged_flags": []
            }
        },
        "metrics": {
            "red": {},
            "green": {},
            "overall": {}
        },
        "visualizations": {}
    }


def initialize_data_tracking() -> Dict[str, Any]:
    """
    Initialize the data tracking structure for visualizations.

    Returns:
        Initialized data tracking structure
    """
    return {
        "effect_vectors": {
            "red": [],
            "green": []
        },
        "statement_texts": {
            "red": [],
            "green": []
        },
        "statement_to_cluster": {
            "red": {},
            "green": {}
        }
    }


def create_callbacks(
    analysis_data: Dict[str, Any],
    data_tracking: Dict[str, Any]
) -> Dict[str, Callable]:
    """
    Create callback functions for effect and flag creation.

    Args:
        analysis_data: Analysis data structure
        data_tracking: Data tracking structure

    Returns:
        Dictionary of callback functions
    """
    def on_effect_created(effect_model, cluster_id, effect_type):
        effect_type_key = effect_type.value.lower()

        # Ensure full_demise_centroid exists and is not empty
        full_demise_centroid = effect_model.full_demise_centroid
        if not full_demise_centroid:
            # Log a warning if the centroid is missing
            logger.error(f"No DEMISE centroid for effect cluster {cluster_id} - generating synthetic placeholder")

        effect_data = {
            "id": len(analysis_data["effects"][effect_type_key]),
            "num_statements": len(effect_model.statements),
            "domains": effect_model.relevant_domains(),
            "statements": [s.statement_text for s in effect_model.statements],
            "cluster_id": cluster_id,
            "effect_type": effect_type_key,
            "effect_model_id": effect_model.id
        }

        analysis_data["effects"][effect_type_key].append(effect_data)
        logger.debug(f"Added {effect_type_key} effect cluster {cluster_id} with {len(effect_model.statements)} statements")

    def on_vector_created(statement, vector, effect_type):
        effect_type_key = effect_type.value.lower()

        # Store the vector for visualization
        data_tracking["effect_vectors"][effect_type_key].append(vector)

        # Store the statement text for reference
        data_tracking["statement_texts"][effect_type_key].append(statement.statement_text)

        logger.debug(f"Tracked {effect_type_key} vector for statement {statement.id}")

    def on_effect_stored(effect_model):
        # This is called after the effect model is stored in the database
        logger.debug(f"Effect model {effect_model.id} stored in database")

    def on_flag_created(flag_model: EffectFlagModel, is_stored):
        effect_type_key = flag_model.effect_type.value.lower()

        flag_data = {
            "id": flag_model.id,
            "title": flag_model.title,
            "summary": flag_model.summary,
            "reason": flag_model.reason,
            "impact": flag_model.impact,
            "authentic": flag_model.authentic,
            "contribution": flag_model.contribution,
            "confidence": flag_model.confidence,
            "start_year": flag_model.start_year,
            "end_year": flag_model.end_year,
            "effect_type": effect_type_key,
            "effect_ids": flag_model.effect_model_ids,
            "statement_ids": flag_model.statement_ids
        }

        # Add to the appropriate list based on whether it's stored or not
        if is_stored:
            analysis_data["process_flow"][effect_type_key]["merged_flags"].append(flag_data)
            logger.debug(f"Added stored {effect_type_key} flag {flag_model.id}: {flag_model.title}")
        else:
            analysis_data["flags"][effect_type_key].append(flag_data)
            analysis_data["process_flow"][effect_type_key]["initial_flags"].append(flag_data)
            logger.debug(f"Added {effect_type_key} flag: {flag_model.title}")

    return {
        "on_effect_created": on_effect_created,
        "on_vector_created": on_vector_created,
        "on_effect_stored": on_effect_stored,
        "on_flag_created": on_flag_created
    }
