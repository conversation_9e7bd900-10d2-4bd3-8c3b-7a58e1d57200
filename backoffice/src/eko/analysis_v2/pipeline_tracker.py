"""
Pipeline Tracking Library for Effect Analysis System

This module replaces the previous callback-based tracking with a dedicated tracking system
that records metrics about the document analysis pipeline in dedicated database tables.
These tables can then be queried by Met<PERSON>se for dashboards.
"""

import json
from typing import Dict, List, Optional, Any, Union

import numpy as np
from loguru import logger
from psycopg import Connection

from eko.models import Entity
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.derived.effect import EffectModel, EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.derived.enums import PipelineStage
from eko.models.virtual_entity import VirtualEntityModel
from eko.settings import settings


class PipelineTracker:
    """
    Main tracking class for the document analysis pipeline.

    This class provides methods to record metrics at each stage of the pipeline,
    storing them in dedicated tracking tables for analysis with Metabase.
    """

    def __init__(self, conn: Connection, run_id: int):
        """
        Initialize the pipeline tracker.

        Args:
            conn: Database connection
            run_id: ID of the current analysis run
        """
        self.conn = conn
        self.run_id = run_id


    def record_stat(self, entity: Optional[Union[VirtualEntityModel, List[VirtualEntityModel]]],
                    stage: PipelineStage,
                    object_id: Optional[int] = None,
                    count: int = 1,
                    effect_type: Optional[EffectType] = None,
                    processing_time_ms: int = 0,
                    metadata: Optional[Dict[str, Any]] = None):
        """
        Record a pipeline statistic.

        Args:
            entity: Entity or list of entities being analyzed
            stage: Pipeline stage
            object_id: ID of the object being processed (statement, effect, etc.)
            count: Number of objects processed
            effect_type: RED or GREEN for effects/flags
            processing_time_ms: Processing time in milliseconds
            metadata: Additional metadata to store
        """
        entity_id = None
        entity_name = None
        logger.info(f"{stage.name}/{effect_type.name if effect_type else '???'}: {processing_time_ms}ms" )

        if entity:
            if isinstance(entity, list):
                if len(entity) > 0:
                    entity_id = entity[0].id
                    entity_name = entity[0].name
            else:
                entity_id = entity.id
                entity_name = entity.name

        effect_type_str = effect_type.name.lower() if effect_type else None

        # Always log the stat regardless of persistence setting
        logger.debug(f"Pipeline stat: {stage.value} for run {self.run_id}, type: {effect_type_str}, time: {processing_time_ms}ms")

        # Only persist to database if the setting is enabled
        if settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                try:
                    cursor.execute(
                        """
                                   INSERT INTO dash.trk_pipeline_stats
                                   (run_id, entity_id, entity_name, stage, object_id, count, effect_type, processing_time_ms, metadata)
                                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s)
                                   """,
                        (
                            self.run_id,
                            entity_id,
                            entity_name,
                            stage.value,
                            object_id,
                            count,
                            effect_type_str,
                            processing_time_ms,
                            json.dumps(metadata) if metadata else None,
                        ),
                    )

                    # Update metrics table
                    cursor.execute(
                        """
                                   INSERT INTO dash.trk_pipeline_metrics
                                   (run_id, entity_id, entity_name, stage, count,
                                    red_count, green_count, total_processing_time_ms)
                                   VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                                   ON CONFLICT (run_id, entity_id, stage)
                                       DO UPDATE SET
                                                     count = dash.trk_pipeline_metrics.count + EXCLUDED.count,
                                                     red_count = CASE WHEN %s = 'red' THEN dash.trk_pipeline_metrics.red_count + %s ELSE dash.trk_pipeline_metrics.red_count END,
                                                     green_count = CASE WHEN %s = 'green' THEN dash.trk_pipeline_metrics.green_count + %s ELSE dash.trk_pipeline_metrics.green_count END,
                                                     total_processing_time_ms = dash.trk_pipeline_metrics.total_processing_time_ms + EXCLUDED.total_processing_time_ms
                                   """,
                        (
                            self.run_id,
                            entity_id,
                            entity_name,
                            stage.value,
                            count,
                            count if effect_type_str == "red" else 0,
                            count if effect_type_str == "green" else 0,
                            processing_time_ms,
                            effect_type_str,
                            count,
                            effect_type_str,
                            count,
                        ),
                    )

                except Exception as e:
                    logger.error(f"Error recording pipeline stat: {e}")
                    logger.exception(e)
            self.conn.commit()

    def record_error(self, entity: Optional[Union[VirtualEntityModel, List[VirtualEntityModel]]],
                     stage: PipelineStage,
                     error_message: str,
                     error_type: str,
                     object_id: Optional[int] = None):
        """
        Record a pipeline error.

        Args:
            entity: Entity or list of entities being analyzed
            stage: Pipeline stage where the error occurred
            error_message: Error message
            error_type: Type of error
            object_id: ID of the object being processed (statement, effect, etc.)
        """
        entity_id = None
        entity_name = None

        if entity:
            if isinstance(entity, list):
                if len(entity) > 0:
                    entity_id = entity[0].id
                    entity_name = entity[0].name
            else:
                entity_id = entity.id
                entity_name = entity.name

        # Always log the error regardless of persistence setting
        logger.error(f"Pipeline error: {stage.value} - {error_type}: {error_message}")

        # Only persist to database if the setting is enabled
        if settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                try:
                    cursor.execute("""
                                   INSERT INTO dash.trk_pipeline_errors
                                   (run_id, entity_id, entity_name, stage, error_message, error_type, object_id)
                                   VALUES (%s, %s, %s, %s, %s, %s, %s)
                                   """, (
                                       self.run_id,
                                       entity_id,
                                       entity_name,
                                       stage.value,
                                       error_message,
                                       error_type,
                                       object_id
                                   ))

                except Exception as e:
                    logger.error(f"Error recording pipeline error: {e}")
                    logger.exception(e)

            self.conn.commit()

    def complete_stage(self, entity: Optional[Union[VirtualEntityModel, List[VirtualEntityModel]]], stage: PipelineStage):
        """
        Mark a pipeline stage as completed.

        Args:
            entity: Entity or list of entities being analyzed
            stage: Pipeline stage to mark as completed
        """
        entity_id = None
        entity_name = None

        if entity:
            if isinstance(entity, list):
                if len(entity) > 0:
                    entity_id = entity[0].id
                    entity_name = entity[0].name
            else:
                entity_id = entity.id
                entity_name = entity.name

        # Always log the stage completion regardless of persistence setting
        logger.info(f"Completed pipeline stage: {stage.value} for run {self.run_id}")

        # Only persist to database if the setting is enabled
        if settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                try:
                    cursor.execute("""
                                   UPDATE dash.trk_pipeline_metrics
                                   SET status = 'completed', completed_at = NOW()
                                   WHERE run_id = %s AND entity_id = %s AND stage = %s
                                   """, (self.run_id, entity_id, stage.value))

                    # If no row exists, create one
                    if cursor.rowcount == 0:
                        cursor.execute("""
                                       INSERT INTO dash.trk_pipeline_metrics
                                           (run_id, entity_id, entity_name, stage, status, completed_at)
                                       VALUES (%s, %s, %s, %s, 'completed', NOW())
                                       """, (self.run_id, entity_id, entity_name, stage.value))
                except Exception as e:
                    logger.error(f"Error completing pipeline stage: {e}")
                    logger.exception(e)

            self.conn.commit()

    def track_statement_vector(self, entity: Optional[Union[Entity, List[Entity]]],
                               statement: StatementAndMetadata,
                               vector: List[float],
                               effect_type: EffectType):
        """
        Track a statement vector being created.

        Args:
            entity: Entity or list of entities being analyzed
            statement: Statement metadata
            vector: Effect vector
            effect_type: RED or GREEN effect type
        """
        metadata = {
            "statement_id": statement.id,
            "statement_text_preview": statement.statement_text[:100] + "..." if len(statement.statement_text) > 100 else statement.statement_text,
            "vector_length": len(vector),
            "vector_magnitude": np.linalg.norm(vector),
            "has_demise_model": statement.demise is not None
        }

        self.record_stat(
            entity=entity,
            stage=PipelineStage.STATEMENT_EXTRACTED,
            object_id=statement.id,
            effect_type=effect_type,
            metadata=metadata
        )

    def track_effect_created(self, entity: Optional[Union[Entity, List[Entity]]],
                             effect_model: EffectModel,
                             cluster_id: str):
        """
        Track an effect cluster being created.

        Args:
            entity: Entity or list of entities being analyzed
            effect_model: Effect model
            cluster_id: Cluster ID (string identifier, typically a UUID)
        """
        metadata = {
            "cluster_id": cluster_id,
            "num_statements": len(effect_model.statements),
            "domains": effect_model.relevant_domains(),
            "centroid_magnitude": np.linalg.norm(effect_model.centroid) if effect_model.centroid else 0,
            "statement_ids": [s.id for s in effect_model.statements if s.id]
        }

        self.record_stat(
            entity=entity,
            stage=PipelineStage.EFFECT_CREATED,
            count=1,
            effect_type=effect_model.effect_type,
            metadata=metadata
        )

    def track_effect_stored(self, entity: Optional[Union[Entity, List[Entity]]],
                            effect_model: EffectModel):
        """
        Track an effect being stored in the database.

        Args:
            entity: Entity or list of entities being analyzed
            effect_model: Effect model with ID set
        """
        if not effect_model.id:
            logger.warning("Effect model has no ID, cannot track as stored")
            return

        metadata = {
            "effect_id": effect_model.id,
            "statement_count": len(effect_model.statements),
            "domains": effect_model.relevant_domains(),
        }

        self.record_stat(
            entity=entity,
            stage=PipelineStage.EFFECT_STORED,
            object_id=effect_model.id,
            effect_type=effect_model.effect_type,
            metadata=metadata
        )

    def track_effect_flag_created(self, entity: Optional[Union[VirtualEntityModel, List[VirtualEntityModel]]],
                                  effect_flag: EffectFlagModel,
                                  is_stored: bool = False):
        """
        Track an effect flag being created.

        Args:
            entity: Entity or list of entities being analyzed
            effect_flag: Effect flag model
            is_stored: Whether the flag has been stored in the database
        """
        metadata = {
            "title": effect_flag.title[:100],
            "impact": effect_flag.impact,
            "authentic": effect_flag.authentic,
            "contribution": effect_flag.contribution,
            "confidence": effect_flag.confidence,
            "num_statements": len(effect_flag.statement_ids),
            "domains": effect_flag.domains,
            "is_merged": effect_flag.effect_flag_ids is not None and len(effect_flag.effect_flag_ids) > 0,
            "merged_from_count": len(effect_flag.effect_flag_ids) if effect_flag.effect_flag_ids else 0
        }

        # Add source tracking for merged flags
        if effect_flag.source_info:
            metadata["source_info"] = {
                "merged": effect_flag.source_info.get("merged", False),
                "merged_from_ids": effect_flag.source_info.get("merged_from_ids", []),
                "total_merged": effect_flag.source_info.get("total_merged", 0),
            }

        stage = PipelineStage.EFFECT_FLAG_STORED if is_stored else PipelineStage.EFFECT_FLAG_CREATED
        self.record_stat(
            entity=entity,
            stage=stage,
            object_id=effect_flag.id,
            effect_type=effect_flag.effect_type,
            metadata=metadata
        )

        # If stored, also record detailed metrics
        if is_stored and effect_flag.id and settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                entity_id = None
                entity_name = None

                if entity:
                    if isinstance(entity, list):
                        if len(entity) > 0:
                            entity_id = entity[0].id
                            entity_name = entity[0].name
                    else:
                        entity_id = entity.id
                        entity_name = entity.name

                cursor.execute("""
                               INSERT INTO dash.trk_effect_flag_metrics
                               (run_id, entity_id, entity_name, effect_type, flag_id, impact, authentic,
                                contribution, confidence, num_statements, num_effect_sources, merged_from_count)
                               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                               """, (
                                   self.run_id,
                                   entity_id,
                                   entity_name,
                                   effect_flag.effect_type.name.lower(),
                                   effect_flag.id,
                                   effect_flag.impact,
                                   effect_flag.authentic,
                                   effect_flag.contribution,
                                   effect_flag.confidence,
                                   len(effect_flag.statement_ids),
                                   len(effect_flag.effect_model_ids) if effect_flag.effect_model_ids else 0,
                                   len(effect_flag.effect_flag_ids) if effect_flag.effect_flag_ids else 0
                               ))

            self.conn.commit()


    def track_clustering_metrics(self,
                                 entity: Optional[Union[Entity, List[Entity]]],
                                 effect_type: EffectType,
                                 clustering_method: str,
                                 eps: float,
                                 min_samples: int,
                                 num_raw_statements: int,
                                 num_valid_statements: int,
                                 num_clusters: int,
                                 silhouette_score: Optional[float] = None,
                                 metadata: Optional[Dict[str, Any]] = None):
        """
        Track clustering metrics.

        Args:
            entity: Entity or list of entities being analyzed
            effect_type: RED or GREEN effect type
            clustering_method: Name of clustering method used
            eps: Epsilon parameter value
            min_samples: Minimum samples parameter value
            num_raw_statements: Number of raw statements before filtering
            num_valid_statements: Number of valid statements after filtering
            num_clusters: Number of clusters created
            silhouette_score: Optional silhouette score for clustering quality
            metadata: Optional additional metadata
        """
        entity_id = None
        entity_name = None

        if entity:
            if isinstance(entity, list):
                if len(entity) > 0:
                    entity_id = entity[0].id
                    entity_name = entity[0].name
            else:
                entity_id = entity.id
                entity_name = entity.name

        statements_per_cluster = num_valid_statements / num_clusters if num_clusters > 0 else 0

        # Always log the clustering metrics regardless of persistence setting
        logger.info(f"Clustering metrics for run {self.run_id}: "
                    f"found {num_clusters} clusters from {num_valid_statements} statements "
                    f"using {clustering_method} (eps={eps}, min_samples={min_samples})")

        # Only persist to database if the setting is enabled
        if settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                               INSERT INTO dash.trk_clustering_metrics
                               (run_id, entity_id, entity_name, effect_type, clustering_method, eps, min_samples,
                                num_raw_statements, num_valid_statements, num_clusters, statements_per_cluster, silhouette_score)
                               VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                               """, (
                                   self.run_id,
                                   entity_id,
                                   entity_name,
                                   effect_type.name.lower(),
                                   clustering_method,
                                   eps,
                                   min_samples,
                                   num_raw_statements,
                                   num_valid_statements,
                                   num_clusters,
                                   statements_per_cluster,
                                   silhouette_score
                               ))

            self.conn.commit()

    def track_vectorization_metrics(self,
                                    entity: Optional[Union[Entity, List[Entity]]],
                                    effect_type: EffectType,
                                    vectors: List[List[float]]):
        """
        Track vectorization quality metrics.

        Args:
            entity: Entity or list of entities being analyzed
            effect_type: RED or GREEN effect type
            vectors: List of effect vectors
        """
        if not vectors:
            logger.warning("No vectors provided for vectorization metrics")
            return

        entity_id = None
        entity_name = None

        if entity:
            if isinstance(entity, list):
                if len(entity) > 0:
                    entity_id = entity[0].id
                    entity_name = entity[0].name
            else:
                entity_id = entity.id
                entity_name = entity.name

        # Convert to numpy for calculations
        np_vectors = np.array(vectors)

        # Calculate metrics
        magnitudes = np.linalg.norm(np_vectors, axis=1)
        avg_magnitude = np.mean(magnitudes)
        std_magnitude = np.std(magnitudes)

        # Calculate median pairwise distance (for a sample if there are many vectors)
        max_samples = 1000  # Limit computation for large vector sets
        if len(vectors) > max_samples:
            sample_indices = np.random.choice(len(vectors), max_samples, replace=False)
            sample_vectors = np_vectors[sample_indices]
        else:
            sample_vectors = np_vectors

        # Calculate pairwise distances
        distances = []
        for i in range(len(sample_vectors)):
            for j in range(i+1, len(sample_vectors)):
                dist = np.linalg.norm(sample_vectors[i] - sample_vectors[j])
                distances.append(dist)

        median_distance = np.median(distances) if distances else 0

        # Always log the vectorization metrics regardless of persistence setting
        logger.info(f"Vectorization metrics for run {self.run_id}: "
                    f"avg magnitude: {avg_magnitude:.2f}, median distance: {median_distance:.2f}, "
                    f"vector dim: {len(vectors[0])}")

        # Only persist to database if the setting is enabled
        if settings.flag_pipeline_tracker_db_persistence:
            with self.conn.cursor() as cursor:
                cursor.execute("""
                               INSERT INTO dash.trk_vectorization_metrics
                               (run_id, entity_id, entity_name, effect_type, vector_dim, avg_magnitude,
                                std_magnitude, median_pairwise_distance)
                               VALUES (%s, %s, %s, %s, %s, %s, %s, %s)
                               """, (
                                   self.run_id,
                                   entity_id,
                                   entity_name,
                                   effect_type.name.lower(),
                                   len(vectors[0]),
                                   float(avg_magnitude),
                                   float(std_magnitude),
                                   float(median_distance)
                               ))

            self.conn.commit()

    def get_pipeline_summary(self) -> Dict[str, Any]:
        """
        Get a summary of the pipeline metrics for the current run.

        Returns:
            Dictionary with pipeline metrics
        """
        # If database persistence is disabled, return an empty summary
        if not settings.flag_pipeline_tracker_db_persistence:
            logger.warning("Pipeline tracker database persistence is disabled, returning empty summary")
            return {
                "note": "Pipeline tracker database persistence is disabled",
                "run_id": self.run_id
            }

        # Otherwise, query the database for metrics
        with self.conn.cursor() as cursor:
            cursor.execute("""
                           SELECT
                               stage,
                               SUM(count) as total_count,
                               SUM(red_count) as red_count,
                               SUM(green_count) as green_count
                           FROM dash.trk_pipeline_metrics
                           WHERE run_id = %s
                           GROUP BY stage
                           ORDER BY stage
                           """, (self.run_id,))

            summary = {}
            for row in cursor.fetchall():
                summary[row[0]] = {
                    "total": row[1],
                    "red": row[2],
                    "green": row[3]
                }

            # Get processing times
            cursor.execute("""
                           SELECT
                               stage,
                               SUM(total_processing_time_ms) as processing_time_ms
                           FROM dash.trk_pipeline_metrics
                           WHERE run_id = %s
                           GROUP BY stage
                           ORDER BY stage
                           """, (self.run_id,))

            for row in cursor.fetchall():
                if row[0] in summary:
                    summary[row[0]]["processing_time_ms"] = row[1]

            # Get error counts
            cursor.execute("""
                           SELECT
                               stage,
                               COUNT(*) as error_count
                           FROM dash.trk_pipeline_errors
                           WHERE run_id = %s
                           GROUP BY stage
                           ORDER BY stage
                           """, (self.run_id,))

            for row in cursor.fetchall():
                if row[0] in summary:
                    summary[row[0]]["errors"] = row[1]
                else:
                    summary[row[0]] = {"errors": row[1]}

            # Add effect flag quality metrics
            cursor.execute("""
                           SELECT
                               effect_type,
                               AVG(impact) as avg_impact,
                               AVG(confidence) as avg_confidence,
                               AVG(num_statements) as avg_statements,
                               COUNT(*) as flag_count
                           FROM dash.trk_effect_flag_metrics
                           WHERE run_id = %s
                           GROUP BY effect_type
                           """, (self.run_id,))

            flag_metrics = {}
            for row in cursor.fetchall():
                flag_metrics[row[0]] = {
                    "avg_impact": row[1],
                    "avg_confidence": row[2],
                    "avg_statements": row[3],
                    "count": row[4]
                }

            summary["flag_metrics"] = flag_metrics

        return summary
