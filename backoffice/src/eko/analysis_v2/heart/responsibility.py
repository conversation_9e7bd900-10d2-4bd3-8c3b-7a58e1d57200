"""
Responsibility Matrix Model implementation.

This module implements the Responsibility Matrix Model analysis for EffectFlags.
It examines the temporal relationships between EffectFlags, Effects, and Statements
to determine how a company responds to issues.
"""

import json
from enum import Enum
from typing import List, Optional, Dict, Any, Tuple
# Prevent circular import
# We import DEMISEModel inside functions where needed
from typing import TYPE_CHECKING

from loguru import logger
from pydantic import BaseModel, Field

from eko.cache.pg_cache import MultiLevelCache
from eko.db import get_bo_conn
from eko.llm import LLMModel
from eko.llm.main import call_llms_typed, get_embedding
from eko.llm.prompts import prompt
from eko.models.vector.base_vector_model import BaseVectorModel
from eko.models.vector.demise.engagement import EngagementDimension
from eko.models.vector.demise.motivation import MotivationDimension

if TYPE_CHECKING:
    from eko.models.vector.demise.demise_model import DEMISEModel

# Create a cache for responsibility analysis
cache = MultiLevelCache("responsibility_matrix")


class AdaptabilityDimension(str, Enum):
    """The adaptability dimension of the responsibility matrix model."""
    ADAPT = "adapt"  # Company addresses issues
    MISDIRECT = "misdirect"  # Company redirects attention from major issues to minor solutions
    DISTORT = "distort"  # Company admits harm but provides low grade and meaningless solutions
    DIVERT = "divert"  # Company diverts attention with numerous minor solutions
    DENY = "deny"  # Company denies responsibility for issues
    DECEIVE = "deceive"  # Company makes positive statements without matching actions
    IGNORE = "ignore"  # Company ignores issues completely
    GASLIGHT = "gaslight"  # Company describes the problem as not being a problem


class ApproachDimension(str, Enum):
    """The approach dimension of the responsibility matrix model."""
    INNOVATIVE = "innovative"  # Company develops new solutions
    SYSTEMATIC = "systematic"  # Company applies systematic methods
    INCREMENTAL = "incremental"  # Company makes small, gradual changes
    OPPORTUNISTIC = "opportunistic"  # Company acts when convenient
    HAPHAZARD = "haphazard"  # Company's approach lacks coherence


class ActionDimension(str, Enum):
    """The action dimension of the responsibility matrix model."""
    MENTION = "mention"  # Company merely mentions the issue without substantial action
    DISCUSS = "discuss"  # Company discusses the issue in more detail
    ANNOUNCE = "announce"  # Company makes formal announcements about the issue
    INVESTIGATE = "investigate"  # Company commits resources to investigate the issue
    ACT = "act"  # Company takes concrete action to address the issue


class EvidenceItem(BaseModel):
    """Evidence used in responsibility matrix analysis."""
    item_type: str  # 'Effect' or 'Statement'
    year: Optional[int]
    description: str
    red_green: str  # 'Red' or 'Green'
    impact: Optional[str]  # 'High', 'Medium', 'Low' or None for statements
    id: int


class ResponsibilityVectorModel(BaseVectorModel):
    """
    Vector model representation of the Responsibility Model for vector space operations.
    
    This model contains individual fields for each enum value with associated float scores.
    The fields are grouped by dimension (Adaptability, Engagement, Motivation, Approach, Action).
    Each field represents the "score" or "weight" of that particular aspect of responsibility.
    """
    # Adaptability Dimension fields
    adapt: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Adapt",
        description="Company addresses issues"
    )
    distort: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Distort",
        description="Company admits harm but provides low grade and meaningless solutions"
    )
    misdirect: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Misdirect",
        description="Company redirects attention from major issues to minor solutions"
    )
    divert: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Divert",
        description="Company diverts attention with numerous minor solutions"
    )
    deny: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Deny",
        description="Company denies responsibility for issues"
    )
    deceive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Deceive",
        description="Company makes positive statements without matching actions"
    )
    ignore: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Ignore",
        description="Company ignores issues completely"
    )
    gaslight: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Gaslight",
        description="Company describes the problem as not being a problem"
    )

    # Engagement Dimension fields
    preventative: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Preventative",
        description="Company seeks to prevent issues arising"
    )
    altruistic: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Altruistic",
        description="Company does good because it's the right thing to do"
    )
    proactive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Proactive",
        description="Company solves issues before they become public"
    )
    responsive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Responsive",
        description="Company responds to issues quickly"
    )
    reactive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Reactive",
        description="Company responds only after pressure"
    )
    dismissive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Dismissive",
        description="Company dismisses importance of issues"
    )
    passive: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Passive",
        description="Company passively acknowledges issues"
    )

    # Motivation Dimension fields
    genuine: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Genuine",
        description="Company genuinely cares about addressing issues"
    )
    compliant: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Compliant",
        description="Company acts to comply with regulations"
    )
    pressured: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Pressured",
        description="Company acts due to external pressure"
    )
    superficial: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Superficial",
        description="Company acts superficially for appearance"
    )
    opportunistic: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Opportunistic",
        description="Company acts to gain advantage"
    )

    # Approach Dimension fields
    innovative: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Innovative",
        description="Company develops new solutions"
    )
    systematic: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Systematic",
        description="Company applies systematic methods"
    )
    incremental: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Incremental",
        description="Company makes small, gradual changes"
    )
    approach_opportunistic: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Approach Opportunistic",
        description="Company acts when convenient"
    )
    haphazard: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Haphazard",
        description="Company's approach lacks coherence"
    )

    # Action Dimension fields
    mention: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Mention",
        description="Company merely mentions the issue without substantial action"
    )
    discuss: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Discuss",
        description="Company discusses the issue in more detail"
    )
    announce: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Announce",
        description="Company makes formal announcements about the issue"
    )
    investigate: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Investigate",
        description="Company commits resources to investigate the issue"
    )
    act: float = Field(
        0.0, ge=0.0, le=1.0,
        title="Act",
        description="Company takes concrete action to address the issue"
    )

    def to_responsibility_model(self) -> 'ResponsibilityModel':
        """
        Convert this vector model to a ResponsibilityModel.
        
        This method finds the highest scoring value in each dimension
        and creates a ResponsibilityModel with those values.
        
        Returns:
            ResponsibilityModel: A responsibility model based on this vector model
        """
        # Find highest scoring adaptability dimension
        adaptability_scores = {
            AdaptabilityDimension.ADAPT: self.adapt,
            AdaptabilityDimension.DISTORT: self.distort,
            AdaptabilityDimension.MISDIRECT: self.misdirect,
            AdaptabilityDimension.DIVERT: self.divert,
            AdaptabilityDimension.DENY: self.deny,
            AdaptabilityDimension.DECEIVE: self.deceive,
            AdaptabilityDimension.IGNORE: self.ignore,
            AdaptabilityDimension.GASLIGHT: self.gaslight if hasattr(self, 'gaslight') else 0.0
        }
        adaptability_dimension = max(adaptability_scores.items(), key=lambda x: x[1])[0]
        adaptability_score = adaptability_scores[adaptability_dimension]

        # Find highest scoring engagement dimension
        engagement_scores = {
            EngagementDimension.PREVENTATIVE: self.preventative,
            EngagementDimension.ALTRUISTIC: self.altruistic,
            EngagementDimension.PROACTIVE: self.proactive,
            EngagementDimension.RESPONSIVE: self.responsive,
            EngagementDimension.REACTIVE: self.reactive,
            EngagementDimension.DISMISSIVE: self.dismissive,
            EngagementDimension.PASSIVE: self.passive
        }
        engagement_dimension = max(engagement_scores.items(), key=lambda x: x[1])[0]
        engagement_score = engagement_scores[engagement_dimension]

        # Find highest scoring motivation dimension
        motivation_scores = {
            MotivationDimension.GENUINE: self.genuine,
            MotivationDimension.COMPLIANT: self.compliant,
            MotivationDimension.PRESSURED: self.pressured,
            MotivationDimension.SUPERFICIAL: self.superficial,
            MotivationDimension.OPPORTUNISTIC: self.opportunistic
        }
        motivation_dimension = max(motivation_scores.items(), key=lambda x: x[1])[0]
        motivation_score = motivation_scores[motivation_dimension]

        # Find highest scoring approach dimension
        approach_scores = {
            ApproachDimension.INNOVATIVE: self.innovative,
            ApproachDimension.SYSTEMATIC: self.systematic,
            ApproachDimension.INCREMENTAL: self.incremental,
            ApproachDimension.OPPORTUNISTIC: self.approach_opportunistic,
            ApproachDimension.HAPHAZARD: self.haphazard
        }
        approach_dimension = max(approach_scores.items(), key=lambda x: x[1])[0]
        approach_score = approach_scores[approach_dimension]

        # Find highest scoring action dimension
        action_scores = {
            ActionDimension.MENTION: self.mention,
            ActionDimension.DISCUSS: self.discuss,
            ActionDimension.ANNOUNCE: self.announce,
            ActionDimension.INVESTIGATE: self.investigate,
            ActionDimension.ACT: self.act
        }
        action_dimension = max(action_scores.items(), key=lambda x: x[1])[0]
        action_score = action_scores[action_dimension]

        # Create and return ResponsibilityModel
        return ResponsibilityModel(
            adaptability_dimension=adaptability_dimension,
            engagement_dimension=engagement_dimension,
            motivation_dimension=motivation_dimension,
            approach_dimension=approach_dimension,
            action_dimension=action_dimension,
            adaptability_score=adaptability_score,
            engagement_score=engagement_score,
            motivation_score=motivation_score,
            approach_score=approach_score,
            action_score=action_score,
            description="Generated from vector model",
            summary="Converted from vector representation",
            reasoning="Automated conversion from vector model"
        )


class ResponsibilityModel(BaseModel):
    """
    Responsibility Model for analyzing how a company responds to issues.
    
    The model has five dimensions:
    1. Adaptability: How a company adapts to issues
    2. Engagement: How a company engages with issues
    3. Motivation: What motivates a company's response
    4. Approach: How a company approaches solutions
    5. Action: Level of action taken by the company
    
    Each dimension has multiple values as defined in their respective enums.
    """
    adaptability_dimension: AdaptabilityDimension
    engagement_dimension: EngagementDimension
    motivation_dimension: MotivationDimension
    approach_dimension: ApproachDimension
    action_dimension: ActionDimension
    description: str = Field(description="Detailed explanation of the responsibility matrix")
    summary: str = Field(description="Brief summary of the responsibility matrix")
    reasoning: str = Field(description="Reasoning behind the matrix evaluation")

    # Dimension scores (between 0.0 and 1.0)
    adaptability_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Score for adaptability dimension")
    engagement_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Score for engagement dimension")
    motivation_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Score for motivation dimension")
    approach_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Score for approach dimension")
    action_score: float = Field(default=0.5, ge=0.0, le=1.0, description="Score for action dimension")

    def to_vector_model(self) -> 'ResponsibilityVectorModel':
        """
        Convert the responsibility model to a vector model.
        
        Returns:
            ResponsibilityVectorModel: A vector model representation of this responsibility model
        """
        # Create a default vector model with all zeros
        vector_model = ResponsibilityVectorModel.construct()

        # Set the corresponding dimension value to the score
        # Adaptability dimension
        if self.adaptability_dimension == AdaptabilityDimension.ADAPT:
            vector_model.adapt = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.DISTORT:
            vector_model.distort = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.MISDIRECT:
            vector_model.misdirect = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.DIVERT:
            vector_model.divert = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.DENY:
            vector_model.deny = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.DECEIVE:
            vector_model.deceive = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.IGNORE:
            vector_model.ignore = self.adaptability_score
        elif self.adaptability_dimension == AdaptabilityDimension.GASLIGHT:
            vector_model.gaslight = self.adaptability_score

        # Engagement dimension
        if self.engagement_dimension == EngagementDimension.PREVENTATIVE:
            vector_model.preventative = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.ALTRUISTIC:
            vector_model.altruistic = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.PROACTIVE:
            vector_model.proactive = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.RESPONSIVE:
            vector_model.responsive = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.REACTIVE:
            vector_model.reactive = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.DISMISSIVE:
            vector_model.dismissive = self.engagement_score
        elif self.engagement_dimension == EngagementDimension.PASSIVE:
            vector_model.passive = self.engagement_score

        # Motivation dimension
        if self.motivation_dimension == MotivationDimension.GENUINE:
            vector_model.genuine = self.motivation_score
        elif self.motivation_dimension == MotivationDimension.COMPLIANT:
            vector_model.compliant = self.motivation_score
        elif self.motivation_dimension == MotivationDimension.PRESSURED:
            vector_model.pressured = self.motivation_score
        elif self.motivation_dimension == MotivationDimension.SUPERFICIAL:
            vector_model.superficial = self.motivation_score
        elif self.motivation_dimension == MotivationDimension.OPPORTUNISTIC:
            vector_model.opportunistic = self.motivation_score

        # Approach dimension
        if self.approach_dimension == ApproachDimension.INNOVATIVE:
            vector_model.innovative = self.approach_score
        elif self.approach_dimension == ApproachDimension.SYSTEMATIC:
            vector_model.systematic = self.approach_score
        elif self.approach_dimension == ApproachDimension.INCREMENTAL:
            vector_model.incremental = self.approach_score
        elif self.approach_dimension == ApproachDimension.OPPORTUNISTIC:
            vector_model.approach_opportunistic = self.approach_score
        elif self.approach_dimension == ApproachDimension.HAPHAZARD:
            vector_model.haphazard = self.approach_score

        # Action dimension
        if self.action_dimension == ActionDimension.MENTION:
            vector_model.mention = self.action_score
        elif self.action_dimension == ActionDimension.DISCUSS:
            vector_model.discuss = self.action_score
        elif self.action_dimension == ActionDimension.ANNOUNCE:
            vector_model.announce = self.action_score
        elif self.action_dimension == ActionDimension.INVESTIGATE:
            vector_model.investigate = self.action_score
        elif self.action_dimension == ActionDimension.ACT:
            vector_model.act = self.action_score

        return vector_model


def get_similar_effects(effect_flag_id: int, threshold: float = 2.0) -> List[Dict[str, Any]]:
    """
    Find similar effects to a given effect flag using vector similarity.
    
    Args:
        effect_flag_id: The ID of the effect flag to find similar effects for
        threshold: Similarity threshold (default: 0.75)
        
    Returns:
        List of similar effects
    """
    # Import here to avoid circular import
    from eko.models.vector.demise.demise_model import DEMISEModel
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Get the demise centroid for the effect flag
            cur.execute("""
                SELECT demise_centroid_json, entity_ids, entity_name, year, description
                FROM ana_effect_flags e
                WHERE e.id = %s
            """, (effect_flag_id,))
            flag_data = cur.fetchone()

            if not flag_data:
                logger.error(f"Effect flag with ID {effect_flag_id} not found")
                return []

            flag_demise_json = flag_data[0]
            entity_ids = flag_data[1] or []
            entity_name = flag_data[2]
            flag_year = flag_data[3]
            description = flag_data[4]

            # If no entity IDs, we can't find related effects
            if not entity_ids:
                logger.warning(f"Effect flag with ID {effect_flag_id} has no entity IDs")
                return []

            # First, try to find similar effects using cosine similarity with PG vector extension if demise centroid exists
            if flag_demise_json and isinstance(flag_demise_json, dict) and len(flag_demise_json) > 0:
                # Convert the flag's demise centroid to a Postgres vector
                demise_vector = DEMISEModel.model_validate(flag_demise_json).to_vector()
                text_embedding = get_embedding(description)

                # Use PG Vector's cosine similarity to find similar effects
                cur.execute("""
                    SELECT e.id, entity_name, 
                             e.year, e.description, e.title,
                            (e.impact/100 * e.authentic/100 * e.contribution/100 ) * 100 as impact,
                           ((e.demise_embedding <=> %s::vector)  *
                           (e.text_embedding <=> %s::vector)) as similarity
                    FROM ana_effect_flags e JOIN kg_statements_v2 s ON s.id = ANY(e.statement_ids)
                    WHERE (s.company_id = ANY(%s::bigint[]) OR s.subject_entities && %s::bigint[] OR s.object_entities && %s::bigint[]) 
                    AND (e.demise_embedding <=> %s::vector < %s
                    OR e.text_embedding <=> %s::vector < 0.8)
                    ORDER BY similarity
                    LIMIT 30
                """, (demise_vector, text_embedding, entity_ids, entity_ids, entity_ids, demise_vector, threshold,
                      text_embedding,))

                effects = cur.fetchall()

                # Filter by similarity threshold
                similar_effects = []
                for effect in effects:
                    effect_id = effect[0]
                    effect_entity_name = effect[1]
                    effect_year = effect[2]
                    effect_description = effect[3]
                    effect_title = effect[4]
                    effect_impact = effect[5]
                    effect_similarity = effect[5]

                    # Only include effects with similarity better than threshold
                    # Note: PG vector's cosine distance is 0 for identical vectors, 1 for orthogonal
                    # So we need to invert the comparison for our threshold
                    similar_effects.append({
                        "id": effect_id,
                        "description": f"<effect-flag-title>{effect_title}</effect-flag-title><effect-flag-description>{effect_description}</effect-flag-description>",
                        "entity_name": effect_entity_name,
                        "year": effect_year,
                        "impact": effect_impact,
                        "similarity": 1 - effect_similarity,  # Convert distance to similarity
                        "red_green": "Red" if effect_impact and float(effect_impact) < 0 else "Green"
                    })

                if similar_effects:
                    return similar_effects
            return []


def get_similar_statements(effect_flag_id: int, entity_ids: List[int], threshold: float = 2.0) -> List[Dict[str, Any]]:
    """
    Find statements made by the entity that are similar to the effect flag.
    
    Args:
        effect_flag_id: The ID of the effect flag
        entity_ids: List of entity IDs to filter statements by
        threshold: Similarity threshold (default: 0.75)
        
    Returns:
        List of similar statements
    """
    # Import here to avoid circular import
    from eko.models.vector.demise.demise_model import DEMISEModel
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Get the effect flag data
            cur.execute("""
                SELECT demise_centroid_json, domains, year, title, description
                FROM ana_effect_flags
                WHERE id = %s
            """, (effect_flag_id,))

            flag_data = cur.fetchone()

            if not flag_data:
                logger.error(f"Effect flag with ID {effect_flag_id} not found")
                return []

            flag_demise_centroid = flag_data[0]
            flag_domains = flag_data[1] or []
            flag_year = flag_data[2]
            flag_title = flag_data[3] or ""
            flag_description = flag_data[4] or ""

            # First try using vector similarity if demise centroid exists
            if flag_demise_centroid and isinstance(flag_demise_centroid, dict) and len(flag_demise_centroid) > 0:
                try:
                    # Convert the flag's demise centroid to a Postgres vector
                    demise_vector = DEMISEModel.from_kv(flag_demise_centroid).to_vector()

                    # Query for statements made by the entity with vector similarity
                    flag_embedding = get_embedding(flag_description[:1000])
                    cur.execute("""
                        SELECT s.id, s.statement_text, s.context, s.model_json, tp.from_year as year,
                               ((s.demise_embedding <=> %s::vector) *
                               (s.text_embedding <=> %s::vector) ) as similarity
                        FROM kg_statements_v2 s
                        JOIN kg_time_periods tp ON s.time_period_id = tp.id
                        WHERE (s.company_id = ANY(%s::bigint[]) OR s.authors && %s::bigint[] OR s.subject_entities && %s::bigint[]) 
                          AND( s.demise_embedding <=> %s::vector < %s
                          OR s.text_embedding <=> %s::vector  < 0.8)
                          AND NOT is_impactful_action
                        ORDER BY similarity
                        LIMIT 30
                    """, (demise_vector, flag_embedding, entity_ids, entity_ids, entity_ids, demise_vector, threshold,
                          flag_embedding))

                    statements = cur.fetchall()

                    # Filter by similarity threshold
                    similar_statements = []
                    for stmt in statements:
                        stmt_id = stmt[0]
                        stmt_text = stmt[2]
                        stmt_context = stmt[2]
                        stmt_model_json = stmt[3]
                        stmt_year = stmt[4]
                        stmt_similarity = stmt[5]

                        # Only include statements with similarity better than threshold
                        # Get the engagement and motivation dimensions from the DEMISE model
                        demise_model = None
                        engagement_dimension = None
                        motivation_dimension = None
                        
                        if stmt_model_json and isinstance(stmt_model_json, dict):
                            try:
                                # Import here to avoid circular import
                                from eko.models.vector.demise.demise_model import DEMISEModel
                                demise_model = DEMISEModel.model_validate(stmt_model_json)
                                engagement_dimension = demise_model.get_engagement_dimension()
                                motivation_dimension = demise_model.get_motivation_dimension()
                            except Exception as e:
                                logger.error(f"Error extracting dimensions: {e}")
                        
                        similar_statements.append({
                            "id": stmt_id,
                            "description": f"<statement-text>{stmt_text}</statement-text><context>{stmt_context}</context>",
                            "year": stmt_year,
                            "model_json": stmt_model_json,
                            "similarity": 1 - stmt_similarity,  # Convert distance to similarity
                            "red_green": "N/A",  # Assuming company statements are positive/green
                            "engagement_dimension": engagement_dimension.value if engagement_dimension else None,
                            "motivation_dimension": motivation_dimension.value if motivation_dimension else None
                        })

                    if similar_statements:
                        return similar_statements
                except Exception as e:
                    logger.error(f"Error with vector similarity: {e}")
                    # Fall through to text-based search

            return []


def format_evidence_for_llm(effect_flag: Dict[str, Any],
                            similar_effects: List[Dict[str, Any]],
                            similar_statements: List[Dict[str, Any]]) -> str:
    """
    Format the evidence in a way that's easy for the LLM to analyze.
    
    Args:
        effect_flag: The effect flag being analyzed
        similar_effects: List of similar effects
        similar_statements: List of similar statements
        
    Returns:
        Formatted evidence as a string
    """
    # Combine all evidence and sort by year
    all_evidence = []

    # Add the effect flag
    all_evidence.append({
        "type": "Effect Flag",
        "year": effect_flag.get("year"),
        "description": effect_flag.get("description", ""),
        "red_green": "Red" if effect_flag.get("effect_type") == "red" else "Green",
        "impact": effect_flag.get("impact", 0),
        "id": effect_flag.get("id")
    })

    # Add similar effects
    for effect in similar_effects:
        all_evidence.append({
            "type": "Effect",
            "year": effect.get("year"),
            "description": effect.get("description", ""),
            "red_green": effect.get("red_green", ""),
            "impact": effect.get("impact", ""),
            "id": effect.get("id")
        })

    # Add similar statements
    for stmt in similar_statements:
        all_evidence.append({
            "type": "Statement",
            "year": stmt.get("year"),
            "description": stmt.get("description", ""),
            "red_green": stmt.get("red_green", "Green"),  # Default to Green for company statements
            "impact": "N/A",
            "id": stmt.get("id")
        })

    # Sort evidence by year
    sorted_evidence = sorted(all_evidence, key=lambda x: (x.get("year") or 0, x.get("type")))

    # Format the evidence
    formatted_evidence = ""
    for i, evidence in enumerate(sorted_evidence):
        # Add engagement and motivation dimensions if available (for statements)
        dimensions_info = ""
        if evidence.get('type') == 'Statement':
            if evidence.get('engagement_dimension'):
                dimensions_info += f"<engagement>{evidence.get('engagement_dimension')}</engagement>"
            if evidence.get('motivation_dimension'):
                dimensions_info += f"<motivation>{evidence.get('motivation_dimension')}</motivation>"
            
        formatted_evidence += f"""
<evidence id="{i + 1}" >
<red-green>{evidence.get('red_green', '')}</red-green>
<year>{evidence.get('year', 'Unknown')}</year>
<impact>{evidence.get('impact', '')}</impact>
<source-id>{evidence.get('id', '')}</source-id>
{dimensions_info}
<description>{evidence.get('description', '')}</description>
</evidence>
"""

    return formatted_evidence


def create_responsibility_prompt(effect_flag: Dict[str, Any],
                                 formatted_evidence: str) -> str:
    """
    Create a prompt for the LLM to analyze the responsibility matrix.
    
    Args:
        effect_flag: The effect flag being analyzed
        formatted_evidence: Formatted evidence string
        
    Returns:
        Prompt string for the LLM
    """
    return f"""
You are analyzing how a company responds to issues using the Responsibility Matrix Model.

# About the Responsibility Matrix Model
The Responsibility Matrix Model has five dimensions:

1. **Adaptability Dimension**: How a company adapts to issues
   - Adapt: Company addresses issues
   - Misdirect: Company redirects attention from major issues to minor solutions
   - Distort: Company admits harm but provides low grade and meaningless solutions
   - Divert: Company diverts attention with numerous minor solutions
   - Deny: Company denies responsibility for issues
   - Deceive: Company makes positive statements without matching actions
   - Ignore: Company ignores issues completely
   - Gaslight: Company describes the problem as not being a problem

2. **Engagement Dimension**: How a company engages with issues
   - Preventative: Company seeks to prevent issues arising
   - Altruistic: Company does good because it's the right thing to do
   - Proactive: Company solves issues before they become public
   - Responsive: Company responds to issues quickly
   - Reactive: Company responds only after pressure
   - Dismissive: Company dismisses importance of issues
   - Passive: Company passively acknowledges issues

3. **Motivation Dimension**: What motivates a company's response
   - Genuine: Company genuinely cares about addressing issues
   - Compliant: Company acts to comply with regulations
   - Pressured: Company acts due to external pressure
   - Superficial: Company acts superficially for appearance
   - Opportunistic: Company acts to gain advantage

4. **Approach Dimension**: How a company approaches solutions
   - Innovative: Company develops new solutions
   - Systematic: Company applies systematic methods
   - Incremental: Company makes small, gradual changes
   - Opportunistic: Company acts when convenient
   - Haphazard: Company's approach lacks coherence

5. **Action Dimension**: Level of action taken by the company
   - Mention: Company merely mentions the issue without substantial action
   - Discuss: Company discusses the issue in more detail
   - Announce: Company makes formal announcements about the issue
   - Investigate: Company commits resources to investigate the issue
   - Act: Company takes concrete action to address the issue

# Guidelines for Analysis

These are a few examples of how to do the analysis

- If a company acts to create systems that prevent issues from occurring in the first place, they are likely **Engagement: Preventative**
- If a company takes environmentally or socially beneficial actions without external pressure or regulation because it's the right thing to do, they are most likely **Engagement: Altruistic** and **Motivation: Genuine**
- If a company has a positive/green effect flag with no preceding similar negative/red effect flag, then they are likely **Adaptability: Adapt** and **Engagement: Preventative**
- If a company solves issues before they become public, they are likely **Engagement: Proactive**
- If a company has a negative/red effect flag with no following similar positive/green actions, then they are likely **Adaptability: Ignore**
- If a company has a negative/red effect flag with a following similar positive/green  actions, then they are likely **Adaptability: Adapt**
- If a company has a negative/red effect flag with following similar positive **statements** but no positive effects, they are most likely **Adaptability: Deceiving**
- If a company has a negative/red effect flag with no matching **statements** made by them, they are most likely **Adaptability: Deceiving** or **Engagement: Dismissive**
- If a company has a negative/red effect flag with a following denial, they are most likely **Engagement: Dismissive**
- If a company has a negative/red effect flag with lot of minor positive/green  actions, they are most likely **Adaptability: Divert**
- If a company has a negative/red effect flag with following similar major positive/green  actions, they are most likely **Adaptability: Adapt**
- If a company has a lot of negative/red effect flag with a single major similar positive/green  actions or two, they are most likely **Adaptability: Misdirect**
- If a company has a negative/red effect flag and admits to harm but provides meaningless actions, they are most likely **Adaptability: Distort**
- If a company portrays a definite problems as not actually being a problem (e.g. "global warming is good for you"), they are most likely **Adaptability: Gaslight**
- If a company only addresses issues after significant public outcry or media attention, they are likely **Engagement: Reactive** and **Motivation: Pressured**
- If a company consistently implements thorough solutions that go beyond minimum requirements when addressing problems, they likely show **Approach: Systematic** and **Motivation: Genuine**
- If a company makes grand announcements about sustainability initiatives but allocates minimal resources to implementation, they are likely **Action: Announce** and **Motivation: Superficial**
- If a company acknowledges an issue exists but takes no meaningful steps to address it, they are showing **Engagement: Passive** and **Action: Mention**
- If a company develops cutting-edge solutions to environmental or social challenges that create new industry standards, they demonstrate **Approach: Innovative** and **Engagement: Proactive**
- If a company only implements changes that coincide with cost savings or marketing opportunities, they are likely **Motivation: Opportunistic** and **Approach: Opportunistic**
- If a company makes small, gradual improvements to their practices over time without major announcements, they are showing **Approach: Incremental** and possibly **Motivation: Genuine**
- If a company responds to criticism by highlighting unrelated positive actions they've taken in other areas, they are likely **Adaptability: Divert**
- If a company meets exactly and only what regulations require without any additional effort, they demonstrate **Motivation: Compliant** and possibly **Engagement: Reactive**
- If a company shifts blame to consumers, suppliers, or regulators when confronted with their negative impacts, they are showing **Adaptability: Deny** and **Engagement: Dismissive**
- If a company conducts thorough research into an issue but never implements the findings, they are likely **Action: Investigate** without progressing to **Action: Act**
- If a company's sustainability efforts seem random and disconnected from their core business impacts, they likely show **Approach: Haphazard**

# The Effect Flag Being Analyzed
ID: {effect_flag.get('id')}
Title: {effect_flag.get('title', '')}
Description: {effect_flag.get('description', '')}
Effect Type: {effect_flag.get('effect_type', '')} (Red=negative, Green=positive)
Impact: {effect_flag.get('impact', '')}
Year: {effect_flag.get('year', 'Unknown')}
Entity: {effect_flag.get('entity_name', '')}

# Evidence
Below is a chronological list of related effects and statements. Pay special attention to the temporal relationships, 
the red/green nature of each item, and the impact levels.

{formatted_evidence}

# Task
Based on the evidence above, analyze how the company responds to the issues described in the Effect Flag.
For each dimension of the Responsibility Matrix Model, determine the most appropriate value and provide reasoning.
"""


def analyze_responsibility_matrix(effect_flag_id: int) -> Optional[ResponsibilityModel]:
    """
    Analyze the responsibility matrix for a given effect flag.
    
    Args:
        effect_flag_id: The ID of the effect flag to analyze
        
    Returns:
        ResponsibilityModel or None if analysis fails
    """
    # Get the effect flag data
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, effect_type, title, description, reason, impact, 
                       entity_ids, entity_name, year, demise_centroid_json
                FROM ana_effect_flags
                WHERE id = %s
            """, (effect_flag_id,))

            flag_data = cur.fetchone()

            if not flag_data:
                logger.error(f"Effect flag with ID {effect_flag_id} not found")
                return None

            effect_flag = {
                "id": flag_data[0],
                "effect_type": flag_data[1],
                "title": flag_data[2],
                "description": flag_data[3],
                "reason": flag_data[4],
                "impact": flag_data[5],
                "entity_ids": flag_data[6] or [],
                "entity_name": flag_data[7],
                "year": flag_data[8],
                "demise_centroid_json": flag_data[9]
            }

    # Get similar effects
    similar_effects = get_similar_effects(effect_flag_id)

    # Get similar statements
    similar_statements = get_similar_statements(effect_flag_id, effect_flag["entity_ids"])

    logger.info(f"Found {len(similar_effects)} similar effects and {len(similar_statements)} similar statements")
    # logger.info(f"Similar effects: {similar_effects}")
    # logger.info(f"Similar statements: {similar_statements}")

    # Format evidence for the LLM
    formatted_evidence = format_evidence_for_llm(effect_flag, similar_effects, similar_statements)

    # Create prompt for the LLM
    responsibility_prompt = create_responsibility_prompt(effect_flag, formatted_evidence)

    # Call the LLM
    cached_prompt = prompt(
        f"Analyze the following evidence to determine the Responsibility Matrix Model for this effect flag.",
        responsibility_prompt,
        cache=True
    )
    # logger.info(cached_prompt)

    try:
        llm_model = LLMModel.LONG_CONTEXT_CHEAPEST
        responsibility_model = call_llms_typed(
            [llm_model],
            cached_prompt,
            8000,
            response_model=ResponsibilityModel,
            cache_key=f"responsibility:v1:{llm_model.value.name}:{effect_flag_id}"
        )
        # distil("responsibility", responsibility_prompt, responsibility_model)
        return responsibility_model
    except Exception as e:
        logger.exception(f"Error analyzing responsibility matrix for effect flag {effect_flag_id}: {e}")
        return None


def store_responsibility_matrix(effect_flag_id: int, model: ResponsibilityModel,
                                related_effect_ids: List[int], related_statement_ids: List[int]) -> Optional[int]:
    """
    Store the responsibility matrix in the database.
    
    Args:
        effect_flag_id: The ID of the effect flag
        model: The responsibility model
        related_effect_ids: IDs of related effects used in the analysis
        related_statement_ids: IDs of related statements used in the analysis
        
    Returns:
        ID of the stored responsibility matrix
    """
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Get entity information from the effect flag
            cur.execute("""
                SELECT entity_name, entity_ids
                FROM ana_effect_flags
                WHERE id = %s
            """, (effect_flag_id,))

            entity_data = cur.fetchone()
            entity_name = entity_data[0] if entity_data else None
            entity_ids = entity_data[1] if entity_data else []

            # Convert the responsibility model to vector model for storage
            vector_model = model.to_vector_model()
            model_json = json.dumps(vector_model.model_dump())

            # Insert responsibility matrix with vector model
            cur.execute("""
                INSERT INTO ana_responsibility (
                    effect_flag_id, adaptability_dimension, engagement_dimension,
                    motivation_dimension, approach_dimension, action_dimension, description,
                    summary, reasoning, related_effect_ids, related_statement_ids,
                    entity_name, entity_ids, model_json
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s
                ) RETURNING id
            """, (
                effect_flag_id,
                model.adaptability_dimension.value,
                model.engagement_dimension.value,
                model.motivation_dimension.value,
                model.approach_dimension.value,
                model.action_dimension.value,
                model.description,
                model.summary,
                model.reasoning,
                related_effect_ids,
                related_statement_ids,
                entity_name,
                entity_ids,
                model_json
            ))

            result = cur.fetchone()
            if result is None:
                logger.error("Failed to insert responsibility matrix record")
                return None

            responsibility_id = result[0]
            conn.commit()

            return responsibility_id


def analyze_and_store_responsibility(effect_flag_id: int) -> Optional[int]:
    """
    Analyze and store the responsibility matrix for a given effect flag.
    
    Args:
        effect_flag_id: The ID of the effect flag to analyze
        
    Returns:
        ID of the stored responsibility matrix or None if analysis fails
    """
    # Get the effect flag data
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id, effect_type, title, description, impact, 
                       entity_ids, entity_name, year
                FROM ana_effect_flags
                WHERE id = %s
            """, (effect_flag_id,))

            flag_data = cur.fetchone()

            if not flag_data:
                logger.error(f"Effect flag with ID {effect_flag_id} not found")
                return None

            effect_flag = {
                "id": flag_data[0],
                "effect_type": flag_data[1],
                "title": flag_data[2],
                "description": flag_data[3],
                "impact": flag_data[4],
                "entity_ids": flag_data[5] or [],
                "entity_name": flag_data[6],
                "year": flag_data[7]
            }

    # Get similar effects
    similar_effects = get_similar_effects(effect_flag_id)
    similar_effect_ids = [effect["id"] for effect in similar_effects]

    # Get similar statements
    similar_statements = get_similar_statements(effect_flag_id, effect_flag["entity_ids"])
    similar_statement_ids = [stmt["id"] for stmt in similar_statements]

    # Analyze responsibility matrix
    responsibility_model = analyze_responsibility_matrix(effect_flag_id)

    if responsibility_model:
        # Store responsibility matrix
        responsibility_id = store_responsibility_matrix(
            effect_flag_id,
            responsibility_model,
            similar_effect_ids,
            similar_statement_ids
        )

        logger.info(f"Responsibility matrix for effect flag {effect_flag_id} stored with ID {responsibility_id}")
        return responsibility_id
    else:
        logger.error(f"Failed to analyze responsibility matrix for effect flag {effect_flag_id}")
        return None


def get_responsibility_vector_model(responsibility_id: int) -> Optional[ResponsibilityVectorModel]:
    """
    Retrieve a responsibility vector model from the database.
    
    Args:
        responsibility_id: The ID of the responsibility record
        
    Returns:
        ResponsibilityVectorModel or None if not found
    """
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT model_json
                FROM ana_responsibility
                WHERE id = %s
            """, (responsibility_id,))

            result = cur.fetchone()

            if not result or not result[0]:
                logger.error(f"Responsibility record with ID {responsibility_id} not found or has no model_json")
                return None

            model_json = result[0]

            try:
                # Convert JSON to ResponsibilityVectorModel
                return ResponsibilityVectorModel.model_validate(model_json)
            except Exception as e:
                logger.exception(f"Error deserializing model_json for responsibility ID {responsibility_id}: {e}")
                return None


def analyze_all_effect_flags() -> List[Tuple[int, Optional[int]]]:
    """
    Analyze and store responsibility matrices for all effect flags.
    
    Returns:
        List of tuples (effect_flag_id, responsibility_id)
    """
    # Get all effect flags
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
                SELECT id
                FROM ana_effect_flags
                WHERE entity_ids IS NOT NULL AND array_length(entity_ids, 1) > 0
                ORDER BY id
            """)

            effect_flag_ids = [row[0] for row in cur.fetchall()]

    results = []
    for effect_flag_id in effect_flag_ids:
        # Check if responsibility matrix already exists
        with get_bo_conn() as conn:
            with conn.cursor() as cur:
                cur.execute("""
                    SELECT id
                    FROM ana_responsibility
                    WHERE effect_flag_id = %s
                """, (effect_flag_id,))

                existing = cur.fetchone()

                if existing:
                    logger.info(
                        f"Responsibility matrix for effect flag {effect_flag_id} already exists with ID {existing[0]}")
                    results.append((effect_flag_id, existing[0]))
                    continue

        # Analyze and store responsibility matrix
        responsibility_id = analyze_and_store_responsibility(effect_flag_id)
        results.append((effect_flag_id, responsibility_id))

    return results
