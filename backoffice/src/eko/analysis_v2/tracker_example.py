"""
Example usage of the pipeline tracker with the dash schema.

This file demonstrates how to properly initialize and use the pipeline tracker
with the new dash schema.
"""

from typing import List

from eko.analysis_v2.effects.pipeline_tracker import get_pipeline_tracker
from loguru import logger

from eko.models import Entity
from eko.models.statement_metadata import StatementAndMetadata
from eko.models.vector.derived.effect import EffectModel, EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType
from eko.models.vector.derived.enums import PipelineStage


def example_track_statement_processing(
    entity: Entity,
    statements: List[StatementAndMetadata],
    vectors: List[List[float]],
    effect_type: EffectType,
    run_id: int
):
    """
    Example of tracking statement processing with the pipeline tracker.

    Args:
        entity: The entity being analyzed
        statements: List of statements with metadata
        vectors: List of statement vectors
        effect_type: RED or GREEN effect type
        run_id: Analysis run ID
    """
    # Get a tracker using the dash schema connection
    # We let get_pipeline_tracker handle getting the dash connection
    tracker = get_pipeline_tracker(run_id=run_id)

    # Track each statement being processed
    for i, (statement, vector) in enumerate(zip(statements, vectors)):
        try:
            # Track the statement vector creation
            tracker.track_statement_vector(
                entity=entity,
                statement=statement,
                vector=vector,
                effect_type=effect_type
            )
            logger.debug(f"Tracked statement {i+1}/{len(statements)}")
        except Exception as e:
            # Track any errors
            tracker.record_error(
                entity=entity,
                stage=PipelineStage.STATEMENT_EXTRACTED,
                error_message=str(e),
                error_type=type(e).__name__,
                object_id=statement.id if statement else None
            )

    # Mark the statement extraction stage as complete
    tracker.complete_stage(entity=entity, stage=PipelineStage.STATEMENT_EXTRACTED)

    # Track vectorization quality metrics
    tracker.track_vectorization_metrics(
        entity=entity,
        effect_type=effect_type,
        vectors=vectors
    )

    # Get a summary of processing
    summary = tracker.get_pipeline_summary()
    logger.info(f"Statement processing summary: {summary}")


def example_track_effect_creation(
    entity: Entity,
    effects: List[EffectModel],
    clustering_method: str,
    eps: float,
    min_samples: int,
    num_raw_statements: int,
    run_id: int
):
    """
    Example of tracking effect creation with the pipeline tracker.

    Args:
        entity: The entity being analyzed
        effects: List of effect models
        clustering_method: The clustering method used
        eps: Epsilon parameter value
        min_samples: Minimum samples parameter value
        num_raw_statements: Number of raw statements before filtering
        run_id: Analysis run ID
    """
    # Get a tracker using the dash schema connection
    tracker = get_pipeline_tracker(run_id=run_id)

    # Track clustering metrics
    green_effects = [e for e in effects if e.effect_type == EffectType.GREEN]
    red_effects = [e for e in effects if e.effect_type == EffectType.RED]

    # Track metrics for each effect type
    if green_effects:
        tracker.track_clustering_metrics(
            entity=entity,
            effect_type=EffectType.GREEN,
            clustering_method=clustering_method,
            eps=eps,
            min_samples=min_samples,
            num_raw_statements=num_raw_statements,
            num_valid_statements=sum(len(e.statements) for e in green_effects),
            num_clusters=len(green_effects)
        )

    if red_effects:
        tracker.track_clustering_metrics(
            entity=entity,
            effect_type=EffectType.RED,
            clustering_method=clustering_method,
            eps=eps,
            min_samples=min_samples,
            num_raw_statements=num_raw_statements,
            num_valid_statements=sum(len(e.statements) for e in red_effects),
            num_clusters=len(red_effects)
        )

    # Track each effect being created and stored
    for i, effect in enumerate(effects):
        # Track the effect creation
        tracker.track_effect_created(
            entity=entity,
            effect_model=effect,
            cluster_id=i
        )

        # After storing the effect in the database and getting its ID
        # We would track it as stored
        tracker.track_effect_stored(
            entity=entity,
            effect_model=effect
        )

    # Mark the effect creation stage as complete
    tracker.complete_stage(entity=entity, stage=PipelineStage.EFFECT_CREATED)
    tracker.complete_stage(entity=entity, stage=PipelineStage.EFFECT_STORED)


def example_track_effect_flags(
    entity: Entity,
    flags: List[EffectFlagModel],
    run_id: int
):
    """
    Example of tracking effect flag creation with the pipeline tracker.

    Args:
        entity: The entity being analyzed
        flags: List of effect flag models
        run_id: Analysis run ID
    """
    # Get a tracker using the dash schema connection
    tracker = get_pipeline_tracker(run_id=run_id)

    # Track each flag being created
    for flag in flags:
        # Track the flag creation
        tracker.track_effect_flag_created(
            entity=entity,
            effect_flag=flag,
            is_stored=False
        )

        # After storing the flag in the database and getting its ID
        # We would track it as stored
        tracker.track_effect_flag_created(
            entity=entity,
            effect_flag=flag,
            is_stored=True
        )

    # Mark the flag creation stage as complete
    tracker.complete_stage(entity=entity, stage=PipelineStage.EFFECT_FLAG_CREATED)
    tracker.complete_stage(entity=entity, stage=PipelineStage.EFFECT_FLAG_STORED)

    # If we were merging flags, we would also track that
    """
    # Example of tracking merged flags
    merged_flag = EffectFlagModel(...)  # Flag that resulted from merging
    merged_from_ids = [1, 2, 3]  # IDs of flags that were merged

    tracker.track_effect_flag_merged(
        entity=entity,
        merged_flag=merged_flag,
        merged_from_ids=merged_from_ids
    )

    # Mark the flag merging stage as complete
    tracker.complete_stage(entity=entity, stage=PipelineStage.EFFECT_FLAG_MERGED)
    """
