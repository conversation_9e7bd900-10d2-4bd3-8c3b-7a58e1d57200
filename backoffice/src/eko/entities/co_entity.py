import json
import re
from typing import List, Union, cast
from typing import Set, Optional

from loguru import logger
from psycopg import Cursor

from eko.entities import PotentialName, CompanyEntityData
from eko.entities.co_entity_util import clean_name_for_lei, match_name_in_potential_names, \
    lei_jurisdiction_to_country_code, find_lei_for_name
# Your existing imports
from eko.entities.companies_house import CompanySearchResult, get_company_ownership
# ---------------------------------------------------------------------------------
# Imports from your codebase
# ---------------------------------------------------------------------------------
from eko.entities.companies_house import search_companies_by_name
from eko.entities.gleif import LEIRecord, lookup_owns_by_lei, get_record_by_lei, lookup_ownedby_by_lei
from eko.entities.sec import Filing, get_subsidiaries
from eko.entities.sec import get_sec_company_data_by_name
from eko.entities.wikipedia import get_company_description
from eko.llm import LLMModel, PROMPT_NOT_CONVERSATIONAL
# Import specific functions from eko.llm instead of eko.llm.main to avoid circular imports
from eko.llm import call_llms, EvalError, call_multiple_llms, call_multiple_llms_with_vote, call_llms_str
from eko.llm.prompts import prompt
from eko.models import name_to_eko_id
from eko.nlp.country import country_name_to_code, is_us_state
from eko.typing import not_none


# ---------------------------------------------------------------------------------
# OO Processor
# ---------------------------------------------------------------------------------

class CompanyEntityProcessor:
    """
    Encapsulates the main logic in an OO-style class.
    Holds a DB cursor and a relationship processor to discover & update
    entity information as well as add relationships to the KG.
    """

    def __init__(self, cur: Cursor):
        self.cur = cur
        self.relationship_processor = KGRelationshipProcessor(self)

    def process_entity(self, data: CompanyEntityData) -> bool:
        """
        Adds or updates company information in the knowledge graph for an entity.

        1. Attempts to find the company in Companies House if no jurisdiction or GB.
        2. Tries the SEC if no jurisdiction or US.
        3. If not found and not strict, queries LLM(s) for alternate legal names.
        4. Tries to find LEI for the final name and updates relationships.

        Returns:
            True if a full LEI match was found, False otherwise.
        """

        if not self._preliminary_checks(data):
            return False  # means name was empty or invalid

        # Commit any pending transactions
        self._commit_pending_transactions()

        # Try matching in Companies House
        self._try_companies_house_match(data)

        # If still None, try matching in SEC
        self._try_sec_match(data)

        # Attempt alternate names if applicable
        self._attempt_alternate_names_if_applicable(data)

        # Final fallback for name
        final_name_to_use = self._finalize_name(data)
        logger.info(f"Final name to use: {final_name_to_use}")

        # Get a "common name"
        common_name = self._compute_common_name(final_name_to_use)
        logger.info(f"Common name for {final_name_to_use} is {common_name}")

        # Update base entity with partial info (common name, ticker, etc.)
        self._update_base_entity(data, final_name_to_use, common_name)

        # Fetch and update company description if none
        self._fetch_and_update_company_description(data, final_name_to_use)

        # Attempt to find the LEI
        lei_matched = self._attempt_lei_lookup_and_update(data, final_name_to_use)

        return lei_matched

    # -------------------------------------------------------------------------
    # Main Steps
    # -------------------------------------------------------------------------

    def _try_companies_house_match(self, data: CompanyEntityData) -> None:
        """
        Tries to find a match in Companies House if jurisdiction is None or 'GB'.
        Updates data.name_to_use, data.legal_eko_id, data.ticker, data.country
        as appropriate. Then uses the relationship processor to add CH relationships.
        """
        if data.jurisdiction is None or data.jurisdiction == "GB":
            ch_potential_names = [
                PotentialName(
                    name=x.title,
                    eko_id_part="eko:entity:ukch:number:" + x.company_number,
                    ticker=None,
                    record=x,
                )
                for x in search_companies_by_name(data.name)
            ]
            if data.strict:
                ch_potential_names = ch_potential_names[:1]

            logger.debug(f"Found {len(ch_potential_names)} potential CH names for {data.name}")
            matched_name = self._process_potential_names(data, ch_potential_names, region="GB")

            # If not strict, no direct match => ask LLM
            if (not data.strict) and (matched_name is None) and ch_potential_names:
                try:
                    from eko.llm.main import LLMOptions
                    options = LLMOptions(
                        cache_key=f"v1:companies-house-name-match-{data.entity_id}-{data.name}-{data.entity_type}",
                        eval=lambda c: is_chosen_name_valid(str(c).strip(), ch_potential_names)
                    )
                    chosen = call_llms_str(
                        [LLMModel.NORMAL_CHEAP],
                        prompt(
                            PROMPT_NOT_CONVERSATIONAL,
                            (
                                f"What is the Canonical Legal Name for the {data.entity_type} '{data.name}'? "
                                f"The Companies House has the following potential names {[x.name for x in ch_potential_names]}, "
                                "reply only with the legal name and if not in the list reply '<unknown>'?"
                            ),
                        ),
                        50,
                        options=options
                    ).strip()
                except EvalError as e:
                    logger.warning(e)
                    return

                if chosen and "<unknown>" not in chosen:
                    # See if it matches one of the potential names
                    matched = match_name_in_potential_names(chosen, ch_potential_names)
                    if matched:
                        data.name_to_use = matched.name
                        data.legal_eko_id = matched.eko_id_part
                        data.ticker = matched.ticker
                        data.country = "GB"
                        # Instead of the old add_ch_relationships(...) call:
                        self.relationship_processor.add_ch_relationships(
                            entity_id=data.entity_id,
                            ch_record=matched.record,
                            previous_entity_ids=data.previous_entity_ids,
                        )
                        logger.info(f"Found Companies House match for {chosen}")

    def _try_sec_match(self, data: CompanyEntityData) -> None:
        """
        Tries to find a match in the SEC if jurisdiction is None or 'US'.
        Updates data fields on success, then uses relationship processor to add SEC relationships.
        """
        if (not data.name_to_use or "<unknown>" in data.name_to_use) and (
                data.jurisdiction is None or data.jurisdiction == "US"
        ):
            sec_potential_names = [
                PotentialName(
                    name=not_none(x.company_name or x.company_name_long),
                    eko_id_part="eko:entity:sec:cik:" + not_none(x.cik),
                    ticker=x.ticker,
                    record=x,
                )
                for x in get_sec_company_data_by_name(data.name)
            ]
            if data.strict:
                sec_potential_names = sec_potential_names[:1]

            logger.info(f"Found {len(sec_potential_names)} potential SEC names for {data.name}")
            matched_name = self._process_potential_names(data, sec_potential_names, region="US")

            # If not strict, no direct match => ask LLM
            if (not data.strict) and (matched_name is None) and sec_potential_names:
                try:
                    chosen = call_llms(
                        [LLMModel.NORMAL_ALL_ROUNDER],
                        prompt(
                            PROMPT_NOT_CONVERSATIONAL,
                            (
                                f"What is the Canonical Legal Name for the {data.entity_type} '{data.name}'? "
                                f"The SEC has the following potential names {[x.name for x in sec_potential_names]}, "
                                "reply only with the legal name and if not in the list reply '<unknown>'?"
                            ),
                        ),
                        50,
                        cache_key=f"v1:sec-name-match-{data.name}-{data.entity_type}",
                        eval=lambda c: is_chosen_name_valid(c.strip(), sec_potential_names),
                    )
                except EvalError as e:
                    logger.warning(e)
                    return

                if chosen and "<unknown>" not in chosen:
                    matched = match_name_in_potential_names(chosen.strip(), sec_potential_names)
                    if matched:
                        data.name_to_use = matched.name
                        data.legal_eko_id = matched.eko_id_part
                        data.ticker = matched.ticker
                        data.country = "US"
                        # Instead of old add_sec_relationships(...) call:
                        self.relationship_processor.add_sec_relationships(
                            entity_id=data.entity_id,
                            sec_record=matched.record,
                            previous_entity_ids=data.previous_entity_ids,
                        )
                        logger.info(f"Found SEC match for {chosen}")

    def _attempt_alternate_names_if_applicable(self, data: CompanyEntityData) -> None:
        """
        If strict is False and no match was found, tries to ask LLM for more
        legal names, unless the max depth has been reached.
        """
        if (
                not data.strict
                and ((not data.name_to_use) or "<unknown>" in data.name_to_use)
                and (data.jurisdiction is None)
        ):
            if len(data.previous_names) > 4:
                logger.warning("Max depth reached; not asking LLM for more names")
            else:
                self._attempt_alternate_names_from_llms(data)

    def _finalize_name(self, data: CompanyEntityData) -> str:
        """
        Final fallback for name if data.name_to_use is None or <unknown>.
        """
        if not data.name_to_use or "<unknown>" in data.name_to_use:
            return data.name
        return data.name_to_use

    # -------------------------------------------------------------------------
    # Helper Methods
    # -------------------------------------------------------------------------

    def _preliminary_checks(self, data: CompanyEntityData) -> bool:
        """
        Performs input validation, logs some preliminary info.
        """
        if data.previous_entity_ids is None:
            raise ValueError("previous_entity_ids must not be None")

        if not data.previous_names:
            data.previous_names = []

        if not data.name or not data.name.strip():
            logger.warning("Name is empty or whitespace")
            return False
        return True

    def _commit_pending_transactions(self) -> None:
        """
        Commits any pending transactions before proceeding.
        """
        self.cur.connection.commit()

    def _process_potential_names(
            self, data: CompanyEntityData, potential_names: List[PotentialName], region: str
    ) -> Optional[str]:
        """
        Checks if data.name exactly matches any name in `potential_names` (via cleaned comparison).
        If found, updates DB with the new legal_name and region, and sets data fields.
        """
        for item in potential_names:
            if clean_name_for_lei(item.name) == clean_name_for_lei(data.name):
                logger.info(f"Found exact match for {data.name} => {item.name}")
                try:
                    self.cur.execute(
                        """
                        UPDATE kg_base_entities
                           SET legal_name = %s, jurisdiction = %s
                         WHERE id = %s
                        """,
                        (item.name, region, data.entity_id),
                    )
                except Exception as e:
                    logger.error(
                        f"Error updating legal_name/jurisdiction for entity_id={data.entity_id} ({data.name}): {e}"
                    )

                _update_org_company_canonical(self.cur, data.name, data.entity_id, data.entity_type)
                data.name_to_use = item.name
                data.legal_eko_id = item.eko_id_part
                data.ticker = item.ticker
                data.country = region
                return item.name

        return None

    def _update_base_entity(
            self, data: CompanyEntityData, final_name_to_use: str, common_name: str
    ) -> None:
        """
        Updates the kg_base_entities table with partial info: name, EKO ID, ticker, etc.
        """
        try:
            self.cur.execute(
                """
                UPDATE kg_base_entities
                   SET llm_cleaned_name = %s,
                       canonical_eko_id  = %s,
                       common_name       = %s,
                       jurisdiction      = %s,
                       scope             = %s
                 WHERE id = %s
                """,
                (
                    final_name_to_use,
                    data.legal_eko_id,
                    common_name,
                    data.country,
                    data.country.lower() if data.country else None,
                    data.entity_id,
                ),
            )
            # Only update ticker if it doesn't exist
            self.cur.execute(
                """
                UPDATE kg_base_entities
                   SET ticker = %s
                 WHERE id = %s
                   AND ticker IS NULL
                """,
                (data.ticker, data.entity_id),
            )
        except Exception as e:
            logger.error(f"Error updating base entity {data.entity_id}: {e}")

    def _fetch_and_update_company_description(
            self, data: CompanyEntityData, final_name_to_use: str
    ) -> None:
        """
        Fetches a short description for the company and updates `description` field if null.
        """
        description = get_company_description(final_name_to_use)
        logger.info(f"Fetched company description for {final_name_to_use}: {description}")
        try:
            self.cur.execute(
                """
                UPDATE kg_base_entities
                   SET description = %s
                 WHERE id = %s
                   AND description IS NULL
                """,
                (description, data.entity_id),
            )
        except Exception as e:
            logger.error(f"Error updating company description for {data.entity_id}: {e}")

    def _attempt_lei_lookup_and_update(
            self, data: CompanyEntityData, final_name_to_use: str
    ) -> bool:
        """
        Attempts to find and update the LEI for the final name.
        Returns True if matched LEI was found, False otherwise.
        """
        exact_match, matched_lei = find_lei_for_name(final_name_to_use)
        if matched_lei is None and (data.name != final_name_to_use):
            logger.debug(f"No LEI found for {final_name_to_use}, trying {data.name} as fallback")
            exact_match, matched_lei = find_lei_for_name(data.name)
            if matched_lei:
                logger.info(f"Found LEI for fallback name {data.name}")

        if not matched_lei:
            logger.warning(f"No LEI found for {data.name} or {final_name_to_use}")
            return False
        else:
            logger.info(f"Found LEI for {data.name} => {matched_lei.company_name}")

        new_country = lei_jurisdiction_to_country_code(matched_lei.entity_jurisdiction)
        try:
            self.cur.execute(
                """
                UPDATE kg_base_entities
                   SET lei         = %s,
                       legal_name  = %s,
                       lei_record  = %s,
                       lei_exact   = %s,
                       jurisdiction= %s,
                       scope       = %s
                 WHERE id = %s
                """,
                (
                    matched_lei.lei,
                    matched_lei.company_name,
                    json.dumps(matched_lei.record),
                    exact_match,
                    new_country,
                    new_country.lower() if new_country else None,
                    data.entity_id,
                ),
            )
            self.cur.execute(
                """
                UPDATE kg_base_entities
                   SET canonical_eko_id = %s
                 WHERE id = %s
                   AND canonical_eko_id IS NULL
                """,
                (
                    data.legal_eko_id or f"eko:entity:lei:{matched_lei.lei}",
                    data.entity_id,
                ),
            )
        except Exception as e:
            logger.error(f"Error updating LEI info for {data.entity_id}: {e}")

        # Instead of old add_lei_relationships(...):
        self.relationship_processor.add_lei_relationships(
            entity_id=data.entity_id,
            lei_record=matched_lei,
            previous_entity_ids=set(),  # or data.previous_entity_ids if you prefer
        )
        self.cur.connection.commit()
        return True

    def _attempt_alternate_names_from_llms(self, data: CompanyEntityData) -> None:
        """
        Asks multiple LLMs to suggest canonical legal names if we can't find a
        Companies House or SEC match, then tries each result.
        """
        possible_name_lists = cast( List[str], call_multiple_llms(
            [LLMModel.DEEPINFRA_MISTRAL_SMALL, LLMModel.GEMINI_FLASH_LITE, LLMModel.GPT_4O_MINI],
            prompt(
                PROMPT_NOT_CONVERSATIONAL,
                (
                    f"What are Canonical Legal Names for the {data.entity_type} '{data.name}' "
                    "such as it would be found in an LEI database, SEC filing, Company Registration, or Companies House? "
                    "Reply only with the legal names in a **semicolon** separated list. For example:\n\n"
                    "Tesla;Tesla Inc;Tesla UK\n\n"
                    "Only include ones you believe exist and are correct. "
                    "If not known or it is not a company, reply '<unknown>'?"
                ),
            ),
            50,
            cache_key=f"v5:name-match-{data.name}-{data.entity_type}",
            metadata={"label": "legal-name-match"},
            eval=lambda x: "<unknown>" in str(x) or len(str(x).split(";")) >= 1,
        ))

        # For each list returned by an LLM
        for potential_name_list in possible_name_lists:
            if "<unknown>" in potential_name_list:
                logger.info(
                    f"LLM did not return a valid name for {data.name}: {potential_name_list}"
                )
                continue

            logger.info(f"LLM matched name {data.name} => {potential_name_list}")
            # Try each name in the list
            for potential_name in [x.strip() for x in potential_name_list.split(";")]:
                cleaned = clean_name_for_lei(potential_name)
                if cleaned not in data.previous_names and (potential_name != data.name):
                    logger.info(
                        f"Attempting nested match for '{data.name}' => '{potential_name}' "
                        f"(previously tried: {data.previous_names})"
                    )
                    nested_attempt = self.process_entity(
                        data.copy(
                            update={
                                "name": potential_name,
                                "previous_names": data.previous_names + [cleaned],
                            }
                        )
                    )
                    # If succeeded, stop; otherwise, keep going to try the next suggestion
                    if nested_attempt:
                        return

    def _compute_common_name(self, final_name_to_use: str) -> str:
        """
        Calls multiple LLMs to figure out a short "common name" for the entity.
        Strips any stray quotes from the result.
        """
        common_name:str = cast(str, call_multiple_llms_with_vote(
            [
                LLMModel.GPT_4O_MINI,
                LLMModel.GEMINI_FLASH_LITE,
                LLMModel.DEEPINFRA_MISTRAL_SMALL,
            ],
            prompt(
                PROMPT_NOT_CONVERSATIONAL,
                f"What is the common name for the entity '{final_name_to_use}' such as you would find in a news article, blog, or tweet?",
            ),
            10,
            cache_key=f"v3:common-name-for-entity-{final_name_to_use}",
        ))
        # Strip any stray quotes
        common_name = re.sub(r'^[\'"]|[\'"]$', "", common_name)  # pyright: ignore [reportCallIssue]
        return common_name


# ---------------------------------------------------------------------------------
# Standalone Utility Functions
# ---------------------------------------------------------------------------------


class KGRelationshipProcessor:
    """
    Responsible for adding business relationships/edges (owns, owned-by, etc.)
    to the knowledge graph using data from Companies House, SEC, GLEIF (LEI), etc.
    """

    def __init__(self, processor: CompanyEntityProcessor):
        """
        :param processor: The CompanyEntityProcessor instance, which holds the DB cursor
                          and the logic for processing an individual company entity.
        """
        self.processor = processor
        self.cur: Cursor = processor.cur  # convenience shortcut

    # -------------------------------------------------------------------------
    # Public Relationship Methods
    # -------------------------------------------------------------------------

    def add_ch_relationships(
            self,
            entity_id: int,
            ch_record: CompanySearchResult,
            previous_entity_ids: Set[int],
    ) -> None:
        """
        Adds relationships for a Companies House record to the knowledge graph.
        """
        subsidiaries = get_company_ownership(ch_record.company_number, depth=1)
        for sub in subsidiaries:
            logger.info(f"Found subsidiary {sub.parent_name} -> {sub.child_name}")

            if sub.child_number == ch_record.company_number:
                # The child is our current entity
                child = entity_id
                parent = self._create_entity_for_ch_rel(
                    sub.parent_name,
                    entity_id,
                    previous_entity_ids,
                    country_name_to_code(sub.parent_country),
                )
            else:
                # The parent is our current entity
                parent = entity_id
                child = self._create_entity_for_ch_rel(
                    sub.child_name,
                    entity_id,
                    previous_entity_ids,
                    country_name_to_code(sub.child_country),
                )

            if child != parent:
                self.cur.execute(
                    """
                    INSERT INTO kg_entity_relations_map (
                        from_entity_id,
                        to_entity_id,
                        relationship_category,
                        relationship_type,
                        relationship_sub_type,
                        relationship_source,
                        relationship_data
                    )
                    VALUES (%s, %s, 'business', 'owns', 'none', 'companies_house', %s)
                    ON CONFLICT (
                        from_entity_id,
                        to_entity_id,
                        relationship_category,
                        relationship_type,
                        relationship_sub_type,
                        relationship_source
                    )
                    DO UPDATE SET relationship_data = EXCLUDED.relationship_data
                    """,
                    (parent, child, sub.model_dump_json()),
                )

    def add_sec_relationships(
            self,
            entity_id: int,
            sec_record: Filing,
            previous_entity_ids: Set[int],
    ) -> None:
        """
        Adds relationships for an SEC record to the knowledge graph.
        """
        subsidiaries = get_subsidiaries(not_none(sec_record.company_name or sec_record.company_name_long))
        logger.debug(f"Found {len(subsidiaries)} subsidiaries under SEC for {sec_record.company_name}")

        for sub in subsidiaries:
            logger.debug(f"Found subsidiary {sub.name}")
            sub_jurisdiction = (
                "US" if is_us_state(sub.jurisdiction) else country_name_to_code(sub.jurisdiction)
            )
            sub_entity_id = self._create_entity_for_sec_rel(
                sub.name,
                sub_jurisdiction,
                entity_id,
                previous_entity_ids,
            )
            if sub_entity_id != entity_id:
                self.cur.execute(
                    """
                    INSERT INTO kg_entity_relations_map (
                        from_entity_id,
                        to_entity_id,
                        relationship_category,
                        relationship_type,
                        relationship_sub_type,
                        relationship_source,
                        relationship_data
                    )
                    VALUES (%s, %s, 'business', 'owns', 'none', 'sec', %s)
                    ON CONFLICT(
                        from_entity_id,
                        to_entity_id,
                        relationship_category,
                        relationship_type,
                        relationship_sub_type,
                        relationship_source
                    )
                    DO UPDATE SET relationship_data = EXCLUDED.relationship_data
                    """,
                    (entity_id, sub_entity_id, sub.model_dump_json()),
                )

    def add_lei_relationships(
            self,
            entity_id: int,
            lei_record: LEIRecord,
            previous_entity_ids: Set[int],
    ) -> None:
        """
        Adds LEI-based relationships to the knowledge graph:
        - Entities owned by the current entity
        - Entities that own the current entity
        """
        # 1. Entities owned by the current entity
        owns = lookup_owns_by_lei(lei_record.lei)
        for owned_rec in owns:
            owned = get_record_by_lei(owned_rec.start_node_lei)
            if owned:
                logger.info(f"Found owned company {owned.company_name}")
                owned_entity_id = self._create_entity_for_lei_rel(
                    owned,
                    entity_id,
                    previous_entity_ids,
                    country_name_to_code(owned.entity_jurisdiction),
                )
                if owned_entity_id != entity_id:
                    self.cur.execute(
                        """
                        INSERT INTO kg_entity_relations_map (
                            from_entity_id,
                            to_entity_id,
                            relationship_category,
                            relationship_type,
                            relationship_sub_type,
                            relationship_source,
                            relationship_data
                        )
                        VALUES (%s, %s, 'business', 'owns', %s, 'gleif', %s)
                        ON CONFLICT (
                            from_entity_id,
                            to_entity_id,
                            relationship_category,
                            relationship_type,
                            relationship_sub_type,
                            relationship_source
                        )
                        DO UPDATE SET relationship_data = EXCLUDED.relationship_data
                        """,
                        (
                            entity_id,
                            owned_entity_id,
                            owned_rec.relationship_role.lower()
                            if owned_rec.relationship_role else "none",
                            json.dumps(owned.record),
                        ),
                    )
            else:
                logger.warning(f"Could not find owned company {owned_rec.end_node_lei}: {owned_rec}")

        # 2. Entities that own the current entity
        owned_by_list = lookup_ownedby_by_lei(lei_record.lei)
        for owned_by_rec in owned_by_list:
            owned_by_entity = get_record_by_lei(owned_by_rec.start_node_lei)
            if owned_by_entity:
                logger.info(f"Found owner company {owned_by_entity.company_name}")
                owned_by_entity_id = self._create_entity_for_lei_rel(
                    owned_by_entity,
                    entity_id,
                    previous_entity_ids,
                    country_name_to_code(owned_by_entity.entity_jurisdiction),
                )
                if owned_by_entity_id != entity_id:
                    self.cur.execute(
                        """
                        INSERT INTO kg_entity_relations_map (
                            from_entity_id,
                            to_entity_id,
                            relationship_category,
                            relationship_type,
                            relationship_sub_type,
                            relationship_source,
                            relationship_data
                        )
                        VALUES (%s, %s, 'business', 'owns', %s, 'gleif', %s)
                        """,
                        (
                            owned_by_entity_id,
                            entity_id,
                            owned_by_rec.relationship_role.lower()
                            if owned_by_rec.relationship_role else None,
                            json.dumps(owned_by_entity.record),
                        ),
                    )
            else:
                logger.warning(f"Could not find owner company {owned_by_rec.start_node_lei}: {owned_by_rec}")

    # -------------------------------------------------------------------------
    # Private "Create or Find Entity" Helpers
    # -------------------------------------------------------------------------

    def _create_entity_for_ch_rel(
            self,
            company_name: str,
            entity_id: int,
            previous_entity_ids: Set[int],
            jurisdiction: Optional[str],
    ) -> int:
        """
        Creates or finds an entity in the DB for a Companies House record,
        then calls processor.process_entity(...) for more info.
        """
        owned_entity_id = self._insert_or_get_entity(
            company_name,
            "company",
            name_to_eko_id("company", company_name),
            None,  # We'll handle final jurisdiction via the OO processor
        )
        if owned_entity_id not in previous_entity_ids and owned_entity_id != entity_id:
            previous_entity_ids.update([entity_id, owned_entity_id])
            data = CompanyEntityData(
                entity_id=owned_entity_id,
                name=company_name,
                entity_type="company",
                strict=True,
                jurisdiction=jurisdiction,
                previous_names=[company_name],
                previous_entity_ids=previous_entity_ids,
            )
            self.processor.process_entity(data)

        return owned_entity_id

    def _create_entity_for_sec_rel(
            self,
            company_name: str,
            jurisdiction: Optional[str],
            entity_id: int,
            previous_entity_ids: Set[int],
    ) -> int:
        """
        Creates or finds an entity in the DB for an SEC record,
        then calls processor.process_entity(...) for more info.
        """
        owned_entity_id = self._insert_or_get_entity(
            company_name,
            "company",
            name_to_eko_id("company", company_name),
            jurisdiction,
        )
        if owned_entity_id not in previous_entity_ids and owned_entity_id != entity_id:
            previous_entity_ids.update([entity_id, owned_entity_id])
            data = CompanyEntityData(
                entity_id=owned_entity_id,
                name=company_name,
                entity_type="company",
                strict=True,
                jurisdiction=jurisdiction,
                previous_names=[company_name],
                previous_entity_ids=previous_entity_ids,
            )
            self.processor.process_entity(data)

        return owned_entity_id

    def _create_entity_for_lei_rel(
            self,
            lei_entity: LEIRecord,
            entity_id: int,
            previous_entity_ids: Set[int],
            jurisdiction: Optional[str],
    ) -> int:
        """
        Creates or finds an entity for the given LEI record,
        then calls processor.process_entity(...) for more info.
        """
        owned_entity_id = self._insert_or_get_entity(
            lei_entity.company_name,
            "company",
            name_to_eko_id("company", lei_entity.company_name),
            None,
        )
        if owned_entity_id not in previous_entity_ids and owned_entity_id != entity_id:
            previous_entity_ids.update([entity_id, owned_entity_id])
            data = CompanyEntityData(
                entity_id=owned_entity_id,
                name=lei_entity.company_name,
                entity_type="company",
                strict=True,
                jurisdiction=jurisdiction,
                previous_names=[lei_entity.company_name],
                previous_entity_ids=previous_entity_ids,
            )
            self.processor.process_entity(data)

        return owned_entity_id

    def _insert_or_get_entity(
            self,
            name: str,
            entity_type: str,
            eko_id: str,
            jurisdiction: Optional[str],
    ) -> int:
        """
        Inserts or retrieves an entity from the DB, returning its ID.
        Uses ON CONFLICT to update (name, type) if eko_id already exists.
        """
        try:
            if jurisdiction:
                self.cur.execute(
                    """
                    INSERT INTO kg_base_entities (name, type, eko_id, jurisdiction)
                    VALUES (%s, %s, %s, %s)
                    ON CONFLICT (eko_id) DO UPDATE
                        SET name = EXCLUDED.name, type = EXCLUDED.type
                    RETURNING id
                    """,
                    (name, entity_type, eko_id, jurisdiction),
                )
            else:
                self.cur.execute(
                    """
                    INSERT INTO kg_base_entities (name, type, eko_id)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (eko_id) DO UPDATE
                        SET name = EXCLUDED.name, type = EXCLUDED.type
                    RETURNING id
                    """,
                    (name, entity_type, eko_id),
                )
            return self.cur.fetchone()[0]
        except Exception as e:
            logger.error(f"Error creating or updating entity '{name}' (type={entity_type}): {e}")
            return -1  # handle error more gracefully if needed


def _update_org_company_canonical(
        cur: Cursor,
        name: str,
        entity_id: int,
        entity_type: str
) -> None:
    """
    If there's a corresponding entity with the same name but different type
    (company vs organisation), link them via the canonical_id field.
    """
    opposite_type = "organisation" if entity_type == "company" else "company"
    try:
        cur.execute(
            "SELECT id FROM kg_base_entities WHERE type = %s AND name = %s",
            (opposite_type, name),
        )
        row = cur.fetchone()
        if row:
            cur.execute(
                """
                UPDATE kg_base_entities
                   SET canonical_id = %s
                 WHERE id = %s
                """,
                (entity_id, row[0]),
            )
    except Exception as e:
        logger.error(f"Error updating canonical_id for {name} with entity_id={entity_id}: {e}")


def is_chosen_name_valid(
        chosen: str,
        potential_names: List[PotentialName],
) -> Union[bool, str]:
    """
    Checks if the chosen name is in the list of potential names (by comparing
    cleaned versions).
    """
    if "<unknown>" in chosen:
        return True

    is_matched = any(
        clean_name_for_lei(chosen) == clean_name_for_lei(item.name)
        for item in potential_names
    )
    if is_matched:
        return True
    else:
        logger.info(f"Could not match {chosen} to any of {[(p.name, p.eko_id_part) for p in potential_names]}")
        return f"{chosen} is not in the list of options, please try again"
