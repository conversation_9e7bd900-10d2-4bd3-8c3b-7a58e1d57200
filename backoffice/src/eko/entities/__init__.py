from typing import Optional, Any, List, Set

from pydantic import BaseModel, Field


class PotentialName(BaseModel):
    """
    Represents a single candidate legal name (e.g., from Companies House or SEC),
    along with its corresponding EKO ID part, optional ticker, and raw record data.
    """
    name: str
    eko_id_part: str
    ticker: Optional[str] = None
    record: Any


class CompanyEntityData(BaseModel):
    """
    Holds all relevant data about the entity being processed, reducing
    repetition of parameters throughout the code.
    """
    entity_id: int
    name: str
    entity_type: str
    strict: bool
    jurisdiction: Optional[str]
    previous_names: List[str] = Field(default_factory=list)
    previous_entity_ids: Set[int] = Field(default_factory=set)

    # The following fields get updated as we discover new information
    name_to_use: Optional[str] = None
    legal_eko_id: Optional[str] = None
    ticker: Optional[str] = None
    country: Optional[str] = None
