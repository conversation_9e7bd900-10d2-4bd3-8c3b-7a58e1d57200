import functools
import re
from typing import List, Optional

import textdistance
from loguru import logger

from eko.entities import PotentialName
from eko.entities.gleif import LEIRecord, lookup_leis_by_company_name
from eko.nlp.country import country_name_to_code


@functools.lru_cache(maxsize=1024)
def clean_name_for_lei(name: str) -> str:
    """
    Normalizes a string by lowercasing and removing non-alphanumeric characters
    (other than whitespace).
    """
    return re.sub(r"[^\w\s]", "", name.lower())


def match_name_in_potential_names(
        chosen_name: str,
        potential_names: List[PotentialName]
) -> Optional[PotentialName]:
    """
    Finds the matching PotentialName in `potential_names` for `chosen_name`,
    based on cleaned name comparison.
    """
    chosen_clean = clean_name_for_lei(chosen_name)
    for item in potential_names:
        if clean_name_for_lei(item.name) == chosen_clean:
            return item
    return None


def lei_jurisdiction_to_country_code(jurisdiction: str) -> Optional[str]:
    """Converts an LEI jurisdiction to a country code (ISO2) if possible."""
    return country_name_to_code(jurisdiction)


def find_lei_for_name(name_from_llm: str) -> (bool, Optional[LEIRecord]):
    """
    Searches the LEI database by the given name. Attempts both an exact match (cleaned)
    and inexact/fuzzy match with jaro_winkler similarity.

    Returns:
        A tuple (found_exact: bool, record: Optional[LEIRecord])
    """
    if not name_from_llm:
        return False, None

    clean_original = (
        clean_name_for_lei(name_from_llm) if len(name_from_llm) > 6 else name_from_llm.lower()
    )

    records = lookup_leis_by_company_name(name_from_llm)
    exact_matched_lei = None
    inexact_matched_lei = None
    max_similarity = 0.0

    for record in records:
        if hasattr(record, "error"):
            logger.error(f"Error looking up LEI for {name_from_llm}: {record.error}")
            continue

        company_name = record.company_name
        clean_matched_name = (
            clean_name_for_lei(company_name) if len(company_name) > 6 else company_name.lower()
        )

        # Exact match
        if clean_matched_name == clean_original:
            logger.info(f"Exact LEI match found: {name_from_llm} == {company_name}")
            exact_matched_lei = record
            break

        # Inexact match
        sim = textdistance.jaro_winkler(clean_matched_name, clean_original)
        if len(name_from_llm) >= 6 and sim > 0.9:
            logger.info(f"Inexact LEI match: {name_from_llm} ~ {company_name} ({sim:.2f})")
            if sim > max_similarity:
                max_similarity = sim
                inexact_matched_lei = record

    return exact_matched_lei is not None, (exact_matched_lei or inexact_matched_lei)
