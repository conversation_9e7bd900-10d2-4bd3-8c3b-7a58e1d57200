import copy
import inspect
import json
import litellm
import os
import time
import traceback
from collections import Counter
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from instructor.exceptions import InstructorRetryException
from json_repair import repair_json
from typing import List, Dict, Optional, TypeVar, Type, Union, Callable, cast, Any

import anthropic
import groq
import instructor
import openai
from loguru import logger
from ollama import ResponseError
from openai import OpenAI
from pydantic import BaseModel, ValidationError

from eko import console
from eko.cache.pg_cache import MultiLevelCache
from eko.llm import LLMModel
from eko.llm.distil import distil
from eko.llm.providers.base import NoneResponseError
from eko.llm.providers.litellm_provider import LiteLLMProvider

# Original providers

# New litellm-based providers
from eko.llm.providers.anthropic_litellm import AnthropicLiteLLMProvider
from eko.llm.providers.deepseek_litellm import DeepSeekLiteLLMProvider
from eko.llm.providers.gemini_litellm import GeminiLiteLLMProvider
from eko.llm.providers.groq_litellm import GroqLiteLLMProvider
from eko.llm.providers.ollama_litellm import OllamaLiteLLMProvider
from eko.llm.providers.openai_compatible_litellm import OpenAICompatibleLiteLLMProvider
from eko.llm.providers.openai_litellm import OpenAILiteLLMProvider, OSeriesLiteLLMProvider
from eko.llm.providers.runpod_litellm import RunPodLiteLLMProvider
from eko.llm.providers.vertex_provider import VertexProvider
from eko.nlp.clean import remove_xml_tags
from eko.typing import not_none
from eko.util.hash import md5_hash_str

# Ollama remote: "http://*************:11434/v1"
provider_map: dict[str, LiteLLMProvider] = {
    # Original providers (kept for backward compatibility)
    # "gpt-old": OpenAIProvider(),
    # "o1-old": OSeriesProvider(),
    # "deepinfra-old": OpenAICompatibleProvider(api_key="q9m0BNyBATp41nKBdLzsqjdVP0cFbGgD", base_url="https://api.deepinfra.com/v1/openai", use_instructor=True, mode=instructor.Mode.JSON),
    # "hyperbolic-old": OpenAICompatibleProvider(api_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************.T79Xp4A6qaG3c2DIdA1WSpvPP57kvloW6OpE61A3EBE",
    # base_url="https://api.hyperbolic.xyz/v1", use_instructor=True),
    # "deepseek-old": OpenAICompatibleProvider(api_key="***********************************", base_url="https://api.deepseek.com", use_instructor=True, mode=instructor.Mode.JSON),
    # "deepseek-r1-old": OpenAICompatibleProvider(api_key="***********************************", base_url="https://api.deepseek.com", use_instructor=True, mode=instructor.Mode.MD_JSON),
    # "anthropic-old": AnthropicProvider(),
    # "groq-old": GroqProvider(),
    # "ollama-old": OpenAICompatibleProvider(api_key="ollama", base_url="http://*************:11434/v1", use_instructor=True, mode=instructor.Mode.JSON),
    # "runpod-old": RunPodProvider(api_key=not_none(os.environ.get("RUNPOD_API_KEY")), endpoint_id=not_none(os.environ.get("RUNPOD_ENDPOINT_ID"))),
    # "gemini-old": OpenAICompatibleProvider(api_key="AIzaSyCffot1AZ2_Jgbrjbo60kLQvFnpKbK5_vE", base_url="https://generativelanguage.googleapis.com/v1beta/openai/", use_instructor=True, mode=instructor.Mode.JSON),
    # "gemini-finetuned-old": GeminiProvider(),
    # New LiteLLM-based implementations
    "gpt": OpenAILiteLLMProvider(),
    "o1": OSeriesLiteLLMProvider(),
    "deepinfra": OpenAICompatibleLiteLLMProvider(
        api_key="q9m0BNyBATp41nKBdLzsqjdVP0cFbGgD",
        base_url="https://api.deepinfra.com/v1/openai",
        provider_name="DeepInfra",
        use_instructor=True,
        instructor_mode=instructor.Mode.JSON,
    ),
    "hyperbolic": OpenAICompatibleLiteLLMProvider(
        api_key="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**********************************************************************.T79Xp4A6qaG3c2DIdA1WSpvPP57kvloW6OpE61A3EBE",
        base_url="https://api.hyperbolic.xyz/v1",
        provider_name="Hyperbolic",
        use_instructor=True,
    ),
    "deepseek": DeepSeekLiteLLMProvider(
        api_key="***********************************",
        base_url="https://api.deepseek.com",
        use_instructor=True,
        instructor_mode=instructor.Mode.JSON,
    ),
    "deepseek-r1": DeepSeekLiteLLMProvider(
        api_key="***********************************",
        base_url="https://api.deepseek.com",
        use_instructor=True,
        instructor_mode=instructor.Mode.MD_JSON,
    ),
    "anthropic": AnthropicLiteLLMProvider(),
    "groq": GroqLiteLLMProvider(),
    "ollama": OllamaLiteLLMProvider(
        base_url="http://*************:11434/v1", use_instructor=True, instructor_mode=instructor.Mode.JSON
    ),
    "runpod": RunPodLiteLLMProvider(
        api_key=not_none(os.environ.get("RUNPOD_API_KEY")), endpoint_id=not_none(os.environ.get("RUNPOD_ENDPOINT_ID"))
    ),
    "gemini": VertexProvider(project_id="92299100527", location="us-central1"),
    "gemini-finetuned": VertexProvider(project_id="92299100527", location="us-central1"),
}

embedding_client = OpenAI()


new_cache = MultiLevelCache("llm")
rate_limited: Dict[LLMModel, float] = {model: 0 for model in LLMModel}
token_count_in: Dict[LLMModel, int] = {model: 0 for model in LLMModel}
token_count_out: Dict[LLMModel, int] = {model: 0 for model in LLMModel}
running_total: float = 0.0




def dump_costing():
    for model in LLMModel:
        model_cost = (model.value.token_pricing_per_million[0] / 1_000_000) * token_count_in[model] + (
                model.value.token_pricing_per_million[1] / 1_000_000) * \
                     token_count_out[model]
        logger.info(f"Cost for {model.value} is {(model_cost * 100):,.2f} cents")


@new_cache.memoize()
def get_embedding(text: str) -> Optional[List[float]]:
    try:
        if len(text) > 8192:
            logger.warning("Text too long for embedding, truncating")
        response = embedding_client.embeddings.create(
            input=text[:8192],
            model="text-embedding-3-small"
        )
        print('.', end='')
        return response.data[0].embedding
    except Exception as e:
        logger.exception(e)
        return None


T = TypeVar('T', bound=BaseModel)


class LLMOptions(BaseModel):
    """Common options for LLM calls, excluding llm/llms, messages, max_tokens, and response_model"""
    auto_rate_limit: bool = True
    eval: Union[None, Callable[[Union[Any, str, None]], Union[bool, str]]] = None
    eval_retry: int = 4
    no_cache: bool = False
    temperature: float = 0.0
    cache_key: Union[str, None] = None
    use_either_key: bool = True
    append_on_eval_fail: bool = True
    eval_retry_message: Union[str, None] = None
    metadata: Optional[dict] = None
    escalate_to: Union[None, List[LLMModel]] = None
    cache_prefix: Optional[str] = None
    auto_distil: Optional[str] = None
    streaming: bool = False
    accept_final_eval_fail: bool = False
    ground_with_search: bool = False
    none_responses: bool = False
    thinking: bool = False
    thinking_budget: Optional[int] = None  # None means use the default budget which is 50% of max tokens

    model_config = {"arbitrary_types_allowed": True}


class EvalError(ValueError):
    pass


def most_common_or_first(arr:List[str]):
    if not arr:
        return None  # Handle empty array

    # Count occurrences of each value
    counts = Counter(arr)

    # Find the most common element(s)
    most_common = counts.most_common()

    # Check if there is a clear most common value
    if len(most_common) == 1 or most_common[0][1] > most_common[1][1]:
        return most_common[0][0]

    # Otherwise, return the first value
    return arr[0]


def call_multiple_llms_with_vote(
    llms: List[LLMModel],
    messages: List[Dict],
    max_tokens,
    response_model: Union[Type[T], None] = None,
    options: LLMOptions = LLMOptions(),
) -> Union[Optional[str], T]:
    response = call_multiple_llms(llms, messages, max_tokens, response_model, options)
    logger.info(f"Responses: {response}")
    return most_common_or_first([x.strip() for x in response if x is not None])


def call_multiple_llms(llms: List[LLMModel], messages: List[Dict], max_tokens,
                       response_model: Union[Type[T], None] = None,
                       options: LLMOptions = LLMOptions()) -> \
        list[Union[Optional[str], T]]:
    """

    :rtype: object
    """
    with ThreadPoolExecutor(max_workers=len(llms)) as executor:
        futures = []
        result = []
        for i, llm in enumerate(llms):
            # Create options with cache_prefix for this specific LLM
            llm_options = options.model_copy(update={'cache_prefix': str(i)})
            futures.append(
                executor.submit(call_llms, [llm], messages, max_tokens, response_model, llm_options))
        for future in futures:
            try:
                result.append(future.result())
            except Exception as e:
                logger.error(f"Error in call_multiple_llms: {str(e)}")
                traceback.print_exc()
                continue

        return result


def call_llms_typed(
    llms: List[LLMModel], messages: List[Dict], max_tokens, response_model: Type[T], options: LLMOptions = LLMOptions()
) -> Union[T, None]:
    assert response_model is not None
    return cast(Union[T, None], call_llms(llms, messages, max_tokens, response_model, options))


def call_llms_str(llms: List[LLMModel], messages: List[Dict], max_tokens,
                  options: LLMOptions = LLMOptions()) -> str:
    return cast(str, call_llms(llms, messages, max_tokens, None, options))


def call_llms_json(
    llms: List[LLMModel],
    messages: List[Dict],
    max_tokens,
    options: LLMOptions = LLMOptions()
) -> Dict[Any, Any]:
    """
    Call LLMs and return the response as a JSON object, used for pure JSON handling.
    """
    string = clean_llm_json_response(call_llms_str(llms, messages, max_tokens, options))
    try:
        return json.loads(string)
    except json.JSONDecodeError:
        logger.error(f"Failed to decode JSON: {string}")
        console.print_exception(show_locals=False)
        raise



def call_llms(
    llms: List[LLMModel],
    messages: List[Dict],
    max_tokens,
    response_model: Union[Type[T], None] = None,
    options: LLMOptions = LLMOptions(),
) -> Union[str, T, None]:
    start = datetime.now()
    _messages = messages.copy()
    if options.temperature > 1.0:
        raise ValueError("Temperature must be less than or equal to 1.0")
    if options.temperature < 0 and options.temperature != -1:
        raise ValueError("Temperature must be -1 or 0..1")
    logger.info(f"Calling LLMs: {llms} with prompt: {json.dumps(messages)[:400]}")
    count = 0
    eval_result = False
    result = None
    escalated = False

    try:
        source = str(inspect.getsource(options.eval)) if options.eval else "<no source>"
    except Exception as e2:
        logger.warning(f"Failed to get source for eval. {e2}")
        source = "<no source>"

    while count < options.eval_retry:
        count += 1

        if count - 1 >= options.eval_retry / 2:
            logger.warning(f"Escalated to {options.escalate_to}")
            escalated = True
        try:
            # Create modified options for this iteration
            iteration_options = options.model_copy(update={
                'no_cache': options.no_cache if count - 1 == 0 else True,
                'eval_retry_message': eval_retry_message if 'eval_retry_message' in locals() else options.eval_retry_message
            })

            result: str | Optional[T] = _call_llms_internal(
                llms if not escalated or options.escalate_to is None else options.escalate_to,
                _messages,
                max_tokens,
                response_model,
                iteration_options,
                retry=count - 1 > 0,
                eval_failure_result=result,
            )
            if result is None and not options.none_responses:
                escalated = True
            else:
                if options.eval:
                    try:
                        eval_result = options.eval(result)
                    except Exception as e:
                        traceback.print_exc()
                        logger.warning(f"Eval failed for {source or ''} -> {e}")
                        eval_result = str(e)
                    if type(eval_result) == str:
                        eval_retry_message = eval_result
                    elif eval_result:
                        return result
                    logger.warning(f"Eval failed '{eval_result}' for {source or ''} -> {str(result)[:600]} ")
                    if count == options.eval_retry:
                        if type(eval_result) == str:
                            errorString = str(f"Eval failed for {source or ''} -> {eval_result} after {count} retries")
                            if not options.accept_final_eval_fail:
                                raise EvalError(errorString)
                            else:
                                logger.warning(
                                    f"Eval failed for {source or ''} -> {eval_result} after {count} retries accepting final result."
                                )
                                return result
                        else:
                            if not eval_result:
                                errorString = str(f"Eval failed for {source or ''} -> {result} after {count} retries")
                                if not options.accept_final_eval_fail:
                                    raise EvalError(errorString)
                                else:
                                    logger.warning(
                                        f"Eval failed for {source or ''} -> {result} after {count} retries accepting final result."
                                    )
                                    return result
                            if options.auto_distil is not None and result is not None:
                                distil(options.auto_distil, _messages[-1]["content"], result)
                            return result
                else:
                    logger.debug(result)
                    if options.auto_distil is not None and result is not None:
                        distil(options.auto_distil, _messages[-1]["content"], result)
                    return result
        except ValidationError as e:
            logger.warning(f"Validation Error for {source or ''} -> {e}")
            escalated = True
        except instructor.exceptions.IncompleteOutputException as ie:
            logger.warning(f"IncompleteOutputException for {source or ''} -> {ie} retrying ...")
            escalated = True
            eval_retry_message = f"The token length of your response was too long, please try again, token limit is {max_tokens}."

        logger.warning(f"Retrying {_messages[-1]['content'][:600]}")
        # Update options for next iteration
        options = options.model_copy(update={
            'temperature': options.temperature + 0.1 if options.temperature < 0.9 else 0.9,
            'eval_retry_message': eval_retry_message if 'eval_retry_message' in locals() else options.eval_retry_message
        })
    raise EvalError(f"Eval failed for {source or ''} -> {eval_result} after {count} retries")

def _call_llms_internal(
    llms: List[LLMModel],
    messages: List[Dict],
    max_tokens: int = 4000,
    response_model: Union[Type[T], None] = None,
    options: LLMOptions = LLMOptions(),
    retry: bool = False,
    eval_failure_result=None,
) -> Union[str, Optional[T]]:
    start = datetime.now()
    to_hash = re.sub(r"\s+", " ",
                     json.dumps(messages) + ":" + ("" if response_model is None else response_model.schema_json()))

    key1 = md5_hash_str("1.0:" + options.cache_key) if options.cache_key is not None else None
    if options.cache_prefix is not None and key1 is not None:
        key1 = options.cache_prefix + ":" + key1
    key2 = ("1.3:" + (md5_hash_str(to_hash)) + (":text" if response_model is None else ":json"))
    if options.cache_prefix is not None:
        key2 = options.cache_prefix + ":" + key2
    value1 = new_cache.get(key1) if key1 is not None else None
    value2 = new_cache.get(key2)
    value = None
    store_key = None
    if options.use_either_key and options.cache_key is not None:
        value = value1 if value1 is not None else value2
        store_key = key1
    elif options.cache_key is not None:
        value = value1
        store_key = key1
    else:
        value = value2
        store_key = key2

    # This is a retry, so we alter the message but not the cache key
    if retry:
        # Deep copy the messages so we don't alter the original
        messages = json.loads(json.dumps(messages))

        if options.append_on_eval_fail:
            if eval_failure_result is not None:
                messages.append({"role": "assistant",
                             "content": eval_failure_result if isinstance(eval_failure_result, str) else eval_failure_result.json()})
            if options.eval_retry_message is not None:
                messages.append({"role": "user",
                                 "content": options.eval_retry_message})
            else:
                try:
                    getsource = str(inspect.getsource(options.eval))
                except Exception as e2:
                    logger.warning(f"Failed to get source for eval. {e2}")
                    getsource = "<no source>"
                messages.append({"role": "user",
                                 "content": f"My eval ```python\n{getsource}``` returned False for your response, it must evaluate to True. Could you try again. "})
        else:
            if options.eval_retry_message is not None:
                messages.append({"role": "user",
                                 "content": options.eval_retry_message})
            else:
                original_main_content = messages[-1]['content']
                messages[-1][
                    "content"] = f"{original_main_content} (This was retried at {datetime.now().strftime('%H:%M:%S')} please follow the instructions carefully.)"

    # logger.info(messages)
    if value is not None and not options.no_cache and not retry:
        new_cache.set(store_key, value, expire=86400 * 30 * 3)
        logger.info("Cache Hit")
        if response_model is None:
            return str(value)
        else:
            try:
                response_model.model_validate_json(value)  # pyright: ignore [reportArgumentType]
                return response_model.parse_raw(value)  # pyright: ignore [reportArgumentType]
            except ValidationError:
                if new_cache.get(key1):
                    new_cache.delete(key1)
                if new_cache.get(key2):
                    new_cache.delete(key2)
                logger.warning("Schema changed so cached entry is invalid")

    logger.info("Cache Miss")

    # Now we have the cache key safely created we can change the message to stop llm caching.
    # if no_cache:
    #     messages[0]["content"] = " [Retrying at " + str(time.time()) + "] " + messages[0]["content"]

    while True:
        recoverable_error = False
        for llm in llms:
            if rate_limited[llm] and rate_limited[llm] - time.time() > 0:
                print(f"{llm} is rate limited for {rate_limited[llm] - time.time()} seconds")
                continue
            rate_limited[llm] = 0
            try:
                try:

                    t = time.time()
                    provider:LiteLLMProvider= provider_map[llm.value.provider]

                    # Handle streaming if requested
                    if options.streaming and hasattr(provider, 'call_chat_streaming'):
                        result = provider.call_chat_streaming(llm, messages, max_tokens, response_model, options.temperature, options.metadata or {}, options)
                    else:
                        if options.streaming:
                            logger.warning(f"Streaming requested but provider {llm.value.provider} doesn't support it. Using non-streaming call.")
                        result = provider.call_chat(llm, messages, max_tokens, response_model, options.temperature, options.metadata or {}, options)
                        
                    if response_model is None:
                        if result is None:
                            raise ValueError("Should not get None for a text response")
                    else:
                        if result is None:
                            return None
                        try:
                            response_model.model_validate_json(cast(T,result).model_dump_json())
                        except Exception as e:
                            logger.warning(f"Re-Validation Error {e}")
                            if new_cache.get(store_key):
                                new_cache.delete(store_key)
                            raise e
                        logger.debug("The response was revalidated")
                    new_cache.set(store_key, result if response_model is None else result.model_dump_json(), expire=86400 * 7)
                    # if (time.time() - t) > 10 and options.auto_rate_limit:
                    #     logger.info(
                    #         f"Time taken for {llm.value.name} call: {time.time() - t} auto rate limiting {llm.value.name}")
                    #     rate_limited[llm] = time.time() + 120
                    logger.info(
                        f"Time taken for {llm.value.name} call: {time.time() - t}")
                    return result
                except InstructorRetryException as ie:
                    logger.warning(ie)
                    logger.warning(f"{messages}")
                    if ie.__cause__ is not None and ie.__cause__.__cause__ is not None:
                        raise ie.__cause__.__cause__
                    raise ie
            except ResponseError as e:
                logger.info(e)
                continue
            except groq.RateLimitError as e:
                logger.info(e)
                logger.info(e.body["error"]["message"])
                if "daily rate limit" in e.body["error"]["message"].lower():
                    print("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except anthropic.RateLimitError as e:
                logger.info(e)
                print(e.body["error"]["message"])
                if "daily rate limit" in e.body["error"]["message"].lower():
                    logger.info("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                elif "credit balance is too low" in e.body["error"]["message"]:
                    logger.info("******** CLAUDE QUOTA EXCEEDED ********")
                    rate_limited[llm] = time.time() + 240
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except anthropic.InternalServerError as e:
                logger.error(f"Error calling anthropic: {e}")
                logger.exception(e)
                recoverable_error = True
            except anthropic.BadRequestError as e:
                if e.status_code == 429:
                    logger.info("******** CLAUDE RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 240
                    continue
                elif "credit balance" in e.message:
                    logger.info("******** CLAUDE OUT OF CREDITS ********")
                    rate_limited[llm] = time.time() + 240
                    continue
                else:
                    raise e
            except openai.RateLimitError as e:
                logger.info(e)
                msg = e.body
                if isinstance(msg, str):
                    msg = json.loads(msg)
                print(msg)
                print(type(msg))
                print(msg["message"])
                if "daily rate limit" in msg["message"].lower():
                    logger.info("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                elif " exceeded your current quota" in msg["message"]:
                    logger.info("******** OPENAI QUOTA EXCEEDED ********")
                    rate_limited[llm] = time.time() + 240
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except Exception as e:
                logger.error(e)
                console.print_exception(show_locals=False)
                if llm == llms[-1]:
                    raise e
        if recoverable_error:
            logger.info("Recoverable error, sleeping for 20 seconds then will try again.")
            time.sleep(20)
        else:
            logger.info("NO LLMs available, sleeping for 3 minutes then will try again.")
            time.sleep(180)


# Convenience functions for calling LLMs

def call_llm_hyde(messages: List[Dict], max_tokens: int = 500, no_cache=False) -> \
        Union[str, T]:
    options = LLMOptions(no_cache=no_cache)
    return call_llms(
        [LLMModel.GROQ_LLAMA3_2_3B, LLMModel.DEEPINFRA_LLAMA3_2_3B], messages, max_tokens,
        options=options)


def call_llm_score(messages: List[Dict],  min:int=0,  max:int=100,  max_tokens: int = 10, no_cache=False) -> \
        int:
    options = LLMOptions(no_cache=no_cache, eval=lambda x: min <= int(re.sub(r"[^0-9]", "", cast(str,x))) <= max)
    answer:str= call_llms_str([LLMModel.NORMAL_CHEAP], messages, max_tokens,  options=options)
    cleaned_answer = re.sub(r"[^0-9]", "", answer)

    if cleaned_answer is not None:
        if len(cleaned_answer) != 0:
            return int(cleaned_answer)
    raise EvalError(f"Failed to evaluate score from {answer}")


def call_llm_boolean(models: list[LLMModel], messages: List[Dict], no_cache=False,
                     boolean_text: tuple[str, str] = ("no", "yes"), metadata: Optional[dict] = None,
                     escalate_to: Optional[list[LLMModel]] = None, cache_key: Optional[str] = None, auto_distil:Optional[str]=None) -> \
        bool:
    options = LLMOptions(
        no_cache=no_cache,
        cache_key=cache_key,
        metadata=metadata,
        escalate_to=escalate_to,
        auto_distil=auto_distil,
        eval=lambda x: x is not None and (len(re.findall(rf"\b{boolean_text[1]}\b", x.lower())) == 1 or len(
            re.findall(rf"\b{boolean_text[0]}\b", x.lower())) == 1)
    )
    response = call_llms(
        models, messages, 10,
        response_model=None, options=options)
    if response and len(re.findall(rf"\b{boolean_text[1]}\b", response.lower())) == 1:
        return True
    return False


class ToolsError(ValueError):
    pass


def call_llms_tools(llms: List[LLMModel], messages: List[Dict], tools: List[Dict], max_tokens: int = 4000,
                    response_model: Union[Type[T], None] = None,
                    options: LLMOptions = LLMOptions()) -> Union[Any, T]:
    """
    Call LLMs with tool definitions (function calling) and execute the full tool loop

    This provides similar behavior to call_llms but specifically for LLM tool use, executing
    the complete interaction cycle of:
    1. Sending the prompt to the LLM with available tools
    2. LLM decides whether to call a tool or respond directly
    3. If tool is called, the implementation is executed
    4. Result is sent back to the LLM
    5. Steps 2-4 repeat until LLM gives final answer or max iterations reached

    Args:
        llms: List of LLM models to try (will try in order until success)
        messages: List of message dictionaries
        tools: List of tool definitions including 'implementation' callables
               Each tool should include standard OpenAI/Claude tool definition plus
               an 'implementation' key with a function to execute
        max_tokens: Maximum tokens in the response
        auto_rate_limit: Whether to automatically rate limit
        response_model: Optional Pydantic model to validate and convert the response
        eval: Optional function to evaluate the response
        eval_retry: Number of times to retry if eval fails
        no_cache: Whether to bypass the cache
        temperature: Temperature for sampling (0.0-1.0)
        cache_key: Explicit cache key (optional)
        use_either_key: Whether to use either the explicit or hash key for cache lookup
        append_on_eval_fail: Whether to append the failed response to message history on retry
        eval_retry_message: Custom message to send on eval failure retry
        metadata: Additional metadata to include with the request
        escalate_to: Models to escalate to if original models fail
        cache_prefix: Prefix for cache key (optional)
        streaming: Whether to use streaming mode for the request (recommended for long operations)

    Returns:
        The model's final response after all tool interactions, optionally validated as a Pydantic model
    """
    start = datetime.now()
    _messages = messages.copy()

    # Extract parameters from options
    auto_rate_limit = options.auto_rate_limit
    eval = options.eval
    eval_retry = options.eval_retry
    no_cache = options.no_cache
    temperature = options.temperature
    cache_key = options.cache_key
    use_either_key = options.use_either_key
    append_on_eval_fail = options.append_on_eval_fail
    eval_retry_message = options.eval_retry_message
    metadata = options.metadata
    escalate_to = options.escalate_to
    cache_prefix = options.cache_prefix
    streaming = options.streaming

    if temperature > 1.0:
        raise ValueError("Temperature must be less than or equal to 1.0")
    if temperature < 0 and temperature != -1:
        raise ValueError("Temperature must be -1 or 0..1")

    count = 0
    eval_result = False
    result = None
    escalated = False

    try:
        source = str(inspect.getsource(eval)) if eval else "<no source>"
    except Exception as e2:
        logger.warning(f"Failed to get source for eval. {e2}")
        source = "<no source>"

    while count < eval_retry:
        count += 1

        if count - 1 >= eval_retry / 2:
            logger.warning(f"Escalated to {escalate_to}")
            escalated = True
        try:
            result = _call_llms_tools_internal(
                llms if not escalated or escalate_to is None else escalate_to,
                _messages, tools, max_tokens,
                auto_rate_limit, response_model,
                no_cache=no_cache if count - 1 == 0 else True,
                temperature=temperature,
                cache_key=cache_key,
                use_either_key=use_either_key,
                cache_prefix=cache_prefix,
                retry=count - 1 > 0,
                append_on_eval_fail=append_on_eval_fail,
                eval_failure_result=result,
                eval_retry_message=eval_retry_message,
                metadata=metadata,
                escalate_to=escalate_to,
                streaming=streaming
            )

            if eval:
                try:
                    eval_result = eval(result)
                except Exception as e:
                    traceback.print_exc()
                    logger.error(f"Eval failed for {source or ''} -> {e}")
                    logger.exception(e)
                    eval_result = False
                    errorString = str(f"Eval failed for {source or ''} -> {e}")
                    raise EvalError(errorString)
                if type(eval_result) == str:
                    eval_retry_message = eval_result
                elif eval_result:
                    return result
                logger.warning(f"Eval failed '{eval_result}' for {source or ''} -> {str(result)[:600]} ")
                if count == eval_retry:
                    if type(eval_result) == str:
                        errorString = str(f"Eval failed for {source or ''} -> {eval_result} after {count} retries")
                        raise EvalError(errorString)
                    else:
                        if not eval_result:
                            errorString = str(f"Eval failed for {source or ''} -> {result} after {count} retries")
                            raise EvalError(errorString)
                        return result
            else:
                logger.info(str(result)[:200] + "..." if isinstance(result, str) and len(str(result)) > 200 else result)
                return result
        except ValidationError as e:
            logger.warning(f"Validation Error for {source or ''} -> {e}")
            escalated = True
        except instructor.exceptions.IncompleteOutputException as ie:
            logger.warning(f"IncompleteOutputException for {source or ''} -> {ie} retrying ...")
            escalated = True
            eval_retry_message = f"The token length of your response was too long, please try again, token limit is {max_tokens}."

        logger.warning(f"Retrying {_messages[-1]['content'][:600]}")
        temperature = temperature + 0.1 if temperature < 0.9 else 0.9


def _call_llms_tools_internal(llms: List[LLMModel], messages: List[Dict], tools: List[Dict], max_tokens: int = 4000,
                             auto_rate_limit: bool = True, response_model: Union[Type[T], None] = None,
                             no_cache: bool = False, temperature=0.0, cache_key: Union[str, None] = None,
                             use_either_key=True, retry=False, append_on_eval_fail=True,
                             eval_failure_result=None, eval_retry_message=None, metadata: dict = None,
                             cache_prefix: str = None,
                             escalate_to: Union[None, List[LLMModel]] = None,
                             streaming: bool = False) -> Union[Any, T]:
    """Internal implementation of call_llms_tools with caching and retry logic"""
    start = datetime.now()

    # Create a copy of tools without implementation for caching
    cache_tools_json = copy.deepcopy(tools)
    for x in cache_tools_json:
        if 'implementation' in x:
            x.pop('implementation', None)

    # Cache management - construct hash from messages and tools
    to_hash = re.sub(r"\s+", " ", json.dumps(messages) + ":" +
                    json.dumps(cache_tools_json) + ":" +
                    ("" if response_model is None else response_model.schema_json()))

    # Create cache keys
    key1 = md5_hash_str("tools:1.0:" + cache_key) if cache_key is not None else None
    if cache_prefix is not None and key1 is not None:
        key1 = cache_prefix + ":" + key1
    key2 = ("tools:1.3:" + (md5_hash_str(to_hash)) + (":text" if response_model is None else ":json"))
    if cache_prefix is not None:
        key2 = cache_prefix + ":" + key2

    # Check cache
    value1 = new_cache.get(key1) if key1 is not None else None
    value2 = new_cache.get(key2)
    value = None
    store_key = None

    # Determine which cache key to use
    if use_either_key and cache_key is not None:
        value = value1 if value1 is not None else value2
        store_key = key1
    elif cache_key is not None:
        value = value1
        store_key = key1
    else:
        value = value2
        store_key = key2

    # This is a retry, so alter the message but not the cache key
    if retry:
        # Deep copy the messages so we don't alter the original
        messages = json.loads(json.dumps(messages))

        if append_on_eval_fail:
            # When retrying, we can add the failed response to the history
            if eval_failure_result is not None:
                content = None
                if hasattr(eval_failure_result, 'content'):
                    content = eval_failure_result.content
                elif hasattr(eval_failure_result, 'model_dump'):
                    content = json.dumps(eval_failure_result.model_dump())
                elif isinstance(eval_failure_result, str):
                    content = eval_failure_result
                elif eval_failure_result is not None:
                    content = json.dumps(eval_failure_result)

                if content:
                    messages.append({"role": "assistant", "content": content})

            # Add retry message
            if eval_retry_message is not None:
                messages.append({"role": "user", "content": eval_retry_message})
            else:
                try:
                    getsource = str(inspect.getsource(eval))
                except Exception as e2:
                    logger.warning(f"Failed to get source for eval. {e2}")
                    getsource = "<no source>"
                messages.append({"role": "user",
                                 "content": f"My eval ```python\n{getsource}``` returned False for your response, it must evaluate to True. Could you try again. "})
        else:
            if eval_retry_message is not None:
                messages.append({"role": "user", "content": eval_retry_message})
            else:
                original_main_content = messages[-1]['content']
                messages[-1]["content"] = f"{original_main_content} (This was retried at {datetime.now().strftime('%H:%M:%S')} please follow the instructions carefully.)"

    # Return cached value if available and cache isn't bypassed
    if value is not None and not no_cache and not retry:
        new_cache.set(store_key, value, expire=86400 * 30 * 3)
        logger.info("Cache Hit")

        # Handle Pydantic model validation for cached results
        if response_model is None:
            return json.loads(value) if not isinstance(value, str) or value.startswith("{") else value
        else:
            try:
                response_model.model_validate_json(value)  # pyright: ignore [reportArgumentType]
                return response_model.model_validate_json(value)  # pyright: ignore [reportArgumentType]
            except ValidationError:
                if new_cache.get(key1):
                    new_cache.delete(key1)
                if new_cache.get(key2):
                    new_cache.delete(key2)
                logger.warning("Schema changed so cached entry is invalid")

    logger.info("Cache Miss")

    # Try each model in order, with retry/fallback logic
    while True:
        recoverable_error = False

        for llm in llms:
            if rate_limited[llm] and rate_limited[llm] - time.time() > 0:
                logger.info(f"{llm} is rate limited for {rate_limited[llm] - time.time()} seconds")
                continue

            rate_limited[llm] = 0

            try:
                provider = provider_map[llm.value.provider]

                try:
                    t = time.time()

                    # Call the provider with tools, using streaming if requested and supported
                    if streaming and hasattr(provider, 'call_chat_with_tools_streaming'):
                        result = provider.call_chat_with_tools_streaming(
                            llm, messages, tools, max_tokens, temperature, metadata or {}
                        )
                    else:
                        if streaming:
                            logger.warning(f"Streaming requested but provider {llm.value.provider} doesn't support it. Using non-streaming call.")
                        result = provider.call_chat_with_tools(
                            llm, messages, tools, max_tokens, temperature, metadata or {}
                        )

                    # Handle Pydantic model validation if specified
                    if response_model is not None:
                        try:
                            # Try to validate and convert to the Pydantic model
                            validated_result = response_model.model_validate_json(clean_llm_json_response(result))
                            result = validated_result
                        except ValidationError as e:
                            logger.warning(f"Validation Error: {e}")
                            raise e

                    # Cache the result

                    new_cache.set(store_key, result, expire=86400 * 7)

                    # Rate limit if slow response
                    if (time.time() - t) > 10 and auto_rate_limit:
                        logger.info(f"Time taken for {llm.value.name} call: {time.time() - t} auto rate limiting {llm.value.name}")
                        rate_limited[llm] = time.time() + 120
                    else:
                        logger.info(f"Time taken for {llm.value.name} call: {time.time() - t}")

                    return result

                except NotImplementedError as ne:
                    logger.warning(f"Provider {llm.value.provider} does not support tool calls: {ne}")
                    if llm == llms[-1] and (escalate_to is None or not escalate_to):
                        raise ToolsError("No providers support tool calls for the provided models")
                    continue
                except litellm.BadRequestError as e:
                    logger.error(f"Error calling LiteLLM: {e}")
                    if e._cause__ is not None:
                        raise e.__cause__
                    if e.status_code == 429:
                        logger.info(f"******** RATE LIMIT HIT FOR {llm.value.name} ********")
                        rate_limited[llm] = time.time() + 240
                        continue
                    elif "credit balance" in e.message:
                        logger.info("******** OUT OF CREDITS FOR {llm.value.name} PLEASE GET MORE! ********")
                        rate_limited[llm] = time.time() + 180
                        continue
                    else:
                        raise e
            except ResponseError as e:
                logger.info(e)
                continue
            except groq.RateLimitError as e:
                logger.info(e)
                if "daily rate limit" in e.body["error"]["message"].lower():
                    logger.info("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except anthropic.RateLimitError as e:
                logger.info(e)
                if "daily rate limit" in e.body["error"]["message"].lower():
                    logger.info("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                elif "credit balance is too low" in e.body["error"]["message"]:
                    logger.info("******** CLAUDE QUOTA EXCEEDED ********")
                    rate_limited[llm] = time.time() + 240
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except anthropic.InternalServerError as e:
                logger.error(f"Error calling anthropic: {e}")
                logger.exception(e)
                recoverable_error = True
            except anthropic.BadRequestError as e:
                if e.status_code == 429:
                    logger.info("******** CLAUDE RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 240
                    continue
                elif "credit balance" in e.message:
                    logger.info("******** CLAUDE OUT OF CREDITS ********")
                    rate_limited[llm] = time.time() + 240
                    continue
                else:
                    raise e
            except openai.RateLimitError as e:
                logger.info(e)
                msg = e.body
                if isinstance(msg, str):
                    msg = json.loads(msg)
                if "daily rate limit" in msg["message"].lower():
                    logger.info("******** DAILY RATE LIMIT ********")
                    rate_limited[llm] = time.time() + 86400
                elif " exceeded your current quota" in msg["message"]:
                    logger.info("******** OPENAI QUOTA EXCEEDED ********")
                    rate_limited[llm] = time.time() + 240
                else:
                    rate_limited[llm] = time.time() + 120
                continue
            except Exception as e:
                logger.error(e)
                traceback.print_exc()
                if llm == llms[-1]:
                    # Try escalation models if provided
                    if escalate_to:
                        logger.info(f"Escalating to {escalate_to}")
                        return _call_llms_tools_internal(
                            escalate_to, messages, tools, max_tokens, auto_rate_limit, response_model,
                            True, temperature, cache_key, use_either_key, retry, append_on_eval_fail,
                            eval_failure_result, eval_retry_message, metadata, cache_prefix,
                            max_tool_iterations, None
                        )
                    raise e

        if recoverable_error:
            logger.info("Recoverable error, sleeping for 20 seconds then will try again.")
            time.sleep(20)
        else:
            # Try escalation if all models failed
            if escalate_to:
                logger.info(f"All models failed. Escalating to {escalate_to}")
                return _call_llms_tools_internal(
                    escalate_to, messages, tools, max_tokens, auto_rate_limit, response_model,
                    True, temperature, cache_key, use_either_key, retry, append_on_eval_fail,
                    eval_failure_result, eval_retry_message, metadata, cache_prefix,
                    max_tool_iterations, None
                )

            logger.info("NO LLMs available, sleeping for 3 minutes then will try again.")
            time.sleep(180)


def call_llms_tools_multiple(llms: List[LLMModel], messages: List[Dict], tools: List[Dict], max_tokens: int = 4000,
                            response_model: Union[Type[T], None] = None,
                            options: LLMOptions = LLMOptions(), max_tool_iterations: int = 5) -> List[Union[Any, T]]:
    """
    Call multiple LLMs with tool calls in parallel.

    Args:
        llms: List of LLM models to use (each will be called independently)
        messages: List of message dictionaries
        tools: List of tool definitions including 'implementation' callables
        max_tokens: Maximum tokens in the response
        auto_rate_limit: Whether to enable automatic rate limiting
        response_model: Optional Pydantic model to validate and convert responses
        eval: Optional function to evaluate responses
        eval_retry: Number of times to retry if eval fails
        no_cache: Whether to bypass the cache
        temperature: Temperature for sampling
        cache_key: Explicit cache key (optional)
        use_either_key: Whether to use either the explicit or hash key for cache lookup
        append_on_eval_fail: Whether to append the failed responses to message history on retry
        eval_retry_message: Custom message to send on eval failure retry
        metadata: Additional metadata
        escalate_to: Models to escalate to if any model fails
        max_tool_iterations: Maximum number of back-and-forth tool interactions

    Returns:
        List of responses from each model, optionally validated as Pydantic models
    """
    with ThreadPoolExecutor(max_workers=len(llms)) as executor:
        futures = []
        result = []

        for i, llm in enumerate(llms):
            # Create options with cache_prefix for this specific LLM
            llm_options = options.model_copy(update={'cache_prefix': str(i)})
            futures.append(
                executor.submit(
                    call_llms_tools,
                    [llm],
                    messages,
                    tools,
                    max_tokens,
                    response_model,
                    llm_options
                )
            )

        for future in futures:
            try:
                result.append(future.result())
            except Exception as e:
                logger.error(f"Error in call_llms_tools_multiple: {str(e)}")
                traceback.print_exc()
                continue

        return result


# Convenience functions for tool calling
def call_tools_claude(messages: List[Dict], tools: List[Dict], max_tokens: int = 4000,
                     response_model: Union[Type[T], None] = None,
                     options: LLMOptions = LLMOptions(),
                     max_tool_iterations: int = 5) -> Union[Any, T]:
    """
    Convenience function to call Claude with tools

    Args:
        messages: List of message dictionaries
        tools: List of tool definitions including 'implementation' callables
        max_tokens: Maximum tokens in the response
        response_model: Optional Pydantic model to validate and convert the response
        options: LLMOptions instance containing all configuration
        max_tool_iterations: Maximum number of back-and-forth tool interactions

    Returns:
        Claude's final response after tool interactions, optionally validated as a Pydantic model
    """
    return call_llms_tools([LLMModel.CLAUDE_SONNET], messages, tools, max_tokens,
                           response_model=response_model, options=options)


def call_tools_gpt(messages: List[Dict], tools: List[Dict], max_tokens: int = 4000,
                  response_model: Union[Type[T], None] = None,
                  options: LLMOptions = LLMOptions(),
                  max_tool_iterations: int = 5) -> Union[Any, T]:
    """
    Convenience function to call GPT-4o with tools

    Args:
        messages: List of message dictionaries
        tools: List of tool definitions including 'implementation' callables
        max_tokens: Maximum tokens in the response
        response_model: Optional Pydantic model to validate and convert the response
        options: LLMOptions instance containing all configuration
        max_tool_iterations: Maximum number of back-and-forth tool interactions

    Returns:
        GPT's final response after tool interactions, optionally validated as a Pydantic model
    """
    return call_llms_tools([LLMModel.GPT_4O], messages, tools, max_tokens,
                           response_model=response_model, options=options)

import re

def clean_llm_json_response(text:str)->str:
    """
    Cleans LLM responses by removing code blocks and other superfluous formatting

    Args:
        response (str): The raw LLM response text

    Returns:
        str: The cleaned response
    """
    cleaned= remove_xml_tags(text)
    cleaned = re.sub("```", "", re.sub("^[^{]*\n", "", cleaned)).strip()
    cleaned_candidate=repair_json(cleaned)
    if cleaned_candidate:
        cleaned=cleaned_candidate
    print(cleaned)
    return cast(str,cleaned)

# Example usage
# raw_response = "Here's some text\n```json\n{\"key\": \"value\"}\n```\nMore text with `inline code` and <tags>unwanted</tags>"
# print(clean_llm_response(raw_response))
