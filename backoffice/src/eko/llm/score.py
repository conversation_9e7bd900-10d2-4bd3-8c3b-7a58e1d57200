import re

from loguru import logger

from eko.llm.main import call_llm_score


def score_text(text: str, score_prompt_function, **score_prompt_args):
    messages = score_prompt_function(text, **score_prompt_args)
    count = 0
    while count < 10:
        count = count + 1
        try:

            quality = call_llm_score(messages, no_cache=count > 1)
            if quality > 100 or quality < 0:
                logger.info("Quality out of range")
                continue

            logger.info(f"Score {quality}")
            return quality
        except Exception as e:
            logger.info("*********************** FAILED RETRYING ************************")
            logger.info(count, e)
    return None
