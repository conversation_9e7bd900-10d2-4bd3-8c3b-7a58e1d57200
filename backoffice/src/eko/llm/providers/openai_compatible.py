import instructor
import json
import traceback
from loguru import logger
from openai import OpenAI
from pydantic import BaseModel
from typing import cast, Union, Type, TypeVar, Dict, List, Any

from eko.llm import LLMModel
from eko.llm.providers.base import LLMProvider

T=TypeVar('T', bound=BaseModel)
class OpenAICompatibleProvider(LLMProvider):

    def __init__(self, api_key: str, base_url: str, use_instructor: bool = True, mode=instructor.Mode.TOOLS) -> object:

        if use_instructor:
                self.openai_client = instructor.from_openai(OpenAI(api_key=api_key, base_url=base_url),mode)
        else:
            self.openai_client = OpenAI(api_key=api_key, base_url=base_url)
        self.no_instructor_openai_client = OpenAI(api_key=api_key, base_url=base_url)
        super().__init__()

    def call_chat(self, llm: LLMModel,  messages: list[Dict], max_tokens: int = 4000,
                  response_model: Union[None, Type[T]] = None, temperature=0.0, metadata: dict = {}) -> Union[
        str, T]:
        try:
            messages = self.remove_cache_control(messages)
            if response_model is None:
                response = self.no_instructor_openai_client.chat.completions.create(
                    model=llm.value.name,
                    messages=messages,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )
                if response.choices is None:
                    logger.error(f"No content in response for {llm.value.name}: {response}")
                    raise ValueError("No content in response")
                answer = response.choices[0].message.content
                logger.debug(f"Text based answer {answer} because response model was None")
                self.record_cost( llm, messages, answer, response)
                return cast(str, answer)
            else:
                response = self.openai_client.chat.completions.create_with_completion(
                    model=llm.value.name,
                    messages=messages,  # pyright: ignore [reportArgumentType]
                    response_model=response_model,
                    max_tokens=max_tokens,
                    temperature=temperature,
                )
                self.record_cost(llm, messages, response[0], response[1])
                logger.info(f"The response was {response}")
                return cast(T, response[0])
        except Exception as e:
            logger.exception(f"Error calling OpenAI compatible provider: {e}")
            traceback.print_stack()
            raise
            
    def call_chat_with_tools(self, llm: LLMModel, messages: list[Dict], tools: List[Dict], 
                          max_tokens: int = 4000, temperature=0.0, metadata: dict = {}) -> Any:
        """
        Implementation of tool/function calling for OpenAI compatible API models with full tool loop
        
        Args:
            llm: The LLM model to use
            messages: List of message dictionaries
            tools: List of tool definitions in OpenAI format with 'implementation' callables
            max_tokens: Maximum tokens in the response
            temperature: Temperature for sampling
            metadata: Additional metadata
            max_tool_iterations: Maximum number of back-and-forth tool calling iterations
            
        Returns:
            The model's final response after tool interactions
        """
        from loguru import logger
        messages = self.remove_cache_control(messages)
        
        # Extract tool definitions and implementations
        api_tools, implementations = self.prepare_tools_for_api(tools)
        
        # Start the conversation loop
        iteration = 0
        current_messages = messages.copy()
        
        max_tool_iterations: int = 25
        # Some compatible APIs may not support tools in the expected format
        try:
            while iteration < max_tool_iterations:
                iteration += 1
                logger.debug(f"Tool iteration {iteration}/{max_tool_iterations}")
                
                # Call the model
                response = self.no_instructor_openai_client.chat.completions.create(
                    model=llm.value.name,
                    messages=current_messages,
                    max_tokens=max_tokens,
                    tools=api_tools,
                    temperature=temperature,
                    tool_choice="auto",
                )
                
                # Get the message
                assistant_message = response.choices[0].message
                current_messages.append(assistant_message.model_dump())
                
                # If there are no tool calls, or model wants to respond directly, we're done
                if not hasattr(assistant_message, 'tool_calls') or not assistant_message.tool_calls:
                    logger.debug("No tool calls in response - conversation complete")
                    self.record_cost(llm, messages, assistant_message, response)
                    logger.warning(assistant_message)
                    return assistant_message.content
                
                # Process each tool call
                for tool_call in assistant_message.tool_calls:
                    try:
                        # Convert OpenAI tool_call to dict format for our executor
                        tool_call_dict = {
                            "id": tool_call.id,
                            "function": {
                                "name": tool_call.function.name,
                                "arguments": tool_call.function.arguments
                            }
                        }
                        
                        # Execute the tool
                        tool_result = self.execute_tool_call(tool_call_dict, implementations)
                        
                        # Add tool result to messages
                        current_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "name": tool_call.function.name,
                            "content": json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result
                        })
                        
                        logger.debug(f"Executed tool {tool_call.function.name}")
                    except Exception as e:
                        # Add error as tool result
                        current_messages.append({
                            "role": "tool",
                            "tool_call_id": tool_call.id,
                            "name": tool_call.function.name,
                            "content": f"Error: {str(e)}"
                        })
                        logger.exception(f"Error executing tool {tool_call.function.name}: {e}")
                
                # Continue the conversation
            
            # If we've reached max iterations, get a final response
            final_response = self.no_instructor_openai_client.chat.completions.create(
                model=llm.value.name,
                messages=current_messages,
                max_tokens=max_tokens,
                temperature=temperature,
            )
            
            result = final_response.choices[0].message.content
            self.record_cost(llm, messages, result, final_response)
            return result
            
        except Exception as e:
            logger.warning(f"Error using tools with OpenAI compatible provider: {e}")
            raise NotImplementedError(f"This OpenAI compatible provider does not support tool calls: {str(e)}")
