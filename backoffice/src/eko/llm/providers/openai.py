import json
from typing import cast, Union, Type, TypeVar, Dict, List, Any

import instructor
from loguru import logger
from openai import OpenAI
from pydantic import BaseModel

from eko.llm import LLMModel
from eko.llm.providers.base import LLMProvider

T=TypeVar('T', bound=BaseModel)
class OpenAIProvider(LLMProvider):
    openai_client = instructor.from_openai(OpenAI())
    openai_client_raw = OpenAI()

    def call_chat(self, llm: LLMModel, messages: list[Dict], max_tokens: int = 4000,
                  response_model: Union[None, Type[T]] = None, temperature=0.0, metadata: dict = {}) -> Union[
        str, T]:

        store_for_distilation = (llm == LLMModel.GPT_4O)
        messages = self.remove_cache_control(messages)
        if response_model is None:
            response = OpenAIProvider.openai_client.chat.completions.create(  # pyright: ignore [reportCallIssue]
                model=llm.value.name,
                messages=messages,  # pyright: ignore [reportArgumentType]
                max_tokens=max_tokens,
                response_model=None,
                seed=1,
                temperature=temperature,
                store=store_for_distilation,
                metadata=metadata if store_for_distilation else None
            )
            answer = response.choices[0].message.content
            logger.debug(f"Text based answer {answer} because response model was None")
            self.record_cost(llm, messages, answer, response)
            return cast(str, answer)
        else:
            response = OpenAIProvider.openai_client.chat.completions.create_with_completion(
                model=llm.value.name,
                messages=messages,  # pyright: ignore [reportArgumentType]
                response_model=response_model,
                max_tokens=max_tokens,
                seed=1,
                temperature=temperature,
                store=store_for_distilation,
                metadata=metadata if store_for_distilation else None
            )
            self.record_cost(llm, messages, response[0], response[1])
            logger.debug(f"The response was {response[0]}")
            return cast(T, response[0])
            
    def call_chat_with_tools(self, llm: LLMModel, messages: list[Dict], tools: List[Dict], 
                          max_tokens: int = 4000, temperature=0.0, metadata: dict = {}) -> Any:
        """
        Implementation of tool/function calling for OpenAI models, with full tool loop
        
        Args:
            llm: The LLM model to use
            messages: List of message dictionaries
            tools: List of tool definitions in OpenAI format with 'implementation' callables
            max_tokens: Maximum tokens in the response
            temperature: Temperature for sampling
            metadata: Additional metadata
            max_tool_iterations: Maximum number of back-and-forth tool calling iterations
            
        Returns:
            The model's final response after tool interactions
        """
        store_for_distilation = (llm == LLMModel.GPT_4O)
        messages = self.remove_cache_control(messages)

        # Extract tool definitions and implementations
        api_tools, implementations = self.prepare_tools_for_api(tools)

        # Start the conversation loop
        iteration = 0
        current_messages = messages.copy()
        max_tool_iterations: int = 25
        while iteration < max_tool_iterations:
            iteration += 1
            logger.debug(f"Tool iteration {iteration}/{max_tool_iterations}")
            
            # Call the model
            response = OpenAIProvider.openai_client_raw.chat.completions.create(
                model=llm.value.name,
                messages=current_messages,
                max_tokens=max_tokens,
                tools=api_tools,
                temperature=temperature,
                seed=1,
                tool_choice="auto",
                store=store_for_distilation,
                metadata=metadata if store_for_distilation else None
            )
            
            # Get the message
            assistant_message = response.choices[0].message
            current_messages.append(assistant_message.model_dump())
            
            # If there are no tool calls, or model wants to respond directly, we're done
            if not assistant_message.tool_calls:
                logger.debug("No tool calls in response - conversation complete")
                self.record_cost(llm, messages, assistant_message, response)
                return assistant_message.content
            
            # Process each tool call
            for tool_call in assistant_message.tool_calls:
                try:
                    # Convert OpenAI tool_call to dict format for our executor
                    tool_call_dict = {
                        "id": tool_call.id,
                        "function": {
                            "name": tool_call.function.name,
                            "arguments": tool_call.function.arguments
                        }
                    }
                    
                    # Execute the tool
                    tool_result = self.execute_tool_call(tool_call_dict, implementations)
                    
                    # Add tool result to messages
                    current_messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": tool_call.function.name,
                        "content": json.dumps(tool_result) if not isinstance(tool_result, str) else tool_result
                    })
                    
                    logger.debug(f"Executed tool {tool_call.function.name}")
                except Exception as e:
                    # Add error as tool result
                    current_messages.append({
                        "role": "tool",
                        "tool_call_id": tool_call.id,
                        "name": tool_call.function.name,
                        "content": f"Error: {str(e)}"
                    })
                    logger.exception(f"Error executing tool {tool_call.function.name}: {e}")
            
            # Continue the conversation
        
        # If we've reached max iterations, get a final response
        final_response = OpenAIProvider.openai_client_raw.chat.completions.create(
            model=llm.value.name,
            messages=current_messages,
            max_tokens=max_tokens,
            temperature=temperature,
            seed=1,
            store=store_for_distilation,
            metadata=metadata if store_for_distilation else None
        )
        
        result = final_response.choices[0].message.content
        self.record_cost(llm, messages, result, final_response)
        return result
