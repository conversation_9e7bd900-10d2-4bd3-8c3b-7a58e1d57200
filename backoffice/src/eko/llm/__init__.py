from functools import lru_cache

import litellm
import tiktoken
from enum import Enum
from pydantic import BaseModel
from typing import Union, Tuple

litellm.model_cost = {
    "gemini/2877576961029308416": {
        "input_cost_per_token": 0.075/1_000_000,  # Use same pricing as base model
        "output_cost_per_token": 0.3/1_000_000,  # Use same pricing as base model
        "context_window": 1000000  # Set appropriate context window
    },
    "gemini/4972735950175076352": {
        "input_cost_per_token": 0.10/1_000_000,  # Use same pricing as base model
        "output_cost_per_token": 0.4/1_000_000,  # Use same pricing as base model
        "context_window": 1000000  # Set appropriate context window
    },
    **litellm.model_cost  # Include existing models
}
litellm.set_verbose = True

class LLMModelClass:
    def __init__(
        self,
        name: str,
        token_pricing_per_million: Tuple[float, float],
        context_window: int,
        provider: str,
        base_model: str = None,
    ):
        self.name = name
        self.token_pricing_per_million = token_pricing_per_million
        self.context_window = context_window
        self.provider=provider
        self.base_model= base_model

class LLMModel(Enum):
    GPT_4O = LLMModelClass("gpt-4o", (2.5, 10), 128_000, "gpt")
    GPT_4O_MINI = LLMModelClass("gpt-4o-mini", (0.15, 0.6), 128_000, "gpt")
    GPT_4O_MINI_FT_FLAGS = LLMModelClass("ft:gpt-4o-mini-2024-07-18:eko-intelligence:flag-experiment-2:AaSF6mN2", (0.15, 0.6), 128_000, "gpt")
    GPT_4O_MINI_FT_TSQUERY = LLMModelClass("ft:gpt-4o-mini-2024-07-18:eko-intelligence:counter-claim-experiment-1:Ab9xYQ9A", (0.15, 0.6), 128_000, "gpt")
    O1_MINI = LLMModelClass("o1-mini", (3, 12), 200_000, "o1")
    O3_MINI = LLMModelClass("o3-mini", (1.10, 4.4), 200_000, "o1")
    O1 = LLMModelClass("o1-2024-12-17", (15, 60), 200_000, "o1")

    GROQ_LLAMA3_2_3B = LLMModelClass("llama-3.2-3b-preview", (0.06, 0.06), 128_000, "groq")
    GROQ_LLAMA3_3_70B = LLMModelClass("llama-3.3-70b-versatile", (0.59, 0.69), 128_000, "groq")
    GROQ_R1_70B = LLMModelClass("DeepSeek-R1-Distill-Llama-70b", (0.59, 0.69), 128_000, "groq")

    # DEEPINFRA_NEMOTRON = LLMModelClass("nvidia/Llama-3.1-Nemotron-70B-Instruct", (0.35, 0.4), 128_000, "deepinfra")
    DEEPINFRA_LLAMA_3_3_70B_TURBO = LLMModelClass("meta-llama/Llama-3.3-70B-Instruct-Turbo", (0.13, 0.4), 128_000, "deepinfra")
    # DEEPINFRA_LLAMA_3_3_70B_INSTRUCT = LLMModelClass("meta-llama/Llama-3.3-70B-Instruct", (0.23, 0.4), 128_000, "deepinfra")
    DEEPINFRA_LLAMA3_2_3B = LLMModelClass("meta-llama/Llama-3.2-3B-Instruct", (0.03, 0.055), 128_000, "deepinfra")
    DEEPINFRA_QWEN_CODER_32B = LLMModelClass("Qwen/Qwen2.5-Coder-32B-Instruct", (0.08, 0.18), 32_000, "deepinfra")
    DEEPINFRA_QWEN_72B = LLMModelClass("Qwen/Qwen2.5-72B-Instruct", (0.35, 0.4), 32_000, "deepinfra")
    DEEPINFRA_QWQ_72B = LLMModelClass("Qwen/QwQ-32B-Preview", (0.15, 0.6), 32_000, "deepinfra")
    DEEPINFRA_QWEN_2_5_7B = LLMModelClass("Qwen/Qwen2.5-7B-Instruct", (0.025, 0.05), 32_000, "deepinfra")
    DEEPINFRA_DEEPSEEK_CHAT = LLMModelClass("deepseek-ai/DeepSeek-V3", (0.85, 0.90), 16_000, "deepinfra")
    DEEPINFRA_MISTRAL_SMALL = LLMModelClass("mistralai/Mistral-Small-24B-Instruct-2501", (0.07, 0.14), 16_000, "deepinfra")


    DEEPSEEK_CHAT = LLMModelClass("deepseek-chat", (0.27, 1.10), 64_000, "deepseek")
    DEEPSEEK_REASON = LLMModelClass("deepseek-reasoner", (0.27, 2.10), 64_000, "deepseek")

    # HYPERBOLIC_LLAMA_3_3_70B = LLMModelClass("meta-llama/Llama-3.3-70B-Instruct", (0.4, 0.4), 128_000, "hyperbolic")

    CLAUDE_SONNET = LLMModelClass("claude-3-7-sonnet-latest", (3, 15), 200_000, "anthropic")
    CLAUDE_HAIKU = LLMModelClass("claude-3-5-haiku-20241022", (1, 5), 200_000, "anthropic")

    LOCAL_LLAMA = LLMModelClass("llama3.2", (0.0, 0.0), 8000, "ollama")
    LOCAL_QWEN_LONG = LLMModelClass("huihui_ai/qwen2.5-1m-abliterated:14b", (0.0, 0.0), 8000, "ollama")
    LOCAL_R1 = LLMModelClass("deepseek-r1:70b", (0.0, 0.0), 8000, "ollama")
    LOCAL_MISTRAL_SMALL = LLMModelClass("mistral-small:24b", (0.0, 0.0), 32000, "ollama")
    LOCAL_QWEN_2_5_14B = LLMModelClass("qwen2.5:14b", (0.0, 0.0), 32000, "ollama")
    OUR_MODEL = LLMModelClass("eko-mistral-small", (0.0, 0.0), 32000, "ollama")

    RUNPOD = LLMModelClass("mistralai/Mistral-Small-24B-Base-2501", (0.0, 0.0), 32000, "runpod")

    GEMINI_FLASH= LLMModelClass("gemini-2.0-flash-001", (0.10, 0.4), 1_000_000, "gemini")
    GEMINI_FLASH_LITE= LLMModelClass("gemini-2.0-flash-lite", (0.075, 0.3), 1_000_000, "gemini")
    GEMINI_PRO = LLMModelClass("gemini-2.5-pro-preview-05-06", (2.5, 15.0), 1_000_000, "gemini")
    GEMINI_FLASH_NEXT = LLMModelClass("gemini-2.5-flash-preview-05-20", (0.15, 0.6), 1_000_000, "gemini")
    GEMINI_FLASH_LITE_FINETUNED = LLMModelClass(
        "1286610023916503040", (0.075, 0.3), 1_000_000, "gemini-finetuned", base_model="vertex_ai/gemini-2.0-flash-lite"
    )
    GEMINI_FLASH_FINETUNED = LLMModelClass(
        "8567804721467752448", (0.10, 0.4), 1_000_000, "gemini-finetuned", base_model="vertex_ai/gemini-2.0-flash-001"
    )

    REASONING_BEST=O1

    REASONING_CHEAPEST=DEEPSEEK_REASON

    LONG_CONTEXT_CHEAPEST=GEMINI_FLASH_LITE

    NORMAL_UHQ = GEMINI_FLASH_NEXT
    NORMAL_HQ = GEMINI_FLASH_NEXT
    NORMAL_HQ_ALT = GEMINI_FLASH_NEXT
    NORMAL_ALL_ROUNDER = GEMINI_FLASH_NEXT
    NORMAL_ALL_ROUNDER_ALT=DEEPSEEK_CHAT
    NORMAL_CHEAP=GEMINI_FLASH_LITE
    
    NORMAL_NEXT=GEMINI_FLASH_LITE

    NORMAL_FAST=GEMINI_FLASH_LITE

    SMALL_BEST=GEMINI_FLASH_LITE
    SMALL_CHEAP=GEMINI_FLASH_LITE
    SMALL_CHEAP_FAST=GEMINI_FLASH_LITE


    VERY_SMALL_ULTRA_FAST=GEMINI_FLASH_LITE
    VERY_SMALL_CHEAPEST=LOCAL_LLAMA
    VERY_SMALL_BEST=LOCAL_LLAMA


class RunContext(BaseModel):
    entity: Union[int, None]
    run_id: Union[int, None]
    run_type: Union[str, None]
    request_id: Union[int, None]


llm_run_context = RunContext(entity=None, run_id=None, run_type=None, request_id=None)

PROMPT_JSON= "You are not conversational you are an API, you just return JSON."
PROMPT_NOT_CONVERSATIONAL= "You are not conversational, you are an API, please only reply as requested with no commentary or additional information."
PROMPT_SUMMARIZER= "You are not conversational, you are an API, please only reply as requested with no commentary or additional information. Just the summary."

PROMPT_RESEARCHER= "You are an expert level researcher in the field of environmental, social and governance impact of companies and countries, you have access to a vast array of information on the behaviour of companies."
PROMPT_PRESERVE_CITATIONS= "Please preserve citations and quotes from the source where possible. Example citation is: Barclays has been profitting from global warming [^3468]."
PROMPT_CREATE_CITATIONS= 'Please include citations and quotes from the supplied source pages where possible. Citations should be in the format [^987645]. Where page_id is the page id from the supplied text (<page id="987645">). Example citation is: Barclays has been profiting from global warming [^3468].</li>'
PROMPT_KEEP_FACTS="Please keep all facts, quotes, time periods, locations and quantity information in your analysis."

# Select a tokenizer; you can use 'cl100k_base' as a general-purpose tokenizer
tokenizer = tiktoken.get_encoding("cl100k_base")

@lru_cache(maxsize=1000)
def token_count(text: str) -> int:
    return len(tokenizer.encode(text))

# Re-export functions from eko.llm.main to avoid circular imports
# These will be imported lazily to prevent circular imports
def call_llms(*args, **kwargs):
    from eko.llm.main import call_llms as _call_llms
    return _call_llms(*args, **kwargs)

def call_llms_str(*args, **kwargs):
    from eko.llm.main import call_llms_str as _call_llms_str
    return _call_llms_str(*args, **kwargs)

def call_multiple_llms(*args, **kwargs):
    from eko.llm.main import call_multiple_llms as _call_multiple_llms
    return _call_multiple_llms(*args, **kwargs)

def call_multiple_llms_with_vote(*args, **kwargs):
    from eko.llm.main import call_multiple_llms_with_vote as _call_multiple_llms_with_vote
    return _call_multiple_llms_with_vote(*args, **kwargs)

# Re-export EvalError
class EvalError(ValueError):
    pass
