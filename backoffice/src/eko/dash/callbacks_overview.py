"""
Pipeline overview callbacks for the Effect Analysis Pipeline dashboard.

This module provides callbacks for the pipeline overview section.
"""

import pandas as pd
import plotly.graph_objects as go
from dash.dependencies import Input, Output
from plotly.subplots import make_subplots

from eko.db import get_bo_conn


def register_overview_callbacks(app):
    """
    Register callbacks for the pipeline overview section.
    
    Args:
        app: The Dash application instance
    """
    # Update pipeline flow chart
    @app.callback(
        Output('pipeline-flow-chart', 'figure'),
        Input('date-range', 'start_date'),
        Input('date-range', 'end_date'),
        Input('run-filter', 'value')
    )
    def update_pipeline_flow(start_date, end_date, selected_runs):
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                # Build query with filters
                query = """
                    SELECT 
                        ar.id AS run_id,
                        ar.run_type,
                        pm.stage,
                        SUM(pm.count) AS count
                    FROM 
                        ana_runs ar
                    JOIN 
                        dash.trk_pipeline_metrics pm ON ar.id = pm.run_id
                    WHERE 
                        ar.created_at BETWEEN %s AND %s
                """
                
                params = [start_date, end_date]
                
                if selected_runs:
                    placeholders = ','.join(['%s'] * len(selected_runs))
                    query += f" AND ar.id IN ({placeholders})"
                    params.extend(selected_runs)
                
                query += """
                    GROUP BY 
                        ar.id, ar.run_type, pm.stage
                    ORDER BY 
                        ar.id, pm.stage
                """
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
        if not rows:
            # Return empty figure if no data
            return go.Figure().update_layout(title="No data available for selected filters")
        
        # Process data
        df = pd.DataFrame(rows, columns=['run_id', 'run_type', 'stage', 'count'])
        df_pivot = df.pivot(index=['run_id', 'run_type'], columns='stage', values='count').reset_index()
        
        # Ensure all pipeline stages exist
        stages = ['document_retrieved', 'page_extracted', 'chunk_created', 'statement_extracted', 
                'effect_created', 'effect_stored', 'effect_flag_created', 'effect_flag_stored']
        
        for stage in stages:
            if stage not in df_pivot.columns:
                df_pivot[stage] = 0
        
        # Create figure
        fig = go.Figure()
        
        # Add traces for each run
        for _, row in df_pivot.iterrows():
            run_label = f"Run {row['run_id']} ({row['run_type']})"
            values = [row.get(stage, 0) for stage in stages]
            
            fig.add_trace(go.Bar(
                x=stages,
                y=values,
                name=run_label,
                hovertemplate='%{x}: %{y}<extra></extra>'
            ))
        
        fig.update_layout(
            title="Pipeline Flow: Objects at Each Stage",
            xaxis_title="Pipeline Stage",
            yaxis_title="Count",
            barmode='group',
            legend_title="Analysis Runs",
            height=500
        )
        
        return fig

    # Update RED vs GREEN distribution
    @app.callback(
        Output('red-green-distribution', 'figure'),
        Input('date-range', 'start_date'),
        Input('date-range', 'end_date'),
        Input('run-filter', 'value')
    )
    def update_red_green_distribution(start_date, end_date, selected_runs):
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                # Build query with filters
                query = """
                    SELECT 
                        ar.id AS run_id,
                        pm.entity_name,
                        SUM(CASE WHEN pm.stage = 'effect_stored' THEN pm.red_count ELSE 0 END) AS red_effects,
                        SUM(CASE WHEN pm.stage = 'effect_stored' THEN pm.green_count ELSE 0 END) AS green_effects,
                        SUM(CASE WHEN pm.stage = 'effect_flag_stored' THEN pm.red_count ELSE 0 END) AS red_flags,
                        SUM(CASE WHEN pm.stage = 'effect_flag_stored' THEN pm.green_count ELSE 0 END) AS green_flags
                    FROM 
                        ana_runs ar
                    JOIN 
                        dash.trk_pipeline_metrics pm ON ar.id = pm.run_id
                    WHERE 
                        ar.created_at BETWEEN %s AND %s
                """
                
                params = [start_date, end_date]
                
                if selected_runs:
                    placeholders = ','.join(['%s'] * len(selected_runs))
                    query += f" AND ar.id IN ({placeholders})"
                    params.extend(selected_runs)
                
                query += """
                    GROUP BY 
                        ar.id, pm.entity_name
                    ORDER BY 
                        ar.id
                """
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
        if not rows:
            # Return empty figure if no data
            return go.Figure().update_layout(title="No data available for selected filters")
        
        # Process data
        df = pd.DataFrame(rows, columns=['run_id', 'entity_name', 'red_effects', 'green_effects', 'red_flags', 'green_flags'])
        
        # Create subplots: effects and flags
        fig = make_subplots(
            rows=1, cols=2,
            subplot_titles=("Effects: RED vs GREEN", "Flags: RED vs GREEN"),
            specs=[[{"type": "bar"}, {"type": "bar"}]]
        )
        
        # Group by entity and aggregate
        df_entity = df.groupby('entity_name').sum().reset_index()
        
        # Add RED effects
        fig.add_trace(
            go.Bar(
                x=df_entity['entity_name'],
                y=df_entity['red_effects'],
                name="RED Effects",
                marker_color='crimson'
            ),
            row=1, col=1
        )
        
        # Add GREEN effects
        fig.add_trace(
            go.Bar(
                x=df_entity['entity_name'],
                y=df_entity['green_effects'],
                name="GREEN Effects",
                marker_color='forestgreen'
            ),
            row=1, col=1
        )
        
        # Add RED flags
        fig.add_trace(
            go.Bar(
                x=df_entity['entity_name'],
                y=df_entity['red_flags'],
                name="RED Flags",
                marker_color='crimson'
            ),
            row=1, col=2
        )
        
        # Add GREEN flags
        fig.add_trace(
            go.Bar(
                x=df_entity['entity_name'],
                y=df_entity['green_flags'],
                name="GREEN Flags",
                marker_color='forestgreen'
            ),
            row=1, col=2
        )
        
        fig.update_layout(
            title="RED vs GREEN Distribution by Entity",
            barmode='group',
            height=500,
            legend_title="Effect Types"
        )
        
        return fig

    # Update pipeline stats table
    @app.callback(
        Output('pipeline-stats-table', 'data'),
        Output('pipeline-stats-table', 'columns'),
        Input('date-range', 'start_date'),
        Input('date-range', 'end_date'),
        Input('run-filter', 'value')
    )
    def update_pipeline_stats_table(start_date, end_date, selected_runs):
        with get_bo_conn() as conn:
            with conn.cursor() as cursor:
                # Build query with filters
                query = """
                    SELECT 
                        ar.id AS run_id,
                        pm.stage,
                        SUM(pm.count) AS total_count,
                        SUM(pm.red_count) AS red_count,
                        SUM(pm.green_count) AS green_count,
                        SUM(pm.total_processing_time_ms) AS processing_time_ms,
                        ROUND(SUM(pm.total_processing_time_ms) / NULLIF(SUM(pm.count), 0), 2) AS ms_per_item
                    FROM 
                        ana_runs ar
                    JOIN 
                        dash.trk_pipeline_metrics pm ON ar.id = pm.run_id
                    WHERE 
                        ar.created_at BETWEEN %s AND %s
                """
                
                params = [start_date, end_date]
                
                if selected_runs:
                    placeholders = ','.join(['%s'] * len(selected_runs))
                    query += f" AND ar.id IN ({placeholders})"
                    params.extend(selected_runs)
                
                query += """
                    GROUP BY 
                        ar.id, pm.stage
                    ORDER BY 
                        ar.id, pm.stage
                """
                
                cursor.execute(query, params)
                rows = cursor.fetchall()
                
        if not rows:
            # Return empty data if no results
            return [], []
        
        # Process data
        df = pd.DataFrame(rows, columns=['run_id', 'stage', 'total_count', 'red_count', 'green_count', 
                                        'processing_time_ms', 'ms_per_item'])
        
        # Format processing time
        df['processing_time'] = df['processing_time_ms'].apply(
            lambda x: f"{x / 1000:.2f}s" if x < 60000 else f"{x / 60000:.2f}min"
        )
        
        # Create table data
        data = df.to_dict('records')
        
        # Create columns
        columns = [
            {"name": "Run ID", "id": "run_id"},
            {"name": "Stage", "id": "stage"},
            {"name": "Total Count", "id": "total_count"},
            {"name": "RED Count", "id": "red_count"},
            {"name": "GREEN Count", "id": "green_count"},
            {"name": "Processing Time", "id": "processing_time"},
            {"name": "MS per Item", "id": "ms_per_item"}
        ]
        
        return data, columns