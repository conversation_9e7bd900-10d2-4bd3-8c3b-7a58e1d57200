"""
Graph dashboard application for the Effect Analysis Pipeline.

This module initializes the Dash application for the traceability graph dashboard.
"""

import dash
import dash_bootstrap_components as dbc
from dash import html, dcc

from eko.dash.callbacks_traceability import create_traceability_dashboard_layout


def create_graph_app():
    """
    Create and configure the Dash application for the traceability graph dashboard.

    Returns:
        dash.Dash: The configured Dash application
    """
    # Initialize the Dash app with a nice theme
    app = dash.Dash(__name__,
                   external_stylesheets=[dbc.themes.COSMO],
                   suppress_callback_exceptions=True)

    # Set the app title
    app.title = "Effect Analysis Traceability Graph"

    # Create the layout
    layout = html.Div([
        dcc.Location(id='url', refresh=False),
        dbc.Container([
            # Header
            html.H1("Effect Analysis Traceability Graph", className="my-4"),

            # Traceability graph content
            html.Div(create_traceability_dashboard_layout(id_prefix="tab-"), id="traceability-content"),

            # Store components for state management
            dcc.Store(id='tab-selected-run-store'),
        ], fluid=True)
    ])

    app.layout = layout

    return app
