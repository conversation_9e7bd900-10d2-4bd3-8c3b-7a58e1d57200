# Effect Analysis Pipeline Dashboard

This dashboard provides real-time monitoring and visualization of the Effect Analysis Pipeline. It helps track the progress and quality of analysis runs, effect clustering, and flag generation.

## Getting Started

### Prerequisites

Make sure you have installed the required packages:

```bash
pipenv install dash dash-bootstrap-components plotly
```

### Running the Dashboard

To start the dashboard, run the following command from the backoffice directory:

```bash
pipenv shell
cd src
python cli.py dash launch
```

Additional options:
- `--port`: Specify a custom port (default: 8050)
- `--debug`: Run in debug mode for development

Example:
```bash
python cli.py dash launch --port 8051 --debug
```

Then open your browser and navigate to: http://localhost:8050 (or the custom port if specified)

## Dashboard Features

The dashboard is divided into several sections:

### 1. Pipeline Overview
- Visualizes the count of objects at each stage of the pipeline
- Shows RED vs GREEN distribution by entity
- Provides detailed processing statistics

### 2. Clustering Metrics
- Shows clustering efficiency with bubble charts
- Displays statements per cluster by entity
- Visualizes silhouette scores for clustering quality

### 3. Effect Flag Quality
- Radar charts for impact, authenticity, contribution, and confidence
- Tracks percentage of merged flags
- Analyzes statements per flag distribution

### 4. Error Tracking
- Treemap visualization of errors by stage and type
- Detailed table of recent errors

### 5. Entity Performance Comparison
- Compares conversion rates across entities
- Displays average flags per run
- Visualizes RED vs GREEN flag ratio by entity

## Filtering Options

The dashboard provides several filtering options:
- Date range selector for time-based filtering
- Run ID selector for specific analysis runs

## Technical Details

The dashboard uses:
- Dash and Plotly for interactive visualizations
- Bootstrap components for layout
- SQL queries against the pipeline tracking tables

## Module Structure

The dashboard code is organized into the following modules:

```
eko/
└── dash/
    ├── __init__.py               # Package initialization
    ├── app.py                    # Main Dash application setup
    ├── dashboard.py              # Dashboard runner and startup
    ├── layout.py                 # Overall dashboard layout
    ├── callbacks.py              # Central callback registration
    ├── callbacks_filters.py      # Filter section callbacks
    ├── callbacks_overview.py     # Pipeline overview callbacks
    ├── callbacks_clustering.py   # Clustering metrics callbacks
    ├── callbacks_flags.py        # Flag quality callbacks
    ├── callbacks_errors.py       # Error tracking callbacks
    ├── callbacks_entities.py     # Entity performance callbacks
    └── components/               # UI components for each section
        ├── __init__.py
        ├── overview.py           # Pipeline overview components
        ├── clustering.py         # Clustering metrics components
        ├── flags.py              # Flag quality components
        ├── errors.py             # Error tracking components
        └── entities.py           # Entity performance components
```

## Database Tables

The dashboard visualizes data from these tables:
- `pipeline_stats`: Detailed statistics at each stage
- `pipeline_metrics`: Aggregated metrics by entity and stage
- `clustering_metrics`: Cluster quality and efficiency
- `vectorization_metrics`: Vector quality measurements
- `effect_flag_metrics`: Flag quality scores
- `pipeline_errors`: Error tracking information

## Future Enhancements

Planned enhancements include:
- Real-time updates without page refresh
- User-configurable dashboard layouts
- Additional metrics for model performance
- Export capabilities for reports