"""
Entity performance components for the Effect Analysis Pipeline dashboard.

This module provides components for visualizing entity-level performance metrics.
"""

import dash_bootstrap_components as dbc
from dash import html, dcc


def create_entity_section():
    """
    Create the entity performance section of the dashboard.
    
    Returns:
        dbc.Card: The entity performance section card
    """
    return dbc.Card([
        dbc.CardBody([
            html.H4("Entity Performance Comparison", className="card-title"),
            
            # Overall entity performance
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id='entity-performance-chart')
                ], width=12),
            ], className="mb-4"),
            
            # Detailed entity metrics
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id='flags-per-entity-chart')
                ], width=6),
                dbc.Col([
                    dcc.Graph(id='red-green-ratio-chart')
                ], width=6),
            ], className="mb-4"),
        ])
    ], className="mb-4")