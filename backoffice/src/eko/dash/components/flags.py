"""
Flag quality components for the Effect Analysis Pipeline dashboard.

This module provides components for visualizing flag quality metrics and enabling traceability.
"""

import dash_bootstrap_components as dbc
from dash import html, dcc


def create_flag_section():
    """
    Create the effect flag quality section of the dashboard.

    Returns:
        dbc.Card: The flag quality section card
    """
    return dbc.Card([
        dbc.CardBody([
            html.H4("Effect Flag Quality", className="card-title"),

            # Flag quality radar chart
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id='flag-quality-metrics')
                ], width=12),
            ], className="mb-4"),

            # Detailed flag metrics
            dbc.Row([
                dbc.Col([
                    dcc.Graph(id='flag-merged-counts')
                ], width=6),
                dbc.Col([
                    dcc.Graph(id='statements-per-flag')
                ], width=6),
            ], className="mb-4"),

            # Traceability Tool Button
            dbc.Row([
                dbc.Col([
                    html.Hr(),
                    html.H5("Traceability Analysis", className="mt-3"),
                    html.P("Analyze the complete process flow from statements to flags with the traceability tool."),
                    dbc.Button("Open Traceability Dashboard", id="open-traceability-btn", color="primary", className="me-2"),
                    html.Div(id="traceability-status"),
                ], width=12),
            ], className="mt-3"),
        ])
    ], className="mb-4")


def create_traceability_modal():
    """
    Create a modal for the traceability dashboard with run selection.

    Returns:
        dbc.Modal: The traceability selection modal
    """
    return dbc.Modal([
        dbc.ModalHeader("Traceability Dashboard"),
        dbc.ModalBody([
            html.P("Select a recent analysis run to visualize the complete process flow."),
            dbc.Row([
                dbc.Col([
                    html.Label("Show all runs:"),
                    dbc.Switch(
                        id="traceability-table-show-all-runs-switch",
                        value=True
                    ),
                ], width=12, className="mb-3"),
            ]),
            dbc.Row([
                dbc.Col([
                    html.Label("Run:"),
                    dcc.Dropdown(
                        id="traceability-run-dropdown",
                        placeholder="Select a run..."
                    ),
                ], width=12),
            ]),
            html.Div(id="traceability-info", className="mt-3"),
        ]),
        dbc.ModalFooter([
            dbc.Button("Launch Traceability Dashboard", id="launch-traceability-btn", color="success", className="me-2"),
            dbc.Button("Close", id="close-traceability-modal-btn", className="ms-auto"),
        ]),
    ], id="traceability-modal", size="lg")