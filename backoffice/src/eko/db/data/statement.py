import traceback

from loguru import logger
from psycopg import Connection, <PERSON>ursor
from psycopg.rows import dict_row
from typing import List, Optional, Tuple, Dict, Any

from eko.db.data.feature_manager import feature_manager
from eko.entities.queries import create_or_retrieve_base_entity_id
from eko.llm.main import get_embedding
from eko.models import Time, PointInTime, SimpleEntity, QuantifiedEntity, Location
from eko.models.statement_metadata import StatementMetadata, StatementAndMetadata
from eko.models.vector.demise.demise_model import DEMISEModel
from eko.models.vector.derived.effect_type import EffectType


class StatementData:
    """
    Handles CRUD operations for statements in the kg_statements_v2 table.
    """

    @staticmethod
    def create(conn: Connection, cursor: Cursor, statement_and_metadata: StatementAndMetadata,
              doc_id: int, page_id: int, demise: Optional[DEMISEModel] = None, time_period_id: Optional[int] = None) -> int:
        """
        Creates a new statement record in the database.

        Args:
            conn: Database connection
            cursor: Database cursor
            statement_and_metadata: StatementAndMetadata object containing statement text and metadata
            doc_id: Document ID
            page_id: Document page ID
            demise: Optional DEMISE model for the statement (if not provided, will use statement_and_metadata.demise)
            time_period_id: Optional time period ID

        Returns:
            The ID of the newly created statement
        """

        if statement_and_metadata is None:
            raise ValueError("statement_and_metadata must be provided")
        # Use the demise model from statement_and_metadata if provided, otherwise use the parameter
        demise_model = statement_and_metadata.demise if statement_and_metadata.demise is not None else demise

        if demise_model is None:
            raise ValueError("A DEMISE model must be provided either in statement_and_metadata.demise or as a separate parameter")

        # Extract entities and related data
        location_ids, object_ids, quantity_ids, subject_ids, time_period_id ,company_id= StatementData._extract_entities(
            cursor, statement_and_metadata.metadata, time_period_id
        )

        # For now, use empty list for authors as it's not part of our model
        author_ids = []

        # Calculate impact_value from the DEMISE model
        impact_value = statement_and_metadata.metadata.impact_value

        if statement_and_metadata.metadata.authors:
            for author in statement_and_metadata.metadata.authors:
                author_ids.append(create_or_retrieve_base_entity_id(cursor, author.name, author.entity_type))

        # Generate embeddings for the statement
        demise_vector = demise_model.to_fixed_size_vector(1024)
        domain_vector = demise_model.domain.to_fixed_size_vector(1024)
        effect_vector = demise_model.get_fixed_size_effect_vector(1024)
        text_vector = get_embedding(statement_and_metadata.statement_text)

        # Use statement_text as source_text if source_text is not provided
        source_text = statement_and_metadata.source_text if statement_and_metadata.source_text is not None else statement_and_metadata.statement_text

        cursor.execute("""
            INSERT INTO kg_statements_v2 (doc_id, doc_page_id, subject_entities, object_entities, statement_category, authors, impact, action,
            statement_text, source_text, context, quantities, locations, time_period_id, model_json, created_at, is_environmental, is_social, is_governance,
            is_animal_welfare, is_vague, is_impactful_action, company_id, impact_value, demise_embedding, text_embedding, domain_embedding, effect_embedding, is_disclosure,
            start_year, end_year)
            VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, now(), %s, %s, %s, %s, %s, %s, %s, %s, %s::vector, %s::vector, %s::vector, %s::vector, %s, %s, %s)
            RETURNING id
            """,
            (doc_id, page_id, subject_ids, object_ids,
             statement_and_metadata.metadata.statement_category, author_ids,
             statement_and_metadata.metadata.impact, statement_and_metadata.metadata.action,
             statement_and_metadata.statement_text, source_text, statement_and_metadata.context,
             quantity_ids, location_ids, time_period_id, demise_model.model_dump_json(),
             statement_and_metadata.metadata.is_environmental, statement_and_metadata.metadata.is_social,
             statement_and_metadata.metadata.is_governance, statement_and_metadata.metadata.is_animal_welfare,
             statement_and_metadata.metadata.is_vague, statement_and_metadata.metadata.is_impactful_action,
             company_id, impact_value, demise_vector, text_vector,domain_vector, effect_vector, statement_and_metadata.metadata.is_disclosure,
             statement_and_metadata.metadata.start_year, statement_and_metadata.metadata.end_year
            )
        )

        statement_id = cursor.fetchone()[0]

        # Save features using the feature manager (more efficient and less prone to deadlocks)
        try:
            # Get sparse key-value pairs from the DEMISE model
            kv = demise_model.to_kv_sparse()

            if kv:
                # Delete existing features for this statement to avoid duplicates
                cursor.execute("DELETE FROM kg_statement_features WHERE statement_id = %s", (statement_id,))

                # Use the feature manager to get feature IDs
                values = []
                for feature_name, feature_value in kv.items():
                    feature_id = feature_manager.get_feature_id(feature_name, cursor)
                    if feature_id is not None:
                        values.append((statement_id, feature_id, feature_value))

                if values:
                    # Batch insert all statement features
                    placeholders = ", ".join(["(%s, %s, %s)" for _ in values])
                    flat_values = [item for sublist in values for item in sublist]

                    cursor.execute(f"""
                        INSERT INTO kg_statement_features (statement_id, feature_id, value)
                        VALUES {placeholders}
                    """, flat_values)

                    logger.debug(f"Saved {len(values)} features for statement {statement_id}")
        except Exception as e:
            logger.error(f"Error saving features for statement {statement_id}: {e}")
            # Continue with returning the statement ID even if feature saving fails

        return statement_id

    @staticmethod
    def delete_by_page_id(conn: Connection, cursor: Cursor, page_id: int) -> None:
        """
        Deletes all statements for a given page ID.

        Args:
            conn: Database connection
            cursor: Database cursor
            page_id: Document page ID
        """
        cursor.execute("DELETE FROM kg_statements_v2 WHERE doc_page_id = %s", (page_id,))

    @staticmethod
    def get_by_id(conn: Connection, statement_id: int) -> StatementAndMetadata:
        """
        Retrieves a statement by ID.

        Args:
            conn: Database connection
            statement_id: Statement ID

        Returns:
            StatementAndMetadata object
        """
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute("""
                SELECT s.id, s.doc_id, s.doc_page_id, s.statement_text, s.source_text, s.context, s.statement_category, s.impact, s.action,
                       s.is_environmental, s.is_social, s.is_governance, s.is_animal_welfare, s.is_vague, s.is_impactful_action,
                       s.model_json, s.impact_value, s.start_year, s.end_year,
                       d.year AS doc_year,
                       'disclosure'= ANY(d.research_categories) AS is_disclosure,

                           (SELECT name
                            FROM kg_base_entities e
                            WHERE e.id = company_id)
                        AS company,
                       COALESCE(
                           (SELECT array_agg(json_build_object('name', e.name, 'entity_type', e.type, 'proper_noun', TRUE))
                            FROM kg_base_entities e
                            WHERE e.id = ANY(s.authors)), ARRAY[]::json[]
                       ) AS authors,
                       COALESCE(
                           (SELECT array_agg(json_build_object('name', e.name, 'entity_type', e.type, 'proper_noun', TRUE))
                            FROM kg_base_entities e
                            WHERE e.id = ANY(s.subject_entities)), ARRAY[]::json[]
                       ) AS subject_entities,
                       COALESCE(
                           (SELECT array_agg(json_build_object('name', e.name, 'entity_type', e.type, 'proper_noun', TRUE))
                            FROM kg_base_entities e
                            WHERE e.id = ANY(s.object_entities)), ARRAY[]::json[]
                       ) AS object_entities,
                       COALESCE(
                           (SELECT array_agg(json_build_object('name', l.name, 'location_type', l.location_type, 'proper_noun', TRUE, 'entity_type', 'location'))
                            FROM kg_locations l
                            WHERE l.id = ANY(s.locations)), ARRAY[]::json[]
                       ) AS locations,
                       COALESCE(
                           (SELECT array_agg(json_build_object('amount', q.amount, 'quantity_type', q.quantity_type, 'unit', q.unit, 'delta', q.delta))
                            FROM kg_quantities q
                            WHERE q.id = ANY(s.quantities)), ARRAY[]::json[]
                       ) AS quantities,
                       tp.from_year, tp.from_month, tp.from_day, tp.to_year, tp.to_month, tp.to_day, tp.precision
                FROM kg_statements_v2 s
                LEFT JOIN kg_time_periods tp ON s.time_period_id = tp.id
                LEFT JOIN kg_documents d ON s.doc_id = d.id
                WHERE s.id = %s
            """, (statement_id,))

            row = cursor.fetchone()

            if not row:
                cursor.execute("SELECT * FROM kg_statements_v2 WHERE id = %s", (statement_id,))
                row = cursor.fetchone()
                if not row:
                    traceback.print_stack()
                    raise ValueError(str(f"Statement with ID {statement_id} not found"))
                else:
                    raise ValueError(str(f"Statement with ID {statement_id} not found, yet found in kg_statements_v2"))

            # Create time object if time period is available
            time = None
            if row.get('from_year') or row.get('to_year'):
                time = Time(
                    from_=PointInTime(
                        year=row.get('from_year'),
                        month=row.get('from_month'),
                        day=row.get('from_day')
                    ) if row.get('from_year') else None,
                    to=PointInTime(
                        year=row.get('to_year'),
                        month=row.get('to_month'),
                        day=row.get('to_day')
                    ) if row.get('to_year') else None,
                    precision=row.get('precision')
                )

            # Create the statement metadata
            quantities = []
            for q_data in row['quantities']:
                # Check if 'entity' field exists, if not create a SimpleEntity
                if 'entity' not in q_data:
                    entity_data = {
                        'name': q_data.get('type', 'unknown'),
                        'entity_type': 'other',
                        'proper_noun': False
                    }
                    q_data['entity'] = SimpleEntity(**entity_data)
                quantities.append(QuantifiedEntity(**q_data))

            metadata = StatementMetadata(
                company=row["company"],
                statement_category=row["statement_category"],
                impact=row["impact"],
                action=row["action"],
                is_environmental=row["is_environmental"],
                is_social=row["is_social"],
                is_governance=row["is_governance"],
                is_animal_welfare=row["is_animal_welfare"],
                is_vague=row["is_vague"],
                is_impactful_action=row["is_impactful_action"],
                is_disclosure=row["is_disclosure"],
                subject_entities=[SimpleEntity(**entity) for entity in row["subject_entities"]],
                authors=[SimpleEntity(**entity) for entity in row["authors"]],
                object_entities=[SimpleEntity(**entity) for entity in row["object_entities"]],
                locations=[Location(**location) for location in row["locations"]],
                quantities=quantities,
                time=time,
                # Set start_year and end_year
                start_year=row.get("start_year"),
                end_year=row.get("end_year"),
                # Extract domain from model_json if available, otherwise use a default
                domain="undefined",  # You may need to extract this from model_json
                # Add impact_value if available
                impact_value=row.get("impact_value", None),
            )

            # Parse DEMISE model from model_json
            demise_model:DEMISEModel
            if row.get('model_json'):
                model_json = row['model_json']
                demise_model = DEMISEModel.model_validate(obj=model_json, strict=True)
            else:
                raise ValueError(f"Statement with ID {statement_id} has no model_json")

            # Create and return StatementAndMetadata
            statement = StatementAndMetadata(
                id=row['id'],
                statement_text=row['statement_text'],
                source_text=row.get('source_text', row['statement_text']),  # Fallback to statement_text if source_text is not available
                context=row['context'],
                metadata=metadata,
                demise=demise_model,
                doc_id=row['doc_id'],
                page_id=row['doc_page_id'],
                doc_year=row.get('doc_year')  # Set doc_year from the document
            )

            return statement

    @staticmethod
    def get_statements_by_doc_id(conn: Connection, doc_id: int) -> List[StatementAndMetadata]:
        """
        Retrieves all statements for a given document ID.

        Args:
            conn: Database connection
            doc_id: Document ID

        Returns:
            List of StatementAndMetadata objects
        """
        statements = []
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute("""
                SELECT id
                FROM kg_statements_v2
                WHERE doc_id = %s
                ORDER BY id
            """, (doc_id,))

            statement_ids = [row['id'] for row in cursor.fetchall()]

            for statement_id in statement_ids:
                statements.append(StatementData.get_by_id(conn, statement_id))

        return statements

    @staticmethod
    def get_statements_by_entity_and_time(conn: Connection, entity_id: int, start_year: int, end_year: int, effect_type: EffectType) -> List[StatementAndMetadata]:
        """
        Retrieves all impactful statements for a given entity and time period.

        Selects statements that:
        1. Have ESG relevance (environmental, social, governance, or animal welfare)
        2. Are impactful actions
        3. Include the specified entity or any entity with the same canonical_id as either subject or object entity
        4. Fall within the specified time period using the document year
        5. Are from documents with domains that meet credibility and role requirements

        Args:
            conn: Database connection
            entity_id: Entity ID to search for (canonical entity)
            start_year: Start year for time period
            end_year: End year for time period
            effect_type: Type of effect (GREEN or RED)

        Returns:
            List of StatementAndMetadata objects
        """
        from eko.settings import settings
        statements = []

        # Use a single cursor for the entire operation to avoid deadlocks
        with conn.cursor(row_factory=dict_row) as cursor:
            # First, get all entities with the same canonical ID or the entity itself
            # Use a simplified query that avoids the json[] issue
            if effect_type == EffectType.GREEN:
                query = """SELECT DISTINCT s.id
    FROM kg_statements_v2 s
    JOIN kg_documents d ON s.doc_id = d.id
    LEFT JOIN kg_domains kd ON d.origin_domain = kd.domain
    WHERE (
        (s.subject_entities IS NOT NULL AND %s = ANY (s.subject_entities))
        )
      AND (
        (s.is_environmental = TRUE OR s.is_social = TRUE OR
         s.is_governance = TRUE OR s.is_animal_welfare = TRUE)
            AND NOT (s.end_year < %s OR s.start_year > %s)
            AND impact_value > 0.01
        )
      AND d.status != 'deleted'
      AND (
        'disclosure' = ANY(d.research_categories)            
        OR d.origin_domain IS NULL
        OR kd.domain IS NULL
        OR (
          kd.credibility >= %s
          AND kd.domain_role = ANY(%s)
          AND (kd.domain_category IS NULL OR NOT (kd.domain_category = ANY(%s)))
        )
      )
    ORDER BY s.id
                """
            else:
                query = """SELECT DISTINCT s.id
    FROM kg_statements_v2 s
    JOIN kg_documents d ON s.doc_id = d.id
    LEFT JOIN kg_domains kd ON d.origin_domain = kd.domain
    WHERE (
        (s.subject_entities IS NOT NULL AND %s = ANY (s.subject_entities))
        )
      AND (
        (s.is_environmental = TRUE OR s.is_social = TRUE OR
         s.is_governance = TRUE OR s.is_animal_welfare = TRUE)
            AND NOT (s.end_year < %s OR s.start_year > %s)
            AND impact_value < -0.01
        )
      AND d.status != 'deleted'
      AND (
        'disclosure' = ANY(d.research_categories)
        OR d.origin_domain IS NULL
        OR kd.domain IS NULL
        OR (
          kd.credibility >= %s
          AND kd.domain_role = ANY(%s)
          AND (kd.domain_category IS NULL OR NOT (kd.domain_category = ANY(%s)))
        )
      )
    ORDER BY s.id
                """

            # Execute with statement year range, document year fallback, and domain filtering parameters
            cursor.execute(
                query,
                (
                    entity_id,
                    start_year,
                    end_year,
                    settings.statement_min_domain_credibility,
                    settings.statement_allowed_domain_roles,
                    settings.statement_excluded_domain_categories
                )
            )

            # Fetch the statement IDs
            statement_ids = [row['id'] for row in cursor.fetchall()]
            logger.info(f"Found {len(statement_ids)} statements for entity {entity_id} from {start_year}-{end_year}")

            # Get each statement individually
            for statement_id in statement_ids:
                try:
                    statements.append(StatementData.get_by_id(conn, statement_id))
                except Exception as e:
                    logger.exception(f"Error fetching statement {statement_id}: {e}")
                    # Continue with the next statement

        return statements

    @staticmethod
    def update(conn: Connection, cursor: Cursor, statement_id: int,
              statement_and_metadata: StatementAndMetadata, demise: Optional[DEMISEModel] = None) -> None:
        """
        Updates an existing statement.

        Args:
            conn: Database connection
            cursor: Database cursor
            statement_id: Statement ID
            statement_and_metadata: Updated StatementAndMetadata object
            demise: Optional updated DEMISE model (if not provided, will use statement_and_metadata.demise)
        """
        # Use the demise model from statement_and_metadata if provided, otherwise use the parameter
        demise_model = statement_and_metadata.demise if statement_and_metadata.demise is not None else demise

        # First retrieve the current statement to get doc_id, page_id, etc.
        cursor.execute("""
            SELECT doc_id, doc_page_id, time_period_id
            FROM kg_statements_v2
            WHERE id = %s
        """, (statement_id,))

        result = cursor.fetchone()
        if not result:
            raise ValueError(f"Statement with ID {statement_id} not found")

        doc_id, page_id, time_period_id = result

        # Extract entities and related data
        location_ids, object_ids, quantity_ids, subject_ids, time_period_id, company_id = StatementData._extract_entities(
            cursor, statement_and_metadata.metadata, time_period_id
        )

        # Update statement
        update_query = """
            UPDATE kg_statements_v2
            SET subject_entities = %s,
                object_entities = %s,
                statement_category = %s,
                impact = %s,
                action = %s,
                statement_text = %s,
                source_text = %s,
                context = %s,
                quantities = %s,
                locations = %s,
                time_period_id = %s,
                is_environmental = %s,
                is_social = %s,
                is_governance = %s,
                is_animal_welfare = %s,
                is_vague = %s,
                is_impactful_action = %s,
                company_id = %s,
                start_year = %s,
                end_year = %s
            WHERE id=%s
        """

        # Use statement_text as source_text if source_text is not provided
        source_text = statement_and_metadata.source_text if statement_and_metadata.source_text is not None else statement_and_metadata.statement_text

        params = [
            subject_ids, object_ids,
            statement_and_metadata.metadata.statement_category,
            statement_and_metadata.metadata.impact, statement_and_metadata.metadata.action,
            statement_and_metadata.statement_text, source_text, statement_and_metadata.context,
            quantity_ids, location_ids, time_period_id,
            statement_and_metadata.metadata.is_environmental, statement_and_metadata.metadata.is_social,
            statement_and_metadata.metadata.is_governance, statement_and_metadata.metadata.is_animal_welfare,
            statement_and_metadata.metadata.is_vague, statement_and_metadata.metadata.is_impactful_action,
            company_id,
            statement_and_metadata.metadata.start_year, statement_and_metadata.metadata.end_year,
            statement_id
        ]

        # Add DEMISE model update if provided
        if demise_model:
            # Calculate impact_value from DEMISE model
            impact_value = demise_model.get_impact_value() if demise_model else 0.0

            # Try to generate embeddings
            try:
                # Generate embeddings for the statement
                demise_vector = demise_model.to_fixed_size_vector(1024)
                text_vector = get_embedding(statement_and_metadata.statement_text)

                update_query += ", model_json = %s, impact_value = %s, demise_embedding = %s::vector, text_embedding = %s::vector"
                params.extend([demise_model.model_dump_json(), impact_value, demise_vector, text_vector])
            except Exception as e:
                logger.error(f"Error generating embeddings during update for statement {statement_id}: {str(e)}")
                update_query += ", model_json = %s, impact_value = %s"
                params.extend([demise_model.model_dump_json(), impact_value])

        update_query += " WHERE id = %s"
        params.append(statement_id)

        cursor.execute(update_query, params)

        # If DEMISE model is updated, update features
        if demise_model:
            # First delete existing features
            cursor.execute("DELETE FROM kg_statement_features WHERE statement_id = %s", (statement_id,))

            # Insert new features
            kv = demise_model.to_kv_sparse()
            for k, v in kv.items():
                cursor.execute("INSERT INTO kg_features (name) VALUES (%s) ON CONFLICT DO NOTHING RETURNING id", (k,))
                if cursor.rowcount == 0:
                    cursor.execute("SELECT id FROM kg_features WHERE name = %s", (k,))
                feature_id = cursor.fetchone()[0]
                cursor.execute("INSERT INTO kg_statement_features (statement_id, feature_id, value) VALUES (%s,%s,%s)",
                            (statement_id, feature_id, v))

    @staticmethod
    def get_statements_for_virtual_entity_efficient(conn: Connection, virt_entity_id: int, esg_only: bool = True) -> List[Dict[str, Any]]:
        """
        Get statements for a virtual entity with their domain embeddings and impact values.
        This is an optimized version that returns dictionaries directly instead of StatementAndMetadata objects.

        Args:
            conn: Database connection
            virt_entity_id: ID of the virtual entity
            esg_only: If True, only return statements that are ESG-related

        Returns:
            List of statement dictionaries with essential properties
        """
        statements = []
        with conn.cursor(row_factory=dict_row) as cursor:
            esg_filter = ""
            if esg_only:
                esg_filter = """
                AND (s.is_environmental = true OR s.is_social = true OR
                    s.is_governance = true OR s.is_animal_welfare = true)
                """

            cursor.execute(f"""
                SELECT
                    s.id,
                    s.statement_text,
                    s.source_text,
                    s.company_id,
                    s.impact_value,
                    s.domain_embedding,
                    s.text_embedding,
                    s.is_environmental,
                    s.is_social,
                    s.is_governance,
                    s.is_animal_welfare,
                    s.doc_id,
                    s.is_disclosure,
                    d.research_categories,
                    s.statement_category
                FROM kg_statements_v2 s
                JOIN kg_virt_entity_map vem ON s.company_id = vem.base_entity_id
                LEFT JOIN kg_documents d ON s.doc_id = d.id
                WHERE vem.virt_entity_id = %s
                {esg_filter}
            """, (virt_entity_id,))

            for row in cursor.fetchall():
                statements.append({
                    "id": row['id'],
                    "statement_text": row['statement_text'],
                    "source_text": row.get('source_text', row['statement_text']),  # Fallback to statement_text if source_text is not available
                    "company_id": row['company_id'],
                    "impact_value": float(row['impact_value']) if row['impact_value'] is not None else 0.0,
                    "domain_embedding": row['domain_embedding'],
                    "text_embedding": row['text_embedding'],
                    "is_environmental": row['is_environmental'],
                    "is_social": row['is_social'],
                    "is_governance": row['is_governance'],
                    "is_animal_welfare": row['is_animal_welfare'],
                    "doc_id": row['doc_id'],
                    "is_disclosure": row['is_disclosure'],
                    "research_categories": row['research_categories'],
                    "statement_category": row['statement_category']
                })

            logger.info(f"Found {len(statements)} statements for virtual entity {virt_entity_id}")

        return statements

    @staticmethod
    def get_statements_for_virtual_entity(conn: Connection, virt_entity_id: int, esg_only: bool = True) -> List[StatementAndMetadata]:
        """
        Get statements for a virtual entity with their domain embeddings and impact values.

        Args:
            conn: Database connection
            virt_entity_id: ID of the virtual entity
            esg_only: If True, only return statements that are ESG-related

        Returns:
            List of StatementAndMetadata objects
        """
        statements = []
        with conn.cursor(row_factory=dict_row) as cursor:
            esg_filter = ""
            if esg_only:
                esg_filter = """
                AND (s.is_environmental = true OR s.is_social = true OR
                    s.is_governance = true OR s.is_animal_welfare = true)
                """

            cursor.execute(f"""
                SELECT s.id
                FROM kg_statements_v2 s
                JOIN kg_virt_entity_map vem ON s.company_id = vem.base_entity_id
                LEFT JOIN kg_documents d ON s.doc_id = d.id
                WHERE vem.virt_entity_id = %s
                {esg_filter}
                ORDER BY s.id
            """, (virt_entity_id,))

            statement_ids = [row['id'] for row in cursor.fetchall()]
            logger.info(f"Found {len(statement_ids)} statements for virtual entity {virt_entity_id}")

            # Get each statement individually
            for statement_id in statement_ids:
                try:
                    statements.append(StatementData.get_by_id(conn, statement_id))
                except Exception as e:
                    logger.exception(f"Error fetching statement {statement_id}: {e}")
                    # Continue with the next statement

        return statements

    @staticmethod
    def get_statements_by_ids(conn: Connection, statement_ids: List[int]) -> Dict[int, StatementAndMetadata]:
        """
        Retrieves multiple statements by their IDs.

        Args:
            conn: Database connection
            statement_ids: List of statement IDs to retrieve

        Returns:
            Dictionary mapping statement IDs to StatementAndMetadata objects
        """
        result = {}
        if not statement_ids:
            return result

        for statement_id in statement_ids:
            try:
                statement = StatementData.get_by_id(conn, statement_id)
                result[statement_id] = statement
            except Exception as e:
                traceback.print_stack()
                logger.exception(f"Error fetching statement {statement_id}: {e}")
                # Continue with the next statement

        return result

    @staticmethod
    def _extract_entities(cursor: Cursor, metadata: StatementMetadata,
                         time_period_id: Optional[int] = None) -> Tuple[List[int], List[int], List[int], List[int], Optional[int], Optional[int]]:
        """
        Extract entities and related data from metadata and persist to the database.

        Args:
            cursor: Database cursor
            metadata: Statement metadata
            time_period_id: Optional existing time period ID

        Returns:
            Tuple of (location_ids, object_ids, quantity_ids, subject_ids, time_period_id, author_ids)
        """
        # Extract company entity
        if metadata.company:
            company_id = create_or_retrieve_base_entity_id(cursor, metadata.company, "company")
            # Remove individual commits to prevent deadlocks
        else:
            company_id = None

        # Extract object entities - using Entity id directly if available
        if metadata.object_entities and len(metadata.object_entities) > 0:
            object_ids = []
            for entity in metadata.object_entities:
                    entity_id = create_or_retrieve_base_entity_id(cursor, entity.name, entity.entity_type)
                    object_ids.append(entity_id)
            # Remove individual commits to prevent deadlocks
        else:
            object_ids = []

        # Extract subject entities - using Entity id directly if available
        if metadata.subject_entities and len(metadata.subject_entities) > 0:
            subject_ids = []
            for entity in metadata.subject_entities:
                    entity_id = create_or_retrieve_base_entity_id(cursor, entity.name, entity.entity_type)
                    subject_ids.append(entity_id)
            # Remove individual commits to prevent deadlocks
        else:
            subject_ids = []

        # Extract locations
        location_ids = []
        if metadata.locations:
            for location in metadata.locations:
                cursor.execute(
                    "INSERT INTO kg_locations (name,location_type) VALUES(%s,%s) ON CONFLICT (name, location_type) DO UPDATE set status='active' RETURNING id",
                    (location.name, location.location_type))
                location_ids.append(cursor.fetchall()[0][0])
                # Remove individual commits to prevent deadlocks

        # Extract time period
        if metadata.time:
            precision = None
            if metadata.time.from_:
                if metadata.time.from_.year and metadata.time.from_.month and metadata.time.from_.day:
                    precision = "day"
                elif metadata.time.from_.year and metadata.time.from_.month:
                    precision = "month"
                elif metadata.time.from_.year:
                    precision = "year"
            else:
                if metadata.time.to:
                    if metadata.time.to.year and metadata.time.to.month and metadata.time.to.day:
                        precision = "day"
                    elif metadata.time.to.year and metadata.time.to.month:
                        precision = "month"
                    elif metadata.time.to.year:
                        precision = "year"
            if precision is not None:
                cursor.execute(
                    "INSERT INTO kg_time_periods (from_year, from_month, from_day, to_year, to_month, to_day, precision) VALUES(%s,%s,%s,%s,%s,%s,%s) on conflict (from_year, from_month, from_day, to_year, to_month, to_day, precision) do update set precision=excluded.precision RETURNING id",
                    (metadata.time.from_.year if metadata.time.from_ else None,
                     metadata.time.from_.month if metadata.time.from_ else None,
                     metadata.time.from_.day if metadata.time.from_ else None,
                     metadata.time.to.year if metadata.time.to else None,
                     metadata.time.to.month if metadata.time.to else None,
                     metadata.time.to.day if metadata.time.to else None, precision))
                time_period_id = cursor.fetchall()[0][0]
                # Remove individual commits to prevent deadlocks

        # Extract quantities
        quantity_ids = []
        if metadata.quantities:
            for quantity in metadata.quantities:
                # Provide a default value for quantity_type if it's None
                quantity_type = quantity.quantity_type if quantity.quantity_type is not None else "unknown"

                cursor.execute(
                    "INSERT INTO kg_quantities (amount, quantity_type, unit, delta) VALUES(%s,%s,%s,%s) on conflict (amount, quantity_type, unit, delta) DO UPDATE set amount=excluded.amount RETURNING id",
                    (quantity.amount, quantity_type, quantity.unit, quantity.delta))

                quantity_ids.append(cursor.fetchall()[0][0])
                # Remove individual commits to prevent deadlocks

        return location_ids, object_ids, quantity_ids, subject_ids, time_period_id, company_id
