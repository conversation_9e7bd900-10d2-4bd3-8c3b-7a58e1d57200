from typing import List, Optional

from psycopg import Connection
from psycopg.rows import dict_row

from eko.models import Entity
from eko.models.vector.derived.effect import EffectModel


class EffectData:
    """
    Handles CRUD operations for effects in the ana_effects table.

    DDL for table creation:

    CREATE TABLE IF NOT EXISTS ana_effects (
        id SERIAL PRIMARY KEY,
        effect_type TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        reason TEXT NOT NULL,
        impact INTEGER NOT NULL,
        authentic INTEGER NOT NULL,
        contribution INTEGER NOT NULL,
        confidence INTEGER NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        demise_centroid_json JSONB,
        entity_name TEXT,
        year INTEGER,
        run_id BIGINT,
        text_embedding VECTOR(1536),
        start_year INTEGER,
        end_year INTEGER,
        impact_score INTEGER,
        demise_embedding VECTOR(1024),
        virtual_entity_id INTEGER
    );

    Related mapping tables:
    - ana_effects_statements_map: Maps effects to statements
    - ana_effects_domains_map: Maps effects to domains
    - ana_effects_relation_map: Maps parent effects to child effects
    - ana_effects_flags_relation_map: Maps effects to effect flags
    """

    @staticmethod
    def create(conn: Connection, effect_model: EffectModel, name:str, entities:List[Entity],
              run_id: int, virtual_entity_id: Optional[int] = None) -> EffectModel:
        """
        Creates a new effect record in the database.

        Args:
            conn: Database connection
            effect_model: EffectModel object containing effect data
            name: Entity name
            entities: List of Entity objects
            run_id: Analysis run ID
            virtual_entity_id: Optional virtual entity ID to associate with this effect

        Returns:
            Updated EffectModel with the database ID set
        """
        # Extract statement IDs directly from the StatementAndMetadata objects
        statement_ids = [statement.id for statement in effect_model.statements if statement.id is not None]

        # Ensure centroid has proper dimension (512)
        centroid = effect_model.centroid
        if len(centroid) != 512:
            # Pad with zeros if needed
            if len(centroid) < 512:
                centroid = centroid + [0.0] * (512 - len(centroid))
            # Truncate if too long
            else:
                centroid = centroid[:512]

        # Generate vector embeddings
        from eko.llm.main import get_embedding

        # Get description for text embedding
        description = effect_model.description if hasattr(effect_model, 'description') else ""

        # Generate text embedding
        text_vector = None
        try:
            text_vector = get_embedding(description)
        except Exception as e:
            from loguru import logger
            logger.warning(f"Failed to generate text embedding: {e}")

        # Get DEMISE vector
        demise_vector = None
        try:
            demise_vector = effect_model.full_demise_centroid.to_fixed_size_vector(1024)
        except Exception as e:
            from loguru import logger
            logger.warning(f"Failed to generate DEMISE embedding: {e}")

        with conn.cursor() as cursor:
            if text_vector and demise_vector:
                # Insert effect with both embeddings
                cursor.execute("""
                    INSERT INTO ana_effects (
                        entity_name, run_id, title, description, reason,
                        impact, authentic, contribution, confidence,
                        demise_centroid_json, effect_type, text_embedding, demise_embedding,
                        start_year, end_year, year, impact_score, created_at, virtual_entity_id
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s,
                        %s::vector, %s::vector, %s, %s, %s, %s, now(), %s
                    ) RETURNING id
                """, (
                    name, run_id, effect_model.title if hasattr(effect_model, 'title') else '',
                    description, effect_model.reason if hasattr(effect_model, 'reason') else '',
                    effect_model.impact if hasattr(effect_model, 'impact') else 0,
                    effect_model.authentic if hasattr(effect_model, 'authentic') else 0,
                    effect_model.contribution if hasattr(effect_model, 'contribution') else 0,
                    effect_model.confidence if hasattr(effect_model, 'confidence') else 0,
                    effect_model.full_demise_centroid.model_dump_json(),
                    effect_model.effect_type.value, text_vector, demise_vector,
                    effect_model.start_year, effect_model.end_year,
                    effect_model.start_year,  # Also set year for backwards compatibility
                    effect_model.impact if hasattr(effect_model, 'impact') else 0,  # impact_score
                    virtual_entity_id or effect_model.virtual_entity_id
                ))
            else:
                # Insert effect without embeddings
                cursor.execute("""
                    INSERT INTO ana_effects (
                        entity_name, run_id, title, description, reason,
                        impact, authentic, contribution, confidence,
                        demise_centroid_json, effect_type, start_year, end_year, year, impact_score, created_at, virtual_entity_id
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, now(), %s
                    ) RETURNING id
                """, (
                    name, run_id, effect_model.title if hasattr(effect_model, 'title') else '',
                    description, effect_model.reason if hasattr(effect_model, 'reason') else '',
                    effect_model.impact if hasattr(effect_model, 'impact') else 0,
                    effect_model.authentic if hasattr(effect_model, 'authentic') else 0,
                    effect_model.contribution if hasattr(effect_model, 'contribution') else 0,
                    effect_model.confidence if hasattr(effect_model, 'confidence') else 0,
                    effect_model.full_demise_centroid.model_dump_json(),
                    effect_model.effect_type.value, effect_model.start_year, effect_model.end_year,
                    effect_model.start_year,  # Also set year for backwards compatibility
                    effect_model.impact if hasattr(effect_model, 'impact') else 0,  # impact_score
                    virtual_entity_id or effect_model.virtual_entity_id
                ))

            effect_id = cursor.fetchone()[0]

            # Batch insert statements mapping
            if statement_ids:
                statement_values = [(effect_id, statement_id) for statement_id in statement_ids]
                cursor.executemany("""
                    INSERT INTO ana_effects_statements_map (effect_id, statement_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, statement_values)

            # Batch insert domains mapping
            domains = effect_model.relevant_domains()
            if domains:
                domain_values = [(effect_id, domain) for domain in domains]
                cursor.executemany("""
                    INSERT INTO ana_effects_domains_map (effect_id, domain)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, domain_values)

            # Ethics mapping tables have been removed

        conn.commit()

        # Set the ID in the model before returning
        effect_model.id = effect_id

        return effect_model

    @staticmethod
    def get_by_id(conn: Connection, effect_id: int) -> EffectModel:
        """
        Retrieves an effect by ID.

        Args:
            conn: Database connection
            effect_id: Effect ID

        Returns:
            EffectModel object
        """
        # Initialize empty lists for related data
        statement_ids = []
        domains = []
        entity_ids = []
        effect_ids = []
        effect_flag_ids = []

        with conn.cursor(row_factory=dict_row) as cursor:
            # Get main effect data
            cursor.execute("""
                SELECT id, entity_name, run_id, title, description, reason,
                       impact, authentic, contribution, confidence,
                       demise_centroid_json, effect_type, start_year, end_year, created_at,
                       year, impact_score, text_embedding, demise_embedding, virtual_entity_id
                FROM ana_effects
                WHERE id = %s
            """, (effect_id,))

            row = cursor.fetchone()

            if not row:
                raise ValueError(f"Effect with ID {effect_id} not found")

            # Retrieve all related data in a single transaction
            # Use a transaction to ensure consistency
            cursor.execute("""
                -- Get statement IDs
                SELECT 'statement' as type, statement_id as id, NULL as name
                FROM ana_effects_statements_map
                WHERE effect_id = %s

                UNION ALL

                -- Get domains
                SELECT 'domain' as type, NULL as id, domain as name
                FROM ana_effects_domains_map
                WHERE effect_id = %s

                UNION ALL

                -- Get virtual entity IDs
                SELECT 'entity' as type, virt_entity_id as id, NULL as name
                FROM ana_effects_entities_map
                WHERE effect_id = %s

                UNION ALL

                -- Get child effect IDs
                SELECT 'child_effect' as type, child_effect_id as id, NULL as name
                FROM ana_effects_relation_map
                WHERE parent_effect_id = %s

                UNION ALL

                -- Get related effect flag IDs
                SELECT 'effect_flag' as type, flag_id as id, NULL as name
                FROM ana_effects_flags_relation_map
                WHERE effect_id = %s
            """, (effect_id, effect_id, effect_id, effect_id, effect_id))

            # Process the results
            for r in cursor.fetchall():
                if r['type'] == 'statement' and r['id'] is not None:
                    statement_ids.append(r['id'])
                elif r['type'] == 'domain' and r['name'] is not None:
                    domains.append(r['name'])
                elif r['type'] == 'entity' and r['id'] is not None:
                    entity_ids.append(r['id'])
                elif r['type'] == 'child_effect' and r['id'] is not None:
                    effect_ids.append(r['id'])
                elif r['type'] == 'effect_flag' and r['id'] is not None:
                    effect_flag_ids.append(r['id'])

            # Parse the EffectModel from JSON
            # Note: We're supplementing any required fields not in the database
            effect_model = EffectModel.model_construct(
                id=row['id'],
                trace_id=f"effect_{row['id']}",
                run_id=row['run_id'],
                effect_type=row['effect_type'],
                entity_name=row['entity_name'],
                start_year=row['start_year'] or 0,
                end_year=row['end_year'] or 0,
                statement_ids=statement_ids,
                domains=domains,
                entity_ids=entity_ids,
                virtual_entity_id=row.get('virtual_entity_id')
            )

            # Add the demise_centroid if available
            if row['demise_centroid_json']:
                from eko.models.vector.demise.demise_model import DEMISEModel
                effect_model.full_demise_centroid = DEMISEModel.model_validate(row['demise_centroid_json'])

            return effect_model

    @staticmethod
    def get_effects_by_virtual_entity(conn: Connection, entity_id: int, run_id: Optional[int] = None) -> List[EffectModel]:
        """
        Retrieves all effects for a given entity, optionally filtering by run ID.

        Args:
            conn: Database connection
            entity_id: Entity ID
            run_id: Optional run ID to filter by

        Returns:
            List of EffectModel objects
        """
        effects = []
        with conn.cursor(row_factory=dict_row) as cursor:
            # First, get all effect IDs for this entity
            if run_id is not None:
                cursor.execute("""
                    SELECT DISTINCT e.id
                    FROM ana_effects e
                    WHERE e.virtual_entity_id = %s AND e.run_id = %s
                    ORDER BY e.id
                """, (entity_id, run_id))
            else:
                cursor.execute("""
                    SELECT DISTINCT e.id
                    FROM ana_effects e
                    WHERE e.virtual_entity_id = %s
                    ORDER BY e.id
                """, (entity_id,))

            effect_ids = [row['id'] for row in cursor.fetchall()]

            # If we have effect IDs, fetch all effect data in a single query
            if effect_ids:
                # Get all main effect data in one query
                placeholders = ",".join("%s" for _ in effect_ids)
                cursor.execute(f"""
                    SELECT id, entity_name, run_id, title, description, reason,
                           impact, authentic, contribution, confidence,
                           demise_centroid_json, effect_type, start_year, end_year, created_at,
                           year, impact_score, text_embedding, demise_embedding, virtual_entity_id
                    FROM ana_effects
                    WHERE id IN ({placeholders})
                    ORDER BY id
                """, effect_ids)

                effect_rows = cursor.fetchall()

                # Get all related data for all effects in one query
                if effect_ids:
                    # Create a mapping to store related data for each effect
                    related_data = {effect_id: {
                        'statement_ids': [],
                        'domains': [],
                        'entity_ids': [],
                        'child_effect_ids': [],
                        'effect_flag_ids': []
                    } for effect_id in effect_ids}

                    # Get statement mappings for all effects
                    cursor.execute(f"""
                        SELECT effect_id, statement_id
                        FROM ana_effects_statements_map
                        WHERE effect_id IN ({placeholders})
                    """, effect_ids)
                    for row in cursor.fetchall():
                        related_data[row['effect_id']]['statement_ids'].append(row['statement_id'])

                    # Get domain mappings for all effects
                    cursor.execute(f"""
                        SELECT effect_id, domain
                        FROM ana_effects_domains_map
                        WHERE effect_id IN ({placeholders})
                    """, effect_ids)
                    for row in cursor.fetchall():
                        related_data[row['effect_id']]['domains'].append(row['domain'])

                    # Get entity mappings for all effects
                    cursor.execute(f"""
                        SELECT effect_id, entity_id
                        FROM ana_effects_entities_map
                        WHERE effect_id IN ({placeholders})
                    """, effect_ids)
                    for row in cursor.fetchall():
                        related_data[row['effect_id']]['entity_ids'].append(row['entity_id'])

                    # Get child effect mappings for all effects
                    cursor.execute(f"""
                        SELECT parent_effect_id, child_effect_id
                        FROM ana_effects_relation_map
                        WHERE parent_effect_id IN ({placeholders})
                    """, effect_ids)
                    for row in cursor.fetchall():
                        related_data[row['parent_effect_id']]['child_effect_ids'].append(row['child_effect_id'])

                    # Get effect flag mappings for all effects
                    cursor.execute(f"""
                        SELECT effect_id, flag_id
                        FROM ana_effects_flags_relation_map
                        WHERE effect_id IN ({placeholders})
                    """, effect_ids)
                    for row in cursor.fetchall():
                        related_data[row['effect_id']]['effect_flag_ids'].append(row['flag_id'])

                    # Create EffectModel objects for each effect
                    for row in effect_rows:
                        effect_id = row['id']
                        effect_data = related_data[effect_id]

                        # Create the EffectModel
                        effect_model = EffectModel.model_construct(
                            id=effect_id,
                            trace_id=f"effect_{effect_id}",
                            run_id=row['run_id'],
                            effect_type=row['effect_type'],
                            entity_name=row['entity_name'],
                            start_year=row['start_year'] or 0,
                            end_year=row['end_year'] or 0,
                            statement_ids=effect_data['statement_ids'],
                            domains=effect_data['domains'],
                            entity_ids=effect_data['entity_ids'],
                            virtual_entity_id=row.get('virtual_entity_id')
                        )

                        # Add the demise_centroid if available
                        if row['demise_centroid_json']:
                            from eko.models.vector.demise.demise_model import DEMISEModel
                            effect_model.full_demise_centroid = DEMISEModel.model_validate(row['demise_centroid_json'])

                        effects.append(effect_model)

        return effects
