"""
Functions for handling statement embeddings.
"""
from typing import List, Optional, Dict, Any, Union, Tu<PERSON>

from loguru import logger
from psycopg import Connection, Cursor

from eko.llm.main import get_embedding
from eko.models.vector.demise.demise_model import DE<PERSON>SEModel


def add_embeddings_to_statement(cursor: Cursor, statement_id: int, demise_model: DEMISEModel, statement_text: str) -> bool:
    """
    Add demise_embedding and text_embedding to a statement in the database.
    
    Args:
        cursor: Database cursor
        statement_id: Statement ID
        demise_model: DEMISE model
        statement_text: Text of the statement
        
    Returns:
        True if successful, False otherwise
    """
    try:
        # Generate embeddings
        # Use to_fixed_size_vector to ensure demise_embedding is 1024 dimensions
        demise_vector = demise_model.to_fixed_size_vector(1024)
        text_vector = get_embedding(statement_text)
        
        if demise_vector and text_vector:
            # Update the statement with both embeddings
            cursor.execute("""
                UPDATE kg_statements_v2
                SET demise_embedding = %s::vector, text_embedding = %s::vector
                WHERE id = %s
            """, (demise_vector, text_vector, statement_id))
            return True
        else:
            logger.warning(f"Failed to generate embeddings for statement {statement_id}")
            return False
    except Exception as e:
        logger.error(f"Error adding embeddings to statement {statement_id}: {str(e)}")
        return False