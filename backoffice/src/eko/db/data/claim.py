import json
from datetime import datetime
from loguru import logger
from psycopg import Connection
from psycopg.rows import dict_row
from typing import List, Optional, Dict, Any, TYPE_CHECKING

from eko.analysis_v2.heart.trust_and_reliability.claim_evidence import ClaimVerdict
from eko.db.data.statement import StatementData
from eko.typing import not_none

if TYPE_CHECKING:
    pass


class DateTimeEncoder(json.JSONEncoder):
    """Custom JSON encoder that handles datetime objects."""
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)


class ClaimData:
    """
    Handles CRUD operations for claims in the ana_claims_v2 table.

    DDL for table creation:

    CREATE TABLE IF NOT EXISTS ana_claims_v2 (
        id SERIAL PRIMARY KEY,
        statement_id INTEGER NOT NULL,
        valid_claim BOOLEAN NOT NULL,
        greenwashing BOOLEAN NOT NULL,
        verdict TEXT NOT NULL,
        summary TEXT NOT NULL,
        conclusion TEXT NOT NULL,
        run_id INTEGER NOT NULL,
        confidence INTEGER NOT NULL,
        counters JSONB NOT NULL,
        company TEXT NOT NULL,
        claim_doc TEXT NOT NULL,
        claim_doc_year INTEGER NOT NULL,
        claim_doc_authors JSONB,
        citations JSONB,
        text TEXT NOT NULL,
        context TEXT,
        esg_claim BOOLEAN NOT NULL,
        company_id INTEGER NOT NULL,
        llm_greenwashing BOOLEAN NOT NULL,
        importance INTEGER DEFAULT 0,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        UNIQUE(run_id, statement_id)
    );

    CREATE INDEX IF NOT EXISTS ana_claims_v2_run_id_idx ON ana_claims_v2(run_id);
    CREATE INDEX IF NOT EXISTS ana_claims_v2_company_id_idx ON ana_claims_v2(company_id);
    CREATE INDEX IF NOT EXISTS ana_claims_v2_statement_id_idx ON ana_claims_v2(statement_id);
    """

    @staticmethod
    def create(conn: Connection, verdict: ClaimVerdict, text: str, context: str, company_id: int) -> int:
        """
        Creates a new claim record in the database.

        Args:
            conn: Database connection
            verdict: ClaimVerdict object containing claim data
            text: Statement text
            context: Statement context
            company_id: Company ID

        Returns:
            ID of the created claim record
        """
        with conn.cursor() as cursor:
            # Convert the counter evidence to JSON string
            counters_json = json.dumps(verdict.counters) if isinstance(verdict.counters, dict) else verdict.counters

            # Convert doc_authors to a JSON array string with custom encoder for datetime objects
            doc_authors_json = json.dumps([x.model_dump() for x in verdict.doc_authors], cls=DateTimeEncoder) if verdict.doc_authors else '[]'

            # Convert citations to JSON - handle both Citation objects and integer IDs
            citations_json = None
            if verdict.citations:
                try:
                    # Check if citations are Citation objects or integers
                    if hasattr(verdict.citations[0], 'model_dump'):
                        # Citation objects
                        citations_json = json.dumps([citation.model_dump() for citation in verdict.citations])
                    else:
                        # Integer IDs
                        citations_json = json.dumps(verdict.citations)
                except Exception as e:
                    logger.error(f"Error converting citations to JSON: {e}")
                    citations_json = '[]'
            else:
                citations_json = '[]'

            # Insert into ana_claims_v2
            cursor.execute(
                """
                INSERT INTO ana_claims_v2 (
                    statement_id, valid_claim, greenwashing, verdict, summary, conclusion,
                    run_id, confidence, counters, company, claim_doc, claim_doc_year,
                    claim_doc_authors, citations, text, context, esg_claim,
                    company_id, llm_greenwashing, importance, created_at,
                    virtual_entity_id, virtual_entity_short_id
                ) VALUES (
                    %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s::jsonb, %s::jsonb, %s, %s, %s, %s, %s, %s, NOW(), %s, %s
                )
                ON CONFLICT (run_id, statement_id)
                DO UPDATE SET
                    valid_claim = EXCLUDED.valid_claim,
                    greenwashing = EXCLUDED.greenwashing,
                    verdict = EXCLUDED.verdict,
                    summary = EXCLUDED.summary,
                    conclusion = EXCLUDED.conclusion,
                    confidence = EXCLUDED.confidence,
                    counters = EXCLUDED.counters,
                    citations = EXCLUDED.citations,
                    llm_greenwashing = EXCLUDED.llm_greenwashing,
                    importance = EXCLUDED.importance,
                    virtual_entity_id = EXCLUDED.virtual_entity_id,
                    virtual_entity_short_id = EXCLUDED.virtual_entity_short_id
                RETURNING id
                """,
                (
                    verdict.statement_id, verdict.valid_claim, verdict.greenwashing,
                    verdict.verdict, verdict.summary, verdict.conclusion, verdict.run_id,
                    verdict.confidence, counters_json, verdict.company, verdict.doc_title,
                    verdict.doc_year, doc_authors_json,
                    citations_json, text, context, True, company_id, verdict.greenwashing,
                    getattr(verdict, 'importance', 0),  # Get importance or default to 0
                    verdict.virtual_entity_id, verdict.virtual_entity_short_id,
                )
            )

            result = cursor.fetchone()
            if result is None:
                raise ValueError("Failed to insert claim record, no ID returned")
            claim_id = result[0]

        conn.commit()
        return claim_id

    @staticmethod
    def get_by_id(conn: Connection, claim_id: int) -> Dict[str, Any]:
        """
        Retrieves a claim by ID.

        Args:
            conn: Database connection
            claim_id: Claim ID

        Returns:
            Dictionary representing the claim
        """
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute(
                """
                SELECT * FROM ana_claims_v2 WHERE id = %s
                """, (claim_id,)
            )

            row = cursor.fetchone()

            if not row:
                raise ValueError(f"Claim with ID {claim_id} not found")
            row['statement']= StatementData.get_by_id(conn, row['statement_id']).model_dump()
            return row

    @staticmethod
    def get_claims_by_company(conn: Connection, company_id: int, run_id: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Retrieves all claims for a given company, optionally filtering by run ID.

        Args:
            conn: Database connection
            company_id: Company ID
            run_id: Optional run ID to filter by

        Returns:
            List of claim dictionaries
        """
        claims = []
        with conn.cursor(row_factory=dict_row) as cursor:
            if run_id is not None:
                cursor.execute(
                    """
                    SELECT * FROM ana_claims_v2
                    WHERE company_id = %s AND run_id = %s
                    ORDER BY id
                    """, (company_id, run_id)
                )
            else:
                cursor.execute(
                    """
                    SELECT * FROM ana_claims_v2
                    WHERE company_id = %s
                    ORDER BY id
                    """, (company_id,)
                )

            claims = cursor.fetchall()
        for claim in claims:
            claim['statement'] = StatementData.get_by_id(conn, claim['statement_id']).model_dump()
        return claims

    @staticmethod
    def list_by_run(conn: Connection, run_id: int) -> List[ClaimVerdict]:
        """
        Retrieves all claims for a specific run as ClaimVerdict models.

        Args:
            conn: Database connection
            run_id: Run ID to filter by

        Returns:
            List of ClaimVerdict instances
        """
        from eko.models.citation_model import Citation
        from eko.models.entity import Entity

        claims = []
        with conn.cursor(row_factory=dict_row) as cursor:
            cursor.execute(
                """
                SELECT
                    c.*,
                    ve.short_id as virtual_entity_short_id,
                    ve.name as virtual_entity_name
                FROM
                    ana_claims_v2 c
                LEFT JOIN
                    kg_virt_entities ve ON c.virtual_entity_id = ve.id
                WHERE
                    c.run_id = %s
                ORDER BY
                    c.id
                """, (run_id,)
            )

            rows = cursor.fetchall()

            for row in rows:
                # Process citations to ensure they're proper Citation objects
                citations_data = row.get('citations', []) or []
                citations = []

                for citation in citations_data:
                    if isinstance(citation, dict):
                        try:
                            citations.append(Citation(**citation))
                        except Exception as e:
                            logger.warning(f"Error converting citation to Citation object: {e}")
                            # Add as-is if conversion fails
                            citations.append(citation)
                    else:
                        citations.append(citation)

                # Process doc_authors to ensure they're proper Entity objects
                doc_authors_data = row.get('doc_authors', []) or []
                doc_authors = []

                for author in doc_authors_data:
                    if isinstance(author, dict):
                        try:
                            doc_authors.append(Entity(**author))
                        except Exception as e:
                            logger.warning(f"Error converting author to Entity object: {e}")
                            # Add as-is if conversion fails
                            doc_authors.append(author)
                    else:
                        doc_authors.append(author)

                # Get the virtual entity from the DAO
                from eko.db.data.virtual_entity import VirtualEntityData
                virtual_entity = VirtualEntityData.get_by_id(conn, row['virtual_entity_id'])


                # Convert to ClaimVerdict
                try:
                    claim_model = ClaimVerdict(
                        id=row['id'],
                        statement_id=row['statement_id'],
                        valid_claim=row['valid_claim'],
                        greenwashing=row['greenwashing'],
                        verdict=row['verdict'],
                        summary=row['summary'],
                        conclusion=row['conclusion'],
                        run_id=row['run_id'],
                        confidence=row['confidence'],
                        counters=row.get('counters', {}),
                        citations=citations,
                        company=row['company'],
                        claim_doc=row['claim_doc'],
                        doc_title=row['claim_doc'],
                        doc_year=row['claim_doc_year'],
                        doc_authors=doc_authors,
                        virtual_entity_id=row['virtual_entity_id'],
                        virtual_entity_short_id=row.get('virtual_entity_short_id', ''),
                        virtual_entity=not_none(virtual_entity),
                        importance=row.get('importance', 0),  # Get importance or default to 0
                        statement=StatementData.get_by_id(conn, row['statement_id'])
                    )
                    # Add the database ID as an attribute
                    setattr(claim_model, 'id', row['id'])
                    claims.append(claim_model)
                except Exception as e:
                    logger.error(f"Error creating ClaimVerdict for claim {row.get('id')}: {e}")
                    logger.exception(e)
                    continue

        return claims

    @staticmethod
    def delete(conn: Connection, claim_id: int) -> bool:
        """
        Deletes a claim from the database.

        Args:
            conn: Database connection
            claim_id: ID of the claim to delete

        Returns:
            True if the claim was deleted, False if it wasn't found
        """
        with conn.cursor() as cursor:
            cursor.execute("DELETE FROM ana_claims_v2 WHERE id = %s", (claim_id,))
            deleted = cursor.rowcount > 0

        conn.commit()

        return deleted

