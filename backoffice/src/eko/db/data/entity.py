import atexit
from concurrent.futures import ThreadPoolExecutor
from datetime import datetime
from typing import Optional, List, Dict, Any, Generator, cast

from loguru import logger
from psycopg import Connection
from psycopg.errors import UniqueViolation
from psycopg.rows import dict_row

from eko.db import get_bo_conn
from eko.entities import CompanyEntityData
from eko.entities.co_entity import CompanyEntityProcessor
from eko.models import Entity, EntityType, name_to_eko_id
from eko.typing import not_none


class EntityStatus:
    ACTIVE = "active"
    DELETED = "deleted"


# Extend the Entity model with additional fields needed for database operations
class EntityExtended(Entity):
    """
    Extended Entity model for kg_base_entities table
    """
    status: str = EntityStatus.ACTIVE
    created_at: Optional[datetime] = None
    canonical_id: Optional[int] = None
    canonical_short_form: Optional[str] = None
    canonical: bool = False
    lei: Optional[str] = None
    lei_record: Optional[Dict[str, Any]] = None
    lei_exact: Optional[bool] = None
    llm_cleaned_name: Optional[str] = None
    jurisdiction: Optional[str] = None
    canonical_eko_id: Optional[str] = None
    ticker: Optional[str] = None
    old_owned_domains: Optional[List[str]] = None
    scope: Optional[str] = None
    text_searchable_name: Optional[str] = None


class EntityData:
    """
    DAO for kg_base_entities table
    """

    # Shared thread pool for all instances
    _thread_pool = ThreadPoolExecutor(max_workers=4, thread_name_prefix="EntityProcessor")

    @classmethod
    def shutdown(cls):
        """
        Shutdown the thread pool gracefully. Should be called when the application exits.
        """
        logger.info("Shutting down EntityData thread pool")
        cls._thread_pool.shutdown(wait=False)

    @staticmethod
    def create(conn: Connection, entity: EntityExtended) -> EntityExtended:
        """
        Create a new entity in the database
        """
        with conn.cursor(row_factory=dict_row) as cur:
            # Generate eko_id if not provided
            if not entity.eko_id.startswith('eko:'):
                entity.eko_id = name_to_eko_id(entity.type, entity.name)

            # Required fields for insert
            fields = ["eko_id", "name", "type", "status"]
            values = [entity.eko_id, entity.name, entity.type, entity.status]

            # Optional fields
            optional_fields = {
                "canonical_id": entity.canonical_id,
                "short_id": entity.short_id,
                "canonical_short_form": entity.canonical_short_form,
                "canonical": entity.canonical,
                "common_name": entity.common_name,
                "lei": entity.lei,
                "legal_name": entity.legal_name,
                "lei_record": entity.lei_record,
                "lei_exact": entity.lei_exact,
                "llm_cleaned_name": entity.llm_cleaned_name,
                "jurisdiction": entity.jurisdiction,
                "canonical_eko_id": entity.canonical_eko_id,
                "description": entity.description,
                "ticker": entity.ticker,
                "url": entity.url,
                "old_owned_domains": entity.old_owned_domains,
                "region_name": entity.region_name,
                "scope": entity.scope,
                "text_searchable_name": entity.text_searchable_name
            }

            # Add non-None optional fields to the query
            for field, value in optional_fields.items():
                if value is not None:
                    fields.append(field)
                    values.append(value)

            # Prepare the SQL query
            fields_str = ", ".join(fields)
            placeholders = ", ".join(["%s"] * len(values))

            try:
                query = f"""
                    INSERT INTO kg_base_entities ({fields_str})
                    VALUES ({placeholders})
                    RETURNING id, created_at
                """
                cur.execute(query, values)
                result = cur.fetchone()
                entity.id = result["id"]
                entity.created_at = result["created_at"]
                return entity
            except UniqueViolation:
                logger.warning(f"Entity with eko_id {entity.eko_id} already exists")
                # Return existing entity
                cur.execute("SELECT * FROM kg_base_entities WHERE eko_id = %s", (entity.eko_id,))
                result = cur.fetchone()
                return EntityExtended(**result)
            except Exception as e:
                logger.exception(f"Failed to create entity: {e}")
                conn.rollback()
                raise

    @staticmethod
    def create_or_get(conn: Connection, name: str, entity_type: str,
                     jurisdiction: Optional[str] = None) -> int:
        """
        Create or retrieve an entity by name and type, returning the entity ID
        """
        if not name or not entity_type:
            raise ValueError("entity_name and entity_type must not be empty")

        with conn.cursor() as cur:
            eko_id = name_to_eko_id(cast(EntityType, entity_type), name)

            # First try to retrieve existing entity
            cur.execute("""
                SELECT id FROM kg_base_entities
                WHERE( (name = %s AND type = %s) OR eko_id = %s) and (updated_at is null or updated_at < now() - interval '3 month')
            """, (name, entity_type, eko_id))

            if result := cur.fetchone():
                return result[0]

            # Create new entity if not exists
            cur.execute("""
                INSERT INTO kg_base_entities (name, type, eko_id, updated_at)
                VALUES (%s, %s, %s, now())
                ON CONFLICT (eko_id) DO UPDATE
                SET name = excluded.name, type = excluded.type , updated_at = now()
                RETURNING id, name
            """, (name, entity_type, eko_id))
            entity_id,entity_name = not_none(cur.fetchone())

            # Process company entities with LEI information
            if entity_type == "company":
                data = CompanyEntityData(
                    entity_id=entity_id,
                    name=entity_name,
                    entity_type=entity_type,
                    strict=False,
                    jurisdiction=jurisdiction,
                    previous_names=[],
                    previous_entity_ids=set(),
                )

                # Submit the task to the thread pool
                def process_company_entity_in_background(data):
                    try:
                        # Instantiate the processor with the DB cursor
                        
                        with get_bo_conn() as conn:
                            with conn.cursor() as cursor:
                                processor = CompanyEntityProcessor(cursor)
        
                                # Run the process and get a boolean result
                                success = processor.process_entity(data)
                        if success:
                            logger.info(f"Successfully matched or inserted company information with LEI for {data.name}")
                        else:
                            logger.info(f"LEI match not found for {data.name}")
                    except Exception as e:
                        logger.exception(f"Error processing company entity {data.name}: {e}")

                # Submit the task to the shared thread pool
                future = EntityData._thread_pool.submit(process_company_entity_in_background, data, cur)
                logger.info(f"Submitted company entity processing to thread pool: {entity_name}")

            return entity_id

    @staticmethod
    def get_by_id(conn: Connection, entity_id: int) -> Optional[EntityExtended]:
        """
        Get an entity by ID
        """
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                """SELECT e.*,
                coalesce((select ARRAY_AGG(kd.domain) FROM kg_domains kd
                JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                WHERE b2.id = e.canonical_id
                OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                FROM kg_base_entities e
                WHERE e.id = %s AND e.status = %s""",
                (entity_id, EntityStatus.ACTIVE)
            )
            result = cur.fetchone()
            if result:
                return EntityExtended(**result)
            return None

    @staticmethod
    def get_by_short_id(conn: Connection, short_id: str) -> Optional[EntityExtended]:
        """
        Get an entity by short_id
        """
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                """SELECT e.*,
                coalesce((select ARRAY_AGG(kd.domain) FROM kg_domains kd
                JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                WHERE b2.id = e.canonical_id
                OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                FROM kg_base_entities e
                WHERE e.short_id = %s AND e.status = %s""",
                (short_id, EntityStatus.ACTIVE)
            )
            result = cur.fetchone()
            if result:
                return EntityExtended(**result)
            return None

    @staticmethod
    def get_by_eko_id(conn: Connection, eko_id: str) -> Optional[EntityExtended]:
        """
        Get an entity by eko_id
        """
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                """SELECT e.*,
                coalesce((select ARRAY_AGG(kd.domain) FROM kg_domains kd
                JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                WHERE b2.id = e.canonical_id
                OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                FROM kg_base_entities e
                WHERE e.eko_id = %s AND e.status = %s""",
                (eko_id, EntityStatus.ACTIVE)
            )
            result = cur.fetchone()
            if result:
                return EntityExtended(**result)
            return None

    @staticmethod
    def update(conn: Connection, entity: EntityExtended) -> EntityExtended:
        """
        Update an entity in the database
        """
        if entity.id is None:
            raise ValueError("Entity ID must be provided for update")

        with conn.cursor(row_factory=dict_row) as cur:
            fields = []
            values = []

            # Build the update fields dynamically
            model_dict = entity.model_dump(exclude={"id", "created_at"})
            for field, value in model_dict.items():
                if value is not None:
                    fields.append(f"{field} = %s")
                    values.append(value)

            if not fields:
                logger.warning("No fields to update")
                return entity

            # Add the entity ID as the last parameter
            values.append(entity.id)

            query = f"""
                UPDATE kg_base_entities
                SET {", ".join(fields)}
                WHERE id = %s
                RETURNING *
            """

            try:
                cur.execute(query, values)
                result = cur.fetchone()
                if result:
                    return EntityExtended(**result)
                logger.error(f"Entity with ID {entity.id} not found for update")
                raise ValueError(f"Entity with ID {entity.id} not found")
            except Exception as e:
                logger.exception(f"Failed to update entity: {e}")
                conn.rollback()
                raise

    @staticmethod
    def get_all_canonical(conn: Connection) -> List[EntityExtended]:
        """
        Get all canonical entities
        """
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                """SELECT e.*,
                coalesce((select ARRAY_AGG(kd.domain) FROM kg_domains kd
                JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                WHERE b2.id = e.canonical_id
                OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                FROM kg_base_entities e
                WHERE e.status = %s AND e.canonical""",
                (EntityStatus.ACTIVE,)
            )
            return [EntityExtended(**dict(x)) for x in cur.fetchall()]

    @staticmethod
    def get_entities_for_canonical(conn: Connection, parent_id: int) -> List[EntityExtended]:
        """
        Get all entities associated with a canonical entity
        """
        with conn.cursor(row_factory=dict_row) as cur:
            cur.execute(
                """SELECT e.*,
                coalesce((select ARRAY_AGG(kd.domain) FROM kg_domains kd
                JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                WHERE b2.id = e.canonical_id
                OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                FROM kg_base_entities e
                WHERE e.status = %s AND e.canonical_id = %s""",
                (EntityStatus.ACTIVE, parent_id)
            )
            return [EntityExtended(**dict(x)) for x in cur.fetchall()]

    @staticmethod
    def get_entities_by_web_search(conn: Connection, name:str, search: str, canonical: bool = True) -> Generator[EntityExtended, None, None]:
        """
        Search entities by POSTGRES web search
        """
        with conn.cursor(row_factory=dict_row) as cur:

            if canonical:
                cur.execute(
                    """WITH matching_entities AS (
                        SELECT distinct canonical_id
                            FROM kg_base_entities
                            WHERE (to_tsvector('english', name) @@ websearch_to_tsquery('english', %s) OR to_tsvector('english', legal_name) @@ websearch_to_tsquery('english', %s))
                            AND status = %s
                        )
                        -- Then fetch the canonical entities
                        SELECT e.*,
                        coalesce((select ARRAY_AGG(kd.domain)
                        FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                        WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                        FROM kg_base_entities e
                        JOIN matching_entities m ON e.id = m.canonical_id
                        ORDER BY levenshtein(left(e.common_name,255), %s)
                        """,
                    (search, search,EntityStatus.ACTIVE, name)
                )
            else:
                cur.execute(
                    """SELECT e.*,
                    coalesce((select ARRAY_AGG(kd.domain)
                    FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                    WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                    FROM kg_base_entities e
                    WHERE (to_tsvector('english', name) @@ websearch_to_tsquery('english', %s) OR to_tsvector('english', legal_name) @@ websearch_to_tsquery('english', %s))
                    AND e.status = %s
                    ORDER BY levenshtein(left(e.common_name,255), %s)
                    """,
                    (search, search, EntityStatus.ACTIVE, name)
                )

            results = cur.fetchall()
            for row in results:
                yield EntityExtended(**(row))


    @staticmethod
    def get_entities_by_regex_search(conn: Connection, name:str, search: str, canonical: bool = True) -> Generator[EntityExtended, None, None]:
        """
        Search entities by POSTGRES web search
        """
        with conn.cursor(row_factory=dict_row) as cur:

            if canonical:
                cur.execute(
                    """WITH matching_entities AS (
                        SELECT distinct canonical_id
                        FROM kg_base_entities
                        WHERE name ~* %s OR legal_name ~* %s
                          AND status = %s
                    )
                       -- Then fetch the canonical entities
                       SELECT e.*,
                              coalesce((select ARRAY_AGG(kd.domain)
                                        FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                                        WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                       FROM kg_base_entities e
                                JOIN matching_entities m ON e.id = m.canonical_id
                       ORDER BY levenshtein(left(e.common_name,255), %s)
                    """,
                    (search, search,EntityStatus.ACTIVE, name)
                )
            else:
                cur.execute(
                    """SELECT e.*,
                              coalesce((select ARRAY_AGG(kd.domain)
                                        FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                                        WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains
                       FROM kg_base_entities e
                       WHERE name ~* %s OR legal_name ~* %s
                         AND e.status = %s
                       ORDER BY levenshtein(left(e.common_name,255), %s)
                    """,
                    (search, search, EntityStatus.ACTIVE, name)
                )

            results = cur.fetchall()
            for row in results:
                yield EntityExtended(**(row))


    @staticmethod
    def fuzzy_search(conn: Connection, name: str, canonical: bool = True) -> Generator[EntityExtended, None, None]:
        """
        Search entities by fuzzy name matching
        """
        with conn.cursor(row_factory=dict_row) as cur:
            name_like = "%" + name + "%"

            if canonical:
                cur.execute(
                    """WITH matching_entities AS (
                        SELECT distinct canonical_id
                            FROM kg_base_entities
                            WHERE (
                                 name ilike %s
                                OR legal_name ilike %s
                                OR llm_cleaned_name ilike %s
                                OR text_searchable_name ilike %s
                                OR common_name ilike %s
                            )
                            AND status = %s
                        )
                        -- Then fetch the canonical entities
                        SELECT e.*,
                        coalesce((select ARRAY_AGG(kd.domain)
                        FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                        WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains,
                        levenshtein(left(e.common_name,255), %s) AS distance
                        FROM kg_base_entities e
                        JOIN matching_entities m ON e.id = m.canonical_id
                        ORDER BY levenshtein(left(e.common_name,255), %s)
                        """,
                    (name_like, name_like, name_like, name_like, name_like, EntityStatus.ACTIVE, name, name)
                )
            else:
                cur.execute(
                    """SELECT e.*,
                    coalesce((select ARRAY_AGG(kd.domain)
                    FROM kg_domains kd JOIN kg_base_entities b2 ON kd.entity_id = b2.id
                    WHERE b2.id = e.canonical_id OR b2.canonical_id = e.canonical_id),'{}'::text[]) AS owned_domains,
                    levenshtein(left(e.common_name,255), %s) AS distance
                    FROM kg_base_entities e
                    WHERE (
                        e.name ilike %s
                        OR e.legal_name ilike %s
                        OR e.llm_cleaned_name ilike %s
                        OR e.text_searchable_name ilike %s
                        OR e.common_name ilike %s
                    )
                    AND e.status = %s
                    ORDER BY levenshtein(left(e.common_name,255), %s)
                    """,
                    (name, name_like, name_like, name_like, name_like, name_like, EntityStatus.ACTIVE, name)
                )

            results = cur.fetchall()
            for row in results:
                # Filter out the distance field which isn't part of EntityExtended
                row_data = {k: v for k, v in dict(row).items() if k != 'distance'}
                yield EntityExtended(**row_data)

    @staticmethod
    def mark_deleted(conn: Connection, entity_id: int) -> bool:
        """
        Mark an entity as deleted (soft delete)
        """
        with conn.cursor() as cur:
            try:
                cur.execute(
                    "UPDATE kg_base_entities SET status = %s WHERE id = %s",
                    (EntityStatus.DELETED, entity_id)
                )
                return cur.rowcount > 0
            except Exception as e:
                logger.exception(f"Failed to mark entity as deleted: {e}")
                conn.rollback()
                raise

    @staticmethod
    def count_entities(conn: Connection, entity_type: Optional[str] = None) -> int:
        """
        Count entities, optionally filtered by type
        """
        with conn.cursor() as cur:
            if entity_type:
                cur.execute(
                    "SELECT COUNT(*) FROM kg_base_entities WHERE status = %s AND type = %s",
                    (EntityStatus.ACTIVE, entity_type)
                )
            else:
                cur.execute(
                    "SELECT COUNT(*) FROM kg_base_entities WHERE status = %s",
                    (EntityStatus.ACTIVE,)
                )
            return cur.fetchone()[0]

    @staticmethod
    def get_aliases(conn: Connection, entity: EntityExtended) -> List[str]:
        """
        Get all aliases (synonyms) for an entity
        """
        with conn.cursor() as cur:
            cur.execute(
                "SELECT synonym FROM kg_entity_synonyms WHERE entity_id = %s",
                (entity.id,)
            )
            return [x[0] for x in cur.fetchall()]

    @staticmethod
    def add_alias(conn: Connection, entity_id: int, synonym: str) -> bool:
        """
        Add an alias (synonym) for an entity
        """
        with conn.cursor() as cur:
            try:
                cur.execute(
                    "INSERT INTO kg_entity_synonyms (entity_id, synonym) VALUES (%s, %s) ON CONFLICT DO NOTHING",
                    (entity_id, synonym)
                )
                return cur.rowcount > 0
            except Exception as e:
                logger.exception(f"Failed to add entity alias: {e}")
                conn.rollback()
                raise

    @staticmethod
    def update_canonical_relation(conn: Connection, entity_id: int, canonical_id: int) -> bool:
        """
        Update the canonical relationship for an entity
        """
        with conn.cursor() as cur:
            try:
                # Get the canonical entity's eko_id
                cur.execute(
                    "SELECT eko_id FROM kg_base_entities WHERE id = %s",
                    (canonical_id,)
                )
                canonical_eko_id = cur.fetchone()[0]

                # Update the entity
                cur.execute(
                    "UPDATE kg_base_entities SET canonical_id = %s, canonical = FALSE, canonical_eko_id = %s WHERE id = %s",
                    (canonical_id, canonical_eko_id, entity_id)
                )
                return cur.rowcount > 0
            except Exception as e:
                logger.exception(f"Failed to update canonical relation: {e}")
                conn.rollback()
                raise

    @staticmethod
    def make_canonical(conn: Connection, entity_id: int) -> bool:
        """
        Make an entity canonical (remove any canonical relationship)
        """
        with conn.cursor() as cur:
            try:
                cur.execute(
                    "UPDATE kg_base_entities SET canonical_id = NULL, canonical = TRUE, canonical_eko_id = NULL WHERE id = %s",
                    (entity_id,)
                )
                return cur.rowcount > 0
            except Exception as e:
                logger.exception(f"Failed to make entity canonical: {e}")
                conn.rollback()
                raise


# Register the shutdown method to be called on application exit
atexit.register(EntityData.shutdown)
