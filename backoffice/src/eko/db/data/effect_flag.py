from typing import List, Optional

from loguru import logger
from psycopg import Connection
from psycopg.rows import dict_row

from eko.db.data.effect import EffectData
from eko.db.data.virtual_entity import VirtualEntityData
from eko.models.vector.derived.categories import EffectCategory
from eko.models.vector.derived.effect import EffectFlagModel
from eko.models.vector.derived.effect_type import EffectType


# Import model section assignment function
# This is imported here to avoid circular imports


class EffectFlagData:
    """
    Handles CRUD operations for effect flags in the ana_effect_flags table.

    DDL for table creation:

    CREATE TABLE IF NOT EXISTS ana_effect_flags (
        id SERIAL PRIMARY KEY,
        run_id BIGINT NOT NULL,
        model_json JSONB NOT NULL,
        created_at TIMESTAMP WITH TIME ZONE DEFAULT now(),
        demise_centroid_json JSONB,
        entity_name TEXT,
        year INTEGER,
        text_embedding VECTOR(1536),
        centroid VECTOR(512),
        start_year INTEGER,
        end_year INTEGER,
        demise_embedding VECTOR(1024),
        effect_type TEXT,
        category TEXT,
        summary TEXT
    );

    Related mapping tables:
    - ana_effect_flags_statements_map: Maps flags to statements
    - ana_effect_flags_doc_pages_map: Maps flags to document pages
    - ana_effect_flags_domains_map: Maps flags to domains
    """

    @staticmethod
    def create(conn: Connection, effect_flag: EffectFlagModel) -> EffectFlagModel:
        """
        Creates a new effect flag record in the database.

        Args:
            conn: Database connection
            effect_flag: EffectFlagModel object containing flag data

        Returns:
            Updated EffectFlagModel with the database ID set
        """
        # Generate vector embeddings
        from eko.llm.main import get_embedding

        # Get text for text embedding (summary + title + description)
        text_content = f"{effect_flag.summary or ''} {effect_flag.title} {effect_flag.analysis}".strip()

        # Generate text embedding
        text_vector = None
        try:
            text_vector = get_embedding(text_content)
        except Exception as e:
            from loguru import logger
            logger.warning(f"Failed to generate text embedding for effect flag: {e}")

        # Get DEMISE vector
        demise_vector = None
        try:
            demise_vector = effect_flag.full_demise_centroid.to_fixed_size_vector(1024)
        except Exception as e:
            from loguru import logger
            logger.warning(f"Failed to generate DEMISE embedding for effect flag: {e}")

        with conn.cursor() as cursor:
            # Insert the main effect flag record
            if text_vector and demise_vector:
                cursor.execute(
                    """
                    INSERT INTO ana_effect_flags (
                        entity_name, run_id, model_json, demise_centroid_json, effect_type, category,
                        start_year, end_year, demise_embedding, text_embedding, title, short_title, description, reason,
                        impact, authentic, contribution, confidence, credibility, year, centroid, created_at, summary,
                        virtual_entity_id, virtual_entity_short_id
                    ) VALUES (
                        %s, %s, %s, %s, %s, %s, %s, %s, %s::vector, %s::vector, %s, %s, %s, %s, %s,
                        %s, %s, %s, %s, %s, %s::vector, now(), %s,
                        %s, %s
                    ) RETURNING id
                """,
                    (
                        effect_flag.entity_name,
                        effect_flag.run_id,
                        effect_flag.model_dump_json(),
                        effect_flag.full_demise_centroid.model_dump_json(),
                        effect_flag.effect_type.name.lower(),
                        effect_flag.category.value,
                        effect_flag.start_year,
                        effect_flag.end_year,
                        demise_vector,
                        text_vector,
                        effect_flag.title,
                        effect_flag.short_title,
                        effect_flag.analysis or "",
                        effect_flag.reason,
                        effect_flag.impact,
                        effect_flag.authentic,
                        effect_flag.contribution,
                        effect_flag.confidence,
                        effect_flag.credibility,
                        effect_flag.start_year,  # year (for backward compatibility)
                        effect_flag.centroid
                        if hasattr(effect_flag, "centroid") and effect_flag.centroid
                        else [0.0] * 512,  # default centroid if not provided
                        effect_flag.summary or "",
                        effect_flag.virtual_entity_id,
                        effect_flag.virtual_entity_short_id,
                    ),
                )
            else:
               raise ValueError("Missing embeddings for effect flag")

            flag_id = cursor.fetchone()[0]

            # Batch insert statement mappings
            if effect_flag.statement_ids:
                statement_values = [(flag_id, statement_id) for statement_id in effect_flag.statement_ids]
                cursor.executemany("""
                    INSERT INTO ana_effect_flags_statements_map (flag_id, statement_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, statement_values)

            # Batch insert domain mappings
            if effect_flag.domains:
                domain_values = [(flag_id, domain) for domain in effect_flag.domains]
                cursor.executemany("""
                    INSERT INTO ana_effect_flags_domains_map (flag_id, domain)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, domain_values)

            # Insert model section mappings
            if effect_flag.model_sections:
                # First, ensure the ana_effect_flags_model_sections_map table exists
                cursor.execute("""
                    CREATE TABLE IF NOT EXISTS ana_effect_flags_model_sections_map (
                        flag_id INTEGER REFERENCES ana_effect_flags(id) ON DELETE CASCADE,
                        model_name TEXT NOT NULL,
                        section_id TEXT NOT NULL,
                        PRIMARY KEY (flag_id, model_name)
                    )
                """)

                # Insert model section mappings
                model_section_values = [(flag_id, model_name, section_id)
                                       for model_name, section_id in effect_flag.model_sections.items()]
                cursor.executemany("""
                    INSERT INTO ana_effect_flags_model_sections_map (flag_id, model_name, section_id)
                    VALUES (%s, %s, %s)
                    ON CONFLICT (flag_id, model_name) DO UPDATE SET section_id = EXCLUDED.section_id
                """, model_section_values)

            # Batch insert effect relationship mappings
            if effect_flag.effect_model_ids:
                effect_values = [(effect_id, flag_id) for effect_id in effect_flag.effect_model_ids]
                cursor.executemany("""
                    INSERT INTO ana_effects_flags_relation_map (effect_id, flag_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, effect_values)

        conn.commit()

        # Set the ID in the model before returning
        effect_flag.id = flag_id

        return effect_flag

    @staticmethod
    def get_by_id(conn: Connection, flag_id: int) -> EffectFlagModel:
        """
        Retrieves an effect flag by ID.

        Args:
            conn: Database connection
            flag_id: Effect flag ID

        Returns:
            EffectFlagModel object
        """
        with conn.cursor(row_factory=dict_row) as cursor:
            # Get main flag data
            cursor.execute("""
                SELECT id, entity_name, run_id, model_json, effect_type, category,
                       demise_centroid_json, start_year, end_year, created_at,
                       title, description, reason, impact, authentic, contribution, confidence, credibility,
                       year, text_embedding, demise_embedding, centroid, summary,
                       virtual_entity_id, virtual_entity_short_id, short_title
                FROM ana_effect_flags
                WHERE id = %s
            """, (flag_id,))

            row = cursor.fetchone()

            if not row:
                raise ValueError(f"Effect flag with ID {flag_id} not found")

            # Get statement IDs
            cursor.execute("""
                SELECT statement_id
                FROM ana_effect_flags_statements_map
                WHERE flag_id = %s
            """, (flag_id,))
            statement_ids = [r['statement_id'] for r in cursor.fetchall()]

            # Get domains
            cursor.execute("""
                SELECT domain
                FROM ana_effect_flags_domains_map
                WHERE flag_id = %s
            """, (flag_id,))
            domains = [r['domain'] for r in cursor.fetchall()]

            # Get model sections
            model_sections = {}
            try:
                cursor.execute("""
                    SELECT model_name, section_id
                    FROM ana_effect_flags_model_sections_map
                    WHERE flag_id = %s
                """, (flag_id,))
                for r in cursor.fetchall():
                    model_sections[r['model_name']] = r['section_id']
            except Exception as e:
                # Table might not exist yet for older flags
                logger.warning(f"Could not retrieve model sections for flag {flag_id}: {e}")

            # Get effect IDs that this flag is related to
            cursor.execute("""
                SELECT effect_id
                FROM ana_effects_flags_relation_map
                WHERE flag_id = %s
            """, (flag_id,))
            effect_ids = [r['effect_id'] for r in cursor.fetchall()]

            virtual_entity= VirtualEntityData.get_by_id(conn, row['virtual_entity_id'])
            # Manually construct the model
            effect_flag = EffectFlagModel(
                id=flag_id,
                trace_id=f"effect_flag_{flag_id}",
                run_id=row['run_id'],
                entity_name=row['entity_name'],
                entity_ids=virtual_entity.get_entity_ids(),
                virtual_entity_id=virtual_entity.id,
                virtual_entity_short_id=virtual_entity.short_id,
                virtual_entity=virtual_entity,
                effect_model_ids=effect_ids,
                effect_model_trace_ids=[f"effect_{eid}" for eid in effect_ids],
                effect_models=[EffectData.get_by_id(conn, eid) for eid in effect_ids],
                effect_type=EffectType.from_text(row['effect_type']),
                category=EffectCategory(row['category']) if row['category'] else EffectCategory.ECOLOGICAL,
                statement_ids=statement_ids,
                domains=domains,
                model_sections=model_sections,  # Add model sections
                start_year=row['start_year'],
                end_year=row['end_year'],
                # These fields are required by the model, fill with defaults if not available
                title=row['title'] if row['title'] is not None else "",
                reason=row['reason'] if row['reason'] is not None else "",
                impact=row['impact'] if row['impact'] is not None else 0,
                authentic=row['authentic'] if row['authentic'] is not None else 0,
                contribution=row['contribution'] if row['contribution'] is not None else 0,
                confidence=row['confidence'] if row['confidence'] is not None else 0,
                credibility=row['credibility'] if row['credibility'] is not None else 0,
                analysis=row['description'] if row['description'] is not None else "",
                summary=row['summary'] if row['summary'] is not None else "",
                full_demise_centroid= (DEMISEModel.model_validate(row['demise_centroid_json']) if row['demise_centroid_json'] else DEMISEModel.model_construct())
            )

            # Add the demise_centroid if available
            if row['demise_centroid_json']:
                from eko.models.vector.demise.demise_model import DEMISEModel
                effect_flag.full_demise_centroid = DEMISEModel.model_validate(row['demise_centroid_json'])

            return effect_flag

    @staticmethod
    def get_flags_by_entity(conn: Connection, entity_id: int,
                            effect_type: Optional[str] = None) -> List[EffectFlagModel]:
        """
        Retrieves all effect flags for a given entity, optionally filtering by flag type.

        Args:
            conn: Database connection
            entity_id: Entity ID
            effect_type: Optional flag type to filter by ('red' or 'green')

        Returns:
            List of EffectFlagModel objects
        """
        flags = []
        with conn.cursor(row_factory=dict_row) as cursor:
            if effect_type is not None:
                cursor.execute("""
                    SELECT DISTINCT f.id
                    FROM ana_effect_flags f
                    WHERE f.virtual_entity_id = %s AND f.effect_type = %s
                    ORDER BY f.id
                """, (entity_id, effect_type))
            else:
                cursor.execute("""
                    SELECT DISTINCT f.id
                    FROM ana_effect_flags f
                    WHERE f.virtual_entity_id = %s
                    ORDER BY f.id
                """, (entity_id,))

            flag_ids = [row['id'] for row in cursor.fetchall()]

            for flag_id in flag_ids:
                flags.append(EffectFlagData.get_by_id(conn, flag_id))

        return flags

    @staticmethod
    def update(conn: Connection, effect_flag: EffectFlagModel) -> EffectFlagModel:
        """
        Updates an existing effect flag record in the database.

        Args:
            conn: Database connection
            effect_flag: EffectFlagModel object containing updated flag data

        Returns:
            Updated EffectFlagModel
        """
        if effect_flag.id is None:
            raise ValueError("Cannot update effect flag without ID")

        with conn.cursor() as cursor:
            # First, delete all existing mappings for this flag
            cursor.execute("DELETE FROM ana_effect_flags_statements_map WHERE flag_id = %s", (effect_flag.id,))
            cursor.execute("DELETE FROM ana_effect_flags_domains_map WHERE flag_id = %s", (effect_flag.id,))
            cursor.execute("DELETE FROM ana_effects_flags_relation_map WHERE flag_id = %s", (effect_flag.id,))

            # Delete model section mappings if the table exists
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'ana_effect_flags_model_sections_map'
                    )
                """)
                if cursor.fetchone()[0]:
                    cursor.execute("DELETE FROM ana_effect_flags_model_sections_map WHERE flag_id = %s", (effect_flag.id,))
            except Exception as e:
                logger.warning(f"Could not delete model section mappings for flag {effect_flag.id}: {e}")

            # Update the main effect flag record
            cursor.execute("""
                UPDATE ana_effect_flags
                SET
                    entity_name = %s,
                    run_id = %s,
                    model_json = %s,
                    demise_centroid_json = %s,
                    effect_type = %s,
                    start_year = %s,
                    end_year = %s
                WHERE id = %s
                RETURNING id
            """, (
                effect_flag.entity_name,
                effect_flag.run_id,
                effect_flag.model_dump_json(),
                effect_flag.full_demise_centroid.model_dump_json(),
                effect_flag.effect_type.name.lower(),
                effect_flag.start_year,
                effect_flag.end_year,
                effect_flag.id
            ))

            if cursor.rowcount == 0:
                raise ValueError(f"Effect flag with ID {effect_flag.id} not found")

            # Batch re-insert all mappings
            # Batch insert statement mappings
            if effect_flag.statement_ids:
                statement_values = [(effect_flag.id, statement_id) for statement_id in effect_flag.statement_ids]
                cursor.executemany("""
                    INSERT INTO ana_effect_flags_statements_map (flag_id, statement_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING

                """, statement_values)

            # Batch insert domain mappings
            if effect_flag.domains:
                domain_values = [(effect_flag.id, domain) for domain in effect_flag.domains]
                cursor.executemany("""
                    INSERT INTO ana_effect_flags_domains_map (flag_id, domain)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING
                """, domain_values)

            # Insert model section mappings
            if effect_flag.model_sections:
                try:
                    # First, ensure the ana_effect_flags_model_sections_map table exists
                    cursor.execute("""
                        CREATE TABLE IF NOT EXISTS ana_effect_flags_model_sections_map (
                            flag_id INTEGER REFERENCES ana_effect_flags(id) ON DELETE CASCADE,
                            model_name TEXT NOT NULL,
                            section_id TEXT NOT NULL,
                            PRIMARY KEY (flag_id, model_name)
                        )
                    """)

                    # Insert model section mappings
                    model_section_values = [(effect_flag.id, model_name, section_id)
                                           for model_name, section_id in effect_flag.model_sections.items()]
                    cursor.executemany("""
                        INSERT INTO ana_effect_flags_model_sections_map (flag_id, model_name, section_id)
                        VALUES (%s, %s, %s)
                        ON CONFLICT (flag_id, model_name) DO UPDATE SET section_id = EXCLUDED.section_id
                    """, model_section_values)
                except Exception as e:
                    logger.warning(f"Could not insert model section mappings for flag {effect_flag.id}: {e}")

            # Batch insert effect relationship mappings
            if effect_flag.effect_model_ids:
                effect_values = [(effect_id, effect_flag.id) for effect_id in effect_flag.effect_model_ids]
                cursor.executemany("""
                    INSERT INTO ana_effects_flags_relation_map (effect_id, flag_id)
                    VALUES (%s, %s)
                    ON CONFLICT DO NOTHING

                """, effect_values)

        conn.commit()

        return effect_flag

    @staticmethod
    def delete(conn: Connection, flag_id: int) -> bool:
        """
        Deletes an effect flag from the database.

        Args:
            conn: Database connection
            flag_id: ID of the effect flag to delete

        Returns:
            True if the flag was deleted, False if it wasn't found
        """
        with conn.cursor() as cursor:
            # First, delete all mappings that reference this flag
            cursor.execute("DELETE FROM ana_effect_flags_statements_map WHERE flag_id = %s", (flag_id,))
            cursor.execute("DELETE FROM ana_effect_flags_domains_map WHERE flag_id = %s", (flag_id,))
            cursor.execute("DELETE FROM ana_effects_flags_relation_map WHERE flag_id = %s", (flag_id,))

            # Delete model section mappings if the table exists
            try:
                cursor.execute("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables
                        WHERE table_name = 'ana_effect_flags_model_sections_map'
                    )
                """)
                if cursor.fetchone()[0]:
                    cursor.execute("DELETE FROM ana_effect_flags_model_sections_map WHERE flag_id = %s", (flag_id,))
            except Exception as e:
                logger.warning(f"Could not delete model section mappings for flag {flag_id}: {e}")

            # Then delete the flag itself
            cursor.execute("DELETE FROM ana_effect_flags WHERE id = %s", (flag_id,))
            deleted = cursor.rowcount > 0

        conn.commit()

        return deleted
