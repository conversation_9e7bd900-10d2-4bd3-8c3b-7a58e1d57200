import json
import traceback
from datetime import datetime
from time import sleep
from typing import Callable

import psycopg
from loguru import logger
from pydantic import BaseModel

from eko.db import cus_connection_config, get_bo_conn
from eko.llm import llm_run_context
from eko.util.slack import send_to_slack


def process_request(request_data):
    result = {'echo': request_data}
    return result


def update_request_status(cursor, request_id, status, response_data=None, message=None):
    query = "UPDATE api_queue SET status = %s, response_data = %s, updated_at = %s, message = %s WHERE id = %s"
    cursor.execute(query, (status, json.dumps(response_data), datetime.now(), message, request_id))
    cursor.connection.commit()



def handle_new_request(operations: dict[str, Callable[[dict], BaseModel]], request_id, request_action, request_data,
                       requester):

    def status_callback(message):
        with psycopg.connect(**cus_connection_config) as cus_conn:
            with cus_conn.cursor() as cus_cursor:
                try:
                    logger.info("Status callback received:", message)
                    cus_cursor.execute("UPDATE api_queue SET message = %s WHERE id = %s", (message, request_id))
                    cus_conn.commit()

                except Exception as e:
                    logger.exception(f"Error processing status callback {request_id}: {e}")
                    send_to_slack(f"Failed to process status callback {request_id}: {str(e)} {traceback.format_exc()}")


    with psycopg.connect(**cus_connection_config) as cus_conn:
        with cus_conn.cursor() as cus_cursor:
            try:
                update_request_status(cus_cursor, request_id, 'processing')
                request_data['requester'] = requester
                request_data['request_id'] = request_id
                request_data['status_callback'] = status_callback
                llm_run_context.request_id = request_id
                response_data = operations[request_action](**request_data)
                llm_run_context.request_id = None
                update_request_status(cus_cursor, request_id, 'completed', response_data)
            except Exception as e:
                logger.exception(f"Error processing request {request_id}: {e}")
                update_request_status(cus_cursor, request_id, 'failed', {'error': str(e)})
                send_to_slack(f"Failed to process request {request_id}: {str(e)} {traceback.format_exc()}")
            finally:
                with get_bo_conn() as conn:
                    with conn.cursor() as bo_cursor:
                        bo_cursor.execute("SELECT sum(input),sum(output) from cost_by_request where request_id=%s", (request_id,))
                        input_cost, output_cost = bo_cursor.fetchone()
                        cus_cursor.execute("UPDATE api_queue SET input_cost = %s, output_cost= %s WHERE id = %s", (input_cost,output_cost, request_id))
                        conn.commit()




def process_pending_requests(operations: dict[str, Callable[[dict], dict]]):
    with psycopg.connect(**cus_connection_config) as connection:
        with connection.cursor() as cursor:
            # Select all pending requests
            cursor.execute("""
                SELECT id, request_action, request_data, requester 
                FROM api_queue 
                WHERE status = 'pending' 
                ORDER BY created_at ASC
            """)
            pending_requests = cursor.fetchall()

    if pending_requests:
        print(f"Found {len(pending_requests)} pending requests. Processing...")
        for request in pending_requests:
            request_id, request_action, request_data, requester = request
            print(f"Processing pending request {request_id}")
            handle_new_request(operations, request_id, request_action, request_data, requester)
    else:
        print("No pending requests found")


def handle_notification(connection, operations: dict[str, Callable], notify):
    try:
        print("Received notification:", notify.payload)
        data = json.loads(notify.payload)
        if 'record' in data:
            record = data['record']
            request_id = record['id']
            request_data = record['request_data']
            request_action = record['request_action']
            requester = record['requester']

            # Only process if status is pending
            if record.get('status') == 'pending':
                handle_new_request(connection, operations, request_id, request_action, request_data, requester)
    except json.JSONDecodeError:
        logger.exception(f"Failed to parse notification payload: {notify.payload}")
    except KeyError as e:
        logger.exception(f"Missing expected field in notification: {e}")
    except Exception as e:
        logger.exception(f"Error processing notification: {e}")


# Listen for notifications
def listen_for_requests(operations: dict[str, Callable]):
    # First process any pending requests
    while True:
        process_pending_requests(operations)
        sleep(5)

# todo: put this back in
# with connection.cursor() as cursor:
#     # Listen for the specific table changes
#     cursor.execute("LISTEN supabase_realtime;")
#     print("Listening for new requests...")
#
#     gen = connection.notifies()
#     try:
#         for notify in gen:
#             handle_notification(operations, notify)
#     except KeyboardInterrupt:
#         gen.close()
#         print("\nStopped listening for requests")
