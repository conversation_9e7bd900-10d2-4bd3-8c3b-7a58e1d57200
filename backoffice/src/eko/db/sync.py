import psycopg


def transfer_table_data(source_conn_params, target_conn_params, tables):
    """
    Transfers data from specified tables in the source database to the target database using the PostgreSQL COPY method.
    Automatically handles column order mismatches between the source and target tables.

    Parameters:
    - source_conn_params (dict): Connection parameters for the source database.
    - target_conn_params (dict): Connection parameters for the target database.
    - tables (list): List of table names to transfer.

    Example conn_params:
    {
        'dbname': 'your_db',
        'user': 'your_user',
        'password': 'your_password',
        'host': 'your_host',
        'port': 'your_port'ls

    }
    """

    for table in tables:
        with psycopg.connect(**source_conn_params) as conn1, psycopg.connect(**target_conn_params) as conn2:
            with conn2.cursor() as cur:
                cur.execute(f"DELETE FROM {table}")
            with conn1.cursor().copy(f"COPY {table} TO STDOUT (FORMAT BINARY)") as copy1:
                with conn2.cursor().copy(f"COPY {table} FROM STDIN (FORMAT BINARY)") as copy2:
                    for data in copy1:
                        copy2.write(data)
