import queue
import threading
import time
import traceback
from contextlib import contextmanager
from typing import Set, Optional

import psycopg
from loguru import logger
from psycopg import Connection


class EkoConnectionPool:
    """
    A custom connection pool for PostgreSQL with improved connection checkout logic.

    Features:
    - Automatic creation of new connections up to max_size
    - Health checking before returning connections
    - Automatic rollback on return
    - Detailed logging to track connection usage and potential leaks
    """

    def __init__(self,
                 conninfo: str,
                 min_size: int = 12,
                 max_size: int = 48,
                 timeout: float = 10.0,
                 max_idle: float = 180.0):
        self.conninfo = conninfo
        self.min_size = min_size
        self.max_size = max_size
        self.timeout = timeout
        self.max_idle = max_idle

        # Connection tracking
        self._pool = queue.Queue()
        self._used_connections: Set[Connection] = set()
        self._lock = threading.RLock()
        self._total_created = 0

        # Pre-fill pool with min_size connections
        self._initialize_pool()

        # Start maintenance thread
        self._stop_maintenance = threading.Event()
        self._maintenance_thread = threading.Thread(
            target=self._maintenance_worker,
            daemon=True,
            name="EkoConnectionPool-Maintenance"
        )
        self._maintenance_thread.start()

    def _initialize_pool(self) -> None:
        logger.info(f"Initializing connection pool with {self.min_size} connections")
        successful = 0
        for _ in range(self.min_size):
            try:
                conn = self._create_connection()
                if conn:
                    self._pool.put(conn)
                    successful += 1
            except Exception as e:
                logger.exception(f"Error creating initial connection: {e}")
        logger.info(f"Pool initialized with {successful} connections")

    def _create_connection(self) -> Optional[Connection]:
        try:
            conn = psycopg.connect(self.conninfo)
            conn.set_autocommit(True)

            # Validate connection
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()
                if not result or result[0] != 1:
                    raise RuntimeError("Unexpected result during connection test.")

            conn._eko_pool_time = time.time()
            conn._eko_pool_id = self._total_created

            with self._lock:
                self._total_created += 1

            logger.debug(f"Created new connection #{conn._eko_pool_id}")
            return conn
        except Exception as e:
            logger.exception(f"Failed to create connection: {e}")
            return None

    def _is_connection_healthy(self, conn: Connection) -> bool:
        try:
            try:
                if conn.broken:
                    logger.error(f"Connection #{conn._eko_pool_id} is BROKEN")
                    return False
                if conn.closed:
                    logger.warning(f"Connection #{conn._eko_pool_id} is already closed")
                    return False
            except Exception as e:
                logger.warning(f"Error checking closed status for connection #{getattr(conn, '_eko_pool_id', 'unknown')}: {e}")
                return False

            try:
                if conn.info.transaction_status != 0:  # Not IDLE
                    logger.debug(f"Connection #{conn._eko_pool_id} in transaction, rolling back")
                    conn.rollback()
            except Exception as tx_e:
                logger.warning(f"Error checking/fixing transaction status for connection #{conn._eko_pool_id}: {tx_e}")
                try:
                    conn.rollback()
                except Exception:
                    pass
                return False

            try:
                with conn.cursor() as cur:
                    cur.execute("SET statement_timeout = 3000")  # 3 seconds
                    cur.execute("SELECT 1")
                    result = cur.fetchone()
                    cur.execute("SET statement_timeout = 0")
                    if not result or result[0] != 1:
                        logger.warning(f"Connection #{conn._eko_pool_id} returned unexpected result: {result}")
                        return False
            except Exception as query_e:
                logger.warning(f"Query check failed on connection #{conn._eko_pool_id}: {query_e}")
                return False

            return True

        except Exception as e:
            logger.warning(f"Health check exception on connection #{getattr(conn, '_eko_pool_id', 'unknown')}: {e}")
            return False

    def _return_connection(self, conn: Connection) -> None:
        with self._lock:
            self._used_connections.discard(conn)
            current_pool_size = self._pool.qsize() + len(self._used_connections)
            # If unhealthy or if we're above our baseline, close the connection
            if not self._is_connection_healthy(conn) or current_pool_size > self.min_size:
                try:
                    logger.debug(f"Closing connection #{conn._eko_pool_id} rather than returning to pool")
                    conn.close()
                except Exception as e:
                    logger.warning(f"Error closing connection #{conn._eko_pool_id}: {e}")
                return

            conn._eko_pool_time = time.time()
            try:
                self._pool.put_nowait(conn)
                logger.debug(f"Returned connection #{conn._eko_pool_id} to pool")
            except queue.Full:
                logger.warning(f"Pool unexpectedly full; closing connection #{conn._eko_pool_id}")
                conn.close()

    @contextmanager
    def connection(self) -> Connection:
        """
        Context manager to get a healthy connection.
        Tries to discard closed/unhealthy connections from the pool before yielding.
        """
        conn = None
        checkout_time = time.time()
        checkout_stack = traceback.format_stack()
        deadline = checkout_time + self.timeout

        try:
            while time.time() < deadline:
                try:
                    conn = self._pool.get(timeout=deadline - time.time())
                except queue.Empty:
                    conn = None

                if conn is None:
                    break

                # If connection is closed or unhealthy, discard it and try the next one.
                if not self._is_connection_healthy(conn):
                    logger.warning(f"Connection #{conn._eko_pool_id} unhealthy; discarding")
                    try:
                        conn.close()
                    except Exception as e:
                        logger.warning(f"Error closing unhealthy connection #{conn._eko_pool_id}: {e}")
                    conn = None
                    continue
                else:
                    # Got a healthy connection.
                    break

            with self._lock:
                current_total = self._pool.qsize() + len(self._used_connections)
            if conn is None:
                if current_total >= self.max_size:
                    logger.error(f"Connection pool exhausted ({current_total} connections in use)")
                    if self._used_connections:
                        conn_ages = [(c._eko_pool_id, time.time() - getattr(c, '_eko_checkout_time', time.time()))
                                     for c in self._used_connections]
                        logger.error(f"Oldest checked out connections: {sorted(conn_ages, key=lambda x: x[1], reverse=True)[:10]}")
                    raise TimeoutError(f"Connection pool exhausted (max_size={self.max_size})")
                conn = self._create_connection()
                if not conn:
                    logger.error("Failed to create a new connection")
                    raise RuntimeError("Failed to create database connection")

            conn._eko_checkout_time = checkout_time
            conn._eko_checkout_stack = checkout_stack
            with self._lock:
                self._used_connections.add(conn)

            yield conn

        except Exception as e:
            logger.exception(f"Exception during connection usage: {e}")
            raise

        finally:
            if conn is not None:
                try:
                    self._return_connection(conn)
                    logger.debug(f"Connection #{conn._eko_pool_id} cleaned up after use")
                except Exception as e:
                    logger.exception(f"Error returning connection #{conn._eko_pool_id}: {e}")
                finally:
                    with self._lock:
                        self._used_connections.discard(conn)

    def _maintenance_worker(self) -> None:
        last_stats_time = time.time()
        last_full_log_time = time.time()
        leak_check_count = 0

        while not self._stop_maintenance.is_set():
            try:
                now = time.time()
                idle_closed = 0
                # Process connections one by one
                for _ in range(self._pool.qsize()):
                    try:
                        conn = self._pool.get_nowait()
                        if now - conn._eko_pool_time > self.max_idle:
                            logger.debug(f"Connection #{conn._eko_pool_id} idle for {now - conn._eko_pool_time:.1f}s, closing")
                            try:
                                conn.close()
                                idle_closed += 1
                            except Exception as e:
                                logger.exception(f"Error closing idle connection #{conn._eko_pool_id}: {e}")
                        else:
                            self._pool.put_nowait(conn)
                    except queue.Empty:
                        break

                with self._lock:
                    current_pool_size = self._pool.qsize()
                    current_used_size = len(self._used_connections)
                    current_total = current_pool_size + current_used_size
                    if current_total < self.min_size:
                        to_create = self.min_size - current_total
                        logger.info(f"Pool below min_size ({current_total}/{self.min_size}); creating {to_create} connections")
                        created = 0
                        for _ in range(to_create):
                            conn = self._create_connection()
                            if conn and self._is_connection_healthy(conn):
                                try:
                                    self._pool.put_nowait(conn)
                                    created += 1
                                except queue.Full:
                                    logger.warning(f"Pool unexpectedly full while adding connection #{conn._eko_pool_id}")
                                    conn.close()
                            else:
                                if conn:
                                    conn.close()
                        logger.info(f"Created {created} new connections; pool now has {self._pool.qsize()} available")

                leak_check_count += 1
                if leak_check_count >= 6:  # every ~30 seconds
                    leak_check_count = 0
                    with self._lock:
                        if len(self._used_connections) > self.min_size * 2:
                            logger.warning(f"Potential connection leak: {len(self._used_connections)} connections in use")
                            connection_ids = [conn._eko_pool_id for conn in self._used_connections]
                            logger.warning(f"Used connection IDs: {connection_ids}")

                now = time.time()
                with self._lock:
                    if now - last_stats_time >= 10:
                        last_stats_time = now
                        logger.debug(f"Pool stats: {self._pool.qsize()} available, {len(self._used_connections)} in use, {self._total_created} total created, {idle_closed} idle closed")
                    if now - last_full_log_time >= 60:
                        last_full_log_time = now
                        logger.info(f"Detailed pool stats: {self._pool.qsize()} available, {len(self._used_connections)} in use, {self._total_created} total created")
                        if self._used_connections:
                            used_conn_ages = [(conn._eko_pool_id, now - getattr(conn, '_eko_checkout_time', now))
                                              for conn in self._used_connections]
                            oldest_conns = sorted(used_conn_ages, key=lambda x: x[1], reverse=True)[:5]
                            if oldest_conns:
                                logger.info(f"Oldest in-use connections: {oldest_conns}")

            except Exception as e:
                logger.exception(f"Maintenance thread error: {e}", exc_info=True)

            self._stop_maintenance.wait(5)

    def shutdown(self) -> None:
        logger.info("Shutting down connection pool")
        self._stop_maintenance.set()

        if self._maintenance_thread.is_alive():
            try:
                self._maintenance_thread.join(timeout=5.0)
                if self._maintenance_thread.is_alive():
                    logger.warning("Maintenance thread did not terminate within timeout")
            except Exception as e:
                logger.exception(f"Error joining maintenance thread: {e}")

        with self._lock:
            pool_size = self._pool.qsize()
            used_size = len(self._used_connections)
            logger.info(f"Before shutdown: {pool_size} connections in pool, {used_size} in use, {self._total_created} total created")
            if used_size > 0:
                logger.warning(f"{used_size} connections still in use during shutdown!")
                for conn in self._used_connections:
                    try:
                        checkout_time = getattr(conn, '_eko_checkout_time', None)
                        age = time.time() - checkout_time if checkout_time else "unknown"
                        logger.warning(f"Conn #{conn._eko_pool_id} in use for {age:.1f}s")
                    except Exception:
                        pass

        with self._lock:
            closed_count = 0
            error_count = 0

            while not self._pool.empty():
                try:
                    conn = self._pool.get_nowait()
                    try:
                        if not conn.closed and conn.info.transaction_status != 0:
                            conn.rollback()
                    except Exception:
                        pass
                    conn.close()
                    closed_count += 1
                except queue.Empty:
                    break
                except Exception as e:
                    error_count += 1
                    logger.exception(f"Error closing pooled connection: {e}")

            used_closed = 0
            for conn in list(self._used_connections):
                try:
                    try:
                        if not conn.closed and conn.info.transaction_status != 0:
                            conn.rollback()
                    except Exception:
                        pass
                    conn.close()
                    used_closed += 1
                except Exception as e:
                    error_count += 1
                    logger.exception(f"Error closing in-use connection: {e}")

            self._used_connections.clear()
            logger.info(f"Shutdown complete: {closed_count} pooled connections closed, {used_closed} in-use connections closed, {error_count} errors")
