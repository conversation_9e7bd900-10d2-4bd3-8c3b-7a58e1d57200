import os

from loguru import logger
from typing_extensions import deprecated

from eko.db import get_bo_conn
from eko.db.sync import transfer_table_data
from eko.log.log import set_log_context, log_info


@deprecated("Don't use")
def sync_xfer_tables_command():
    set_log_context("sync-xfer-tables", None)
    log_info("main", "Started Table Sync")
    transfer_backoffice_to_customer(['xfer_flags_v2', "xfer_gw_claims", "xfer_gw_cherry", "xfer_gw_vague", 'xfer_gw_promises', 'xfer_entities',
                                     'xfer_issues',
                                     "xfer_model_sections", "xfer_runs", "xfer_score"])
    log_info("main", "Finished Table Sync")


@deprecated("Don't use")
def transfer_backoffice_to_customer(tables:list[str]):
    transfer_table_data({
        'dbname': os.environ.get('POSTGRES_DB') or 'backoffice',
        'user': os.environ.get('POSTGRES_USER') or 'postgres',
        'password': os.environ.get('POSTGRES_PASSWORD') or 'password',
        'host': os.environ.get('POSTGRES_HOST') or 'localhost',
        'port': os.environ.get('POSTGRES_PORT') or '5432'
    }, {
        'dbname': os.environ.get('CUSTOMER_DB_DB'),
        'user': os.environ.get('CUSTOMER_DB_USER'),
        'password': os.environ.get('CUSTOMER_DB_PASSWORD'),
        'host': os.environ.get('CUSTOMER_DB_HOST'),
        'port': os.environ.get('CUSTOMER_DB_PORT')
    }, tables)


def remove_old_runs(run_id:int):
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM ana_runs WHERE id NOT IN (SELECT id from xfer_runs) AND id != %s", (run_id,))

@deprecated("Don't use")
def xfer_score(run_id:int, entity_id:int):
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""
INSERT INTO xfer_score (run_id, entity_xid, score, score_json)
SELECT %s as run_id, (select short_id from kg_base_entities where id=%s),
coalesce(
(
    select (sum( flags.impact/100.0 * flags.authentic/100.0 * flags.contribution/100.0 * i.impact_weight / sqrt((SELECT DATE_PART('year', CURRENT_DATE) + 1 - year)) ))
    FROM ana_issue_flags flags
    JOIN _deprecated_kg_issues i ON i.issue=flags.issue
    WHERE flags.flag_type='red'
    AND year <= DATE_PART('year', CURRENT_DATE) + 1
    AND confidence >= 75
    AND flags.entity_id=%s
)
/ ((select count(*) from kg_document_entity_map em where entity_id = %s) + 1),0) as score,
'{}' as score_json
ON CONFLICT (run_id,entity_xid) DO UPDATE SET 
    score = EXCLUDED.score, 
    score_json = EXCLUDED.score_json
            """, (run_id, entity_id, entity_id, entity_id))
            conn.commit()



@deprecated("Don't use")
def xfer_gw_claims(run_id: int):
    """
    Transfer greenwashing claims and counter claims to the xfer_gw_claims table for a specific run
    
    Uses the virtual_entity_short_id field directly as the entity_xid value instead of 
    using the id_encode function, similar to how the xfer_flags function works.
    
    Args:
        run_id: The run ID to transfer claims for
    """
    logger.info("Transferring Greenwashing Claims & Counter Claims")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM xfer_gw_claims")
            logger.info("Inserting flags in xfer_gw_claims")
            cur.execute("""
            INSERT INTO xfer_gw_claims (id, run_id, subject, object, company, subject_xid, object_xid, company_xid, valid_claim, llm_greenwashing, verdict,
            summary, conclusion, verdict_confidence, citations, counters, claim_doc, claim_doc_year, claim_doc_authors, claim_doc_page,
            statement_id, subject_entity_id, object_entity_id, company_id, text, extended_text,
            confidence, quantity_id, location_id, time_period_id, implied, context, created_at, category,
            chunk_page_id, doc_id, action_types, statement_types, status,
            greenwashing, esg_claim, greenwashing_type, greenwashing_reason)
             SELECT * FROM (
                  SELECT
                      cv.id AS verdict_id, cv.run_id, sub_ent.name AS subject, obj_ent.name AS object, co_ent.name AS company,
                      (SELECT short_id FROM kg_virt_entities WHERE id = s.subject_entity_id) AS subject_xid, 
                      (SELECT short_id FROM kg_virt_entities WHERE id = s.object_entity_id) AS object_xid, 
                      (SELECT short_id FROM kg_virt_entities WHERE id = s.company_id) AS company_xid,
                      cv.valid_claim, cv.greenwashing AS llm_greenwashing,
                      cv.verdict, cv.summary, cv.conclusion, cv.confidence AS verdict_confidence,
                       coalesce((select JSON_AGG(
                                       JSON_BUILD_OBJECT(
                                               'doc_name', rep.name,
                                               'doc_id', rep.id,
                                               'doc_page_id', rp.id,
                                               'url', rep.url,
                                               'public_url', rep.public_url,
                                               'page', rp.page,
                                               'score', rep.score,
                                               'title', rep.title,
                                               'credibility', rep.credibility,
                                               'year', rep.year
                                       )
                               )
                        FROM kg_document_pages rp JOIN kg_documents rep ON rp.doc_id = rep.id
                        WHERE rp.id = ANY(cv.citations)
                       ), '[]')::jsonb as citations,
                      (
                          SELECT JSON_AGG(JSON_BUILD_OBJECT('text', summarized_text, 'issue', null, 'citations', ft.citations))
                          FROM (
                                   SELECT
                                       string_agg(summarized_text, E'\n\n') as summarized_text,
                                       null,
                                       JSON_AGG(
                                               JSON_BUILD_OBJECT(
                                                       'id', cc.chunk_id,
                                                       'report', cc_rep.title,
                                                       'year', cc_rep.year,
                                                       'doc_page_id', cc_page.id,
                                                       'authors', cc_rep.authors,
                                                       'page', cc_page.page,
                                                       'url', cc_rep.url,
                                                       'public_url', cc_rep.public_url,
                                                       'credibility', cc_rep.credibility,
                                                       'score', cc_rep.score
                                               )
                                       ) as citations
                                   FROM ana_gw_claim_counters cc
                                   JOIN kg_report_chunks c ON c.id = cc.chunk_id
                                   JOIN kg_document_pages cc_page ON cc_page.id = c.doc_page_id
                                   JOIN kg_documents cc_rep ON cc_rep.id = cc_page.doc_id
                                   WHERE cc.claim_verdict_id = cv.id
                                   GROUP BY 2
                               ) ft
                      ) as counters,
                      (select name from kg_documents where id = s.doc_id limit 1) as claim_doc,
                      (select year from kg_documents where id = s.doc_id limit 1) as claim_doc_year,
                      (select authors from kg_documents where id = s.doc_id limit 1) as claim_doc_authors,
                      (select page from kg_document_pages where id = s.doc_page_id limit 1) as claim_doc_page,
                      s.id,
                      s.subject_entity_id,
                      s.object_entity_id,
                      s.company_id,
                      s.text,
                      s.extended_text,
                      s.confidence,
                      s.quantity_id,
                      s.location_id,
                      s.time_period_id,
                      s.implied,
                      s.context,
                      s.created_at,
                      s.category,
                      s.doc_page_id as chunk_page_id,
                      s.doc_id,
                      s.action_types,
                      s.statement_types,
                      s.status,
                      s.greenwashing,
                      s.esg_claim,
                      s.greenwashing_type,
                      s.greenwashing_reason
                  FROM ana_gw_claim_verdict cv
                           JOIN kg_statements s on cv.statement_id = s.id
                           LEFT JOIN kg_base_entities sub_ent ON s.subject_entity_id = sub_ent.id
                           LEFT JOIN kg_base_entities obj_ent ON s.object_entity_id = obj_ent.id
                           LEFT JOIN kg_base_entities co_ent ON s.company_id = co_ent.id
                  WHERE (cv.run_id IN (SELECT id from xfer_runs) OR cv.run_id = %s)
                  ) AS subquery
            ON CONFLICT (id) DO UPDATE SET
                run_id = EXCLUDED.run_id,
                valid_claim = EXCLUDED.valid_claim,
                llm_greenwashing = EXCLUDED.llm_greenwashing,
                verdict = EXCLUDED.verdict,
                summary = EXCLUDED.summary,
                conclusion = EXCLUDED.conclusion,
                verdict_confidence = EXCLUDED.verdict_confidence,
                counters = EXCLUDED.counters
            """, (run_id,))
            conn.commit()


@deprecated("Don't use")
def xfer_gw_promises(run_id:int):
    """
    Transfer greenwashing promises to the xfer_gw_promises table for a specific run
    
    Uses the short_id from kg_virt_entities directly as the entity_xid value instead of 
    using the id_encode function, similar to how the xfer_flags function works.
    
    Args:
        run_id: The run ID to transfer promises for
    """
    logger.info("Transferring Greenwashing Promises")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM xfer_gw_promises")
            cur.execute("""
                INSERT INTO xfer_gw_promises (id, run_id, entity_xid, fulfilled, created_at, verdict, confidence, verdict_json, citations, fulfilled_verdict_json, broken_verdict_json, statement_json, summary, conclusion)
                SELECT
                       verdict.id,
                       verdict.run_id,
                       (SELECT short_id FROM kg_virt_entities WHERE id = entity_id),
                       fulfilled,
                       created_at,
                       verdict,
                       confidence,
                       verdict_json,
                       fulfilled_verdict_json,
                       broken_verdict_json,
                         (select
                                       JSON_BUILD_OBJECT(
                                               'text', stat.text,
                                                  'extended_text', stat.extended_text,
                                                    'subject', (select short_id from kg_virt_entities where id=stat.subject_entity_id),
                                                    'object', (select short_id from kg_virt_entities where id=stat.object_entity_id),
                                                    'company', (select short_id from kg_virt_entities where id=stat.company_id),
                                                    'action_types', stat.action_types,
                                                    'statement_types', stat.statement_types,
                                                    'category', stat.category,
                                                    'doc_id', stat.doc_id,
                                                    'doc_page_id', stat.doc_page_id,
                                                    'location', (select JSON_BUILD_OBJECT('name', loc.name, 'type', loc.location_type) from kg_locations loc where loc.id = stat.location_id),
                                                    'time_period', (select JSON_BUILD_OBJECT('start', tp.from_year, 'end', tp.to_year) from kg_time_periods tp where tp.id = stat.time_period_id),
                                                    'quantity', (select JSON_BUILD_OBJECT('amount', q.amount, 'unit', q.unit) from kg_quantities q where q.id = stat.quantity_id),
                                                    'implied', stat.implied,
                                                    'context', stat.context,
                                                    'created_at', stat.created_at,
                                                    'status', stat.status,
                                                    'greenwashing', stat.greenwashing,
                                                    'esg_claim', stat.esg_claim,
                                                    'greenwashing_type', stat.greenwashing_type,
                                                    'greenwashing_reason', stat.greenwashing_reason,
                                                     'citation',coalesce((select JSON_AGG(
                                                       JSON_BUILD_OBJECT(
                                                               'doc_name', rep.name,
                                                               'doc_id', rep.id,
                                                               'doc_page_id', rp.id,
                                                               'url', rep.url,
                                                               'public_url', rep.public_url,
                                                               'page', rp.page,
                                                               'score', rep.score,
                                                               'title', rep.title,
                                                               'credibility', rep.credibility,
                                                               'year', rep.year
                                                       )
                                               )
                                        FROM kg_document_pages rp JOIN kg_documents rep ON rp.doc_id = rep.id
                                        WHERE rp.id =stat.doc_page_id),'[]')
                                        )::jsonb as statement_json
                                        FROM kg_statements stat
                                        WHERE stat.id = statement_id

                                       ) as statement_json,

                       (select JSON_AGG(
                                       JSON_BUILD_OBJECT(
                                               'doc_name', rep.name,
                                               'doc_id', rep.id,
                                               'doc_page_id', rp.id,
                                               'url', rep.url,
                                               'public_url', rep.public_url,
                                               'page', rp.page,
                                               'score', rep.score,
                                               'title', rep.title,
                                               'credibility', rep.credibility,
                                               'year', rep.year
                                       )
                               )
                        FROM kg_document_pages rp JOIN kg_documents rep ON rp.doc_id = rep.id
                        WHERE rp.id = ANY(verdict.citations)
                       )::jsonb as citations,
                       confidence,
                       verdict,
                       conclusion,
                       summary,
                       (SELECT short_id FROM kg_virt_entities WHERE id = entity_id)
                from ana_gw_promise_verdict verdict
                WHERE (verdict.run_id IN (SELECT id from xfer_runs) OR verdict.run_id = %s)
                """, (run_id,))
            conn.commit()



@deprecated("Don't use")
def xfer_model_sections():
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM xfer_model_sections")
            cur.execute("""INSERT INTO xfer_model_sections
                           SELECT s.model,s.section,s.title,s.description,s.level,s.icon
                           FROM kg_model_sections as s
                           WHERE s.status != 'deleted'
            """)
            conn.commit()


@deprecated("Don't use")
def xfer_issues():
    logger.info("Transferring issues")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("DELETE FROM xfer_issues")
            cur.execute("""INSERT INTO xfer_issues (ethical_group, type, issue, description, summary, embedding,
                                                    created_at, synonyms, section_map, impact, impact_weight, title)
                           SELECT ethical_group, type, issue, description, summary, embedding,
 created_at,
                (
                SELECT array_agg(distinct s.synonym)
                FROM kg_issue_synonyms s
                WHERE s.issue = tax.issue
                ) as synonyms   ,
            (SELECT JSON_AGG(
                        JSON_BUILD_OBJECT(ms.model,
                            JSON_BUILD_OBJECT(
                            'section', ms.section,
                            'title', ms.title,
                            'description', ms.description,
                            'level', ms.level,
                            'icon', ms.icon
                            )
                        )
                    ) as smap
            from kg_issue_section_map as map
            JOIN kg_model_sections as ms ON ms.section = map.model_section
            WHERE map.issue = tax.issue
            ) as section_map ,tax.impact, tax.impact_weight , tax.title
                           FROM _deprecated_kg_issues as tax
                           WHERE tax.status != 'deleted'
            """)
            conn.commit()


@deprecated("Don't use")
def xfer_runs():
    logger.info("Transferring runs")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            cur.execute("""INSERT INTO xfer_runs (run_type, id, start_year, end_year, models, scope, target, completed_at)
            SELECT 'full' as run_type, max(id) as id, start_year, max(end_year), max(models), scope, target, max(completed_at)
            FROM ana_runs
            WHERE status = 'completed'
              AND run_type = 'full'
            GROUP BY start_year, scope, target
            UNION
            SELECT  'inc' as run_type,max(id) as id, start_year, max(end_year), max(models), scope, target, max(completed_at)
            from ana_runs
            WHERE status = 'completed'
              AND run_type = 'inc'
              AND id >= (SELECT max(id) FROM ana_runs WHERE run_type = 'full')
            GROUP BY start_year, scope, target
           UNION
            SELECT run_type::text as run_type, id, start_year, end_year, models, scope, target, completed_at
            from ana_runs
            WHERE status = 'completed'
                AND run_type = 'hist'
               OR run_type = 'hist-inc'
            ON CONFLICT DO NOTHING;
            """)
            conn.commit()



@deprecated("Don't use")
def xfer_entities(run_id: int):
    """
    Transfer entities to the xfer_entities table for a specific run
    
    Args:
        run_id: The run ID to transfer entities for
    """
    logger.info("Transferring entities")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # First check schema of xfer_entities
            cur.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'xfer_entities' 
            AND column_name IN ('common_names', 'leis')
            """)
            
            columns = cur.fetchall()
            column_types = {col[0]: col[1] for col in columns}
            
            # Clear any existing entities for this run
            cur.execute("DELETE FROM xfer_entities WHERE run_id = %s", (run_id,))
            
            # Insert entities from the virtual entities table with array field handling
            if column_types.get('common_names') == 'ARRAY' and column_types.get('leis') == 'ARRAY':
                # Both fields are arrays in target table
                cur.execute("""
                INSERT INTO xfer_entities (
                    run_id, entity_xid, name, type, title, description, 
                    common_names, leis, legal_names, aka, created_at
                )
                SELECT 
                    %s AS run_id, 
                    short_id AS entity_xid,
                    name,
                    type,
                    title,
                    description,
                    common_names,
                    leis,
                    legal_names,
                    aka,
                    created_at
                FROM kg_virt_entities
                """, (run_id,))
            else:
                # Convert arrays to text for target table with text fields
                cur.execute("""
                INSERT INTO xfer_entities (
                    run_id, entity_xid, name, type, title, description, 
                    common_names, leis, legal_names, aka, created_at
                )
                SELECT 
                    %s AS run_id, 
                    short_id AS entity_xid,
                    name,
                    type,
                    title,
                    description,
                    CASE WHEN common_names IS NULL THEN NULL
                         ELSE array_to_string(common_names, ', ')
                    END AS common_names,
                    CASE WHEN leis IS NULL THEN NULL
                         ELSE array_to_string(leis, ', ')
                    END AS leis,
                    legal_names,
                    aka,
                    created_at
                FROM kg_virt_entities
                """, (run_id,))
            
            conn.commit()

@deprecated("Don't use")
def xfer_gw_cherry(run_id: int):
    """
    Transfer greenwashing cherry-picking data to the xfer_gw_cherry table for a specific run
    
    Args:
        run_id: The run ID to transfer data for
    """
    logger.info("Transferring greenwashing cherry-picking data")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Clear any existing cherry-picking data
            cur.execute("DELETE FROM xfer_gw_cherry")
            
            # Insert cherry-picking data from new selective highlighting detection
            cur.execute("""
            INSERT INTO xfer_gw_cherry (
                id, run_id, entity_xid, label, red_flags, green_flags, 
                model, score, explanation, analysis, reason, citations, analysis_model
            )
            SELECT 
                cp.id,
                %s AS run_id,
                ve.short_id AS entity_xid,
                'Cherry Picking' AS label,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', s.id,
                        'text', s.statement_text,
                        'impact', s.impact_value,
                        'doc_id', s.doc_id,
                        'doc_page_id', s.doc_page_id,
                        'domain_embedding', s.domain_embedding
                    ))
                    FROM unnest(cp.negative_statement_ids) AS statement_id
                    JOIN kg_statements_v2 s ON s.id = statement_id
                ) AS red_flags,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', s.id,
                        'text', s.statement_text,
                        'impact', s.impact_value,
                        'doc_id', s.doc_id,
                        'doc_page_id', s.doc_page_id,
                        'domain_embedding', s.domain_embedding
                    ))
                    FROM unnest(cp.positive_statement_ids) AS statement_id
                    JOIN kg_statements_v2 s ON s.id = statement_id
                ) AS green_flags,
                'SELECTIVE_HIGHLIGHTING' AS model,
                CASE 
                    WHEN cp.repeat_count > 5 THEN 80
                    WHEN cp.repeat_count > 3 THEN 60
                    ELSE 40
                END AS score,
                'Positive statements repeated ' || cp.repeat_count || ' times while downplaying negative impacts' AS explanation,
                'Cherry picking detected: ' || cp.repeat_count || ' repetitions of positive statements with average impact of ' || 
                ROUND(cp.average_positive_impact::numeric, 2) || ' while downplaying negative impacts with average value of ' || 
                ROUND(cp.negative_impact::numeric, 2) AS analysis,
                'The company repeatedly emphasizes minor positive impacts while downplaying significant negative impacts in the same domain.' AS reason,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'doc_id', d.id,
                        'doc_name', d.name,
                        'title', d.title,
                        'url', d.url,
                        'public_url', d.public_url,
                        'year', d.year,
                        'credibility', d.credibility
                    ))
                    FROM (
                        SELECT DISTINCT doc_id
                        FROM kg_statements_v2 s
                        WHERE s.id = ANY(cp.positive_statement_ids) OR s.id = ANY(cp.negative_statement_ids)
                    ) AS stmt_docs
                    JOIN kg_documents d ON d.id = stmt_docs.doc_id
                ) AS citations,
                jsonb_build_object(
                    'virt_entity_id', cp.virt_entity_id,
                    'positive_statement_ids', cp.positive_statement_ids,
                    'negative_statement_ids', cp.negative_statement_ids,
                    'domain_vector', cp.domain_vector,
                    'average_positive_impact', cp.average_positive_impact,
                    'negative_impact', cp.negative_impact,
                    'repeat_count', cp.repeat_count,
                    'created_at', cp.created_at,
                    'run_id', cp.run_id
                ) AS analysis_model
            FROM ana_cherry_v2 cp
            JOIN kg_virt_entities ve ON ve.id = cp.virt_entity_id
            WHERE (cp.run_id IN (SELECT id FROM xfer_runs) OR cp.run_id = %s)
            
            UNION
            
            -- Insert flooding instances (similar to cherry picking in customer app)
            SELECT 
                fl.id + 10000, -- Add offset to avoid ID conflicts with cherry picking
                %s AS run_id,
                ve.short_id AS entity_xid,
                'Flooding' AS label,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', s.id,
                        'text', s.statement_text,
                        'impact', s.impact_value,
                        'doc_id', s.doc_id,
                        'doc_page_id', s.doc_page_id,
                        'domain_embedding', s.domain_embedding
                    ))
                    FROM unnest(fl.negative_statement_ids) AS statement_id
                    JOIN kg_statements_v2 s ON s.id = statement_id
                ) AS red_flags,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'id', s.id,
                        'text', s.statement_text,
                        'impact', s.impact_value,
                        'doc_id', s.doc_id,
                        'doc_page_id', s.doc_page_id,
                        'domain_embedding', s.domain_embedding
                    ))
                    FROM unnest(fl.positive_statement_ids) AS statement_id
                    JOIN kg_statements_v2 s ON s.id = statement_id
                ) AS green_flags,
                'SELECTIVE_HIGHLIGHTING' AS model,
                CASE 
                    WHEN fl.positive_count > 10 THEN 85
                    WHEN fl.positive_count > 7 THEN 65
                    ELSE 45
                END AS score,
                'Flooding detected: ' || fl.positive_count || ' minor positive statements used to distract from significant negative impacts' AS explanation,
                'Flooding detected: ' || fl.positive_count || ' minor positive statements with average impact of ' || 
                ROUND(fl.average_positive_impact::numeric, 2) || ' used to distract from negative impacts with average value of ' || 
                ROUND(fl.negative_impact::numeric, 2) AS analysis,
                'The company uses multiple small positive impacts to distract from significant negative impacts in the same domain.' AS reason,
                (
                    SELECT jsonb_agg(jsonb_build_object(
                        'doc_id', d.id,
                        'doc_name', d.name,
                        'title', d.title,
                        'url', d.url,
                        'public_url', d.public_url,
                        'year', d.year,
                        'credibility', d.credibility
                    ))
                    FROM (
                        SELECT DISTINCT doc_id
                        FROM kg_statements_v2 s
                        WHERE s.id = ANY(fl.positive_statement_ids) OR s.id = ANY(fl.negative_statement_ids)
                    ) AS stmt_docs
                    JOIN kg_documents d ON d.id = stmt_docs.doc_id
                ) AS citations,
                jsonb_build_object(
                    'virt_entity_id', fl.virt_entity_id,
                    'positive_statement_ids', fl.positive_statement_ids,
                    'negative_statement_ids', fl.negative_statement_ids,
                    'domain_vector', fl.domain_vector,
                    'average_positive_impact', fl.average_positive_impact,
                    'negative_impact', fl.negative_impact,
                    'positive_count', fl.positive_count,
                    'created_at', fl.created_at,
                    'run_id', fl.run_id
                ) AS analysis_model
            FROM ana_flooding fl
            JOIN kg_virt_entities ve ON ve.id = fl.virt_entity_id
            WHERE (fl.run_id IN (SELECT id FROM xfer_runs) OR fl.run_id = %s)
            """, (run_id, run_id, run_id, run_id))
            conn.commit()


@deprecated("Don't use")
def xfer_gw_vague(run_id: int):
    """
    Transfer greenwashing vague claims data to the xfer_gw_vague table for a specific run
    
    Args:
        run_id: The run ID to transfer data for
    """
    logger.info("Transferring greenwashing vague claims data")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Clear any existing vague claims data
            cur.execute("DELETE FROM xfer_gw_vague")
            
            # Insert vague claims data
            cur.execute("""
            INSERT INTO xfer_gw_vague (
                id, run_id, entity_xid, completed_at
            )
            SELECT 
                id,
                %s AS run_id,
                (SELECT short_id FROM kg_virt_entities WHERE id = entity_id) AS entity_xid,
                created_at
            FROM ana_gw_vague_claims
            WHERE (run_id IN (SELECT id FROM xfer_runs) OR run_id = %s)
            """, (run_id, run_id))
            conn.commit()


@deprecated("Don't use")
def xfer_flags(run_id: int):
    """
    Transfer flags to the xfer_flags_v2 table for a specific run using the new XferEffectFlagModel
    
    Args:
        run_id: The run ID to transfer flags for
    """
    logger.info("Transferring flags to xfer_flags_v2")
    with get_bo_conn() as conn:
        with conn.cursor() as cur:
            # Clear any existing flags for this run
            cur.execute("DELETE FROM xfer_flags_v2 WHERE run_id = %s", (run_id,))
            
            # Insert flags with model field populated with full XferEffectFlagModel
            cur.execute("""
            INSERT INTO xfer_flags_v2 (
                run_id, id, entity_xid, flag_type, issue, model
            )
            SELECT 
                %s AS run_id,
                ef.id,
                ef.virtual_entity_short_id AS entity_xid,
                ef.effect_type AS flag_type,
                iff.issue,
                jsonb_build_object(
                    'id', ef.id,
                    'entity_xid', ef.virtual_entity_short_id,
                    'flag_type', ef.effect_type,
                    'flag_title', ef.title,
                    'year', ef.year,
                    'start_year', ef.start_year,
                    'end_year', ef.end_year,
                    'score', iff.score,
                    'impact', ef.impact,
                    'authentic', ef.authentic,
                    'contribution', ef.contribution,
                    'confidence', ef.confidence,
                    'issue', iff.issue,
                    'flag_summary', ef.summary,
                    'flag_analysis', ef.description,
                    'domains', (SELECT array_agg(domain) FROM ana_effect_flags_domains_map WHERE flag_id = ef.id),
                    'citations', iff.citations,
                    'flag_statements', iff.flag_statements,
                    'impact_value_analysis', iff.impact_value_analysis,
                    'category', ef.category,
                    'full_demise_centroid', ef.demise_centroid_json
                ) AS model
            FROM ana_effect_flags ef
            JOIN ana_issue_flags_effects_map iem ON ef.id = iem.effect_id
            JOIN ana_issue_flags iff ON iem.flag_id = iff.id
            WHERE (ef.run_id IN (SELECT id FROM xfer_runs) OR ef.run_id = %s)
            AND ef.virtual_entity_short_id IS NOT NULL
            """, (run_id, run_id))
            conn.commit()

# KG_TABLES=kg_portfolio_to_fund_map,kg_portfolios,kg_securities
