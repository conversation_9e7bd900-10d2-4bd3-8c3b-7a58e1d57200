import sys
import threading
from typing import Optional

from dotenv import load_dotenv
from eko.commands.analyse import analyse_command
from fastapi import FastAPI, Query
from loguru import logger
from pydantic import Field, BaseModel

from eko.commands.chunk import chunk_command
from eko.commands.fix import fix_doc_hash
from eko.commands.scrape import scrape_reports_command
from eko.db._deprecated_xfer import sync_xfer_tables_command
from eko.entities.queries import get_entity_by_short_id
from eko.llm.main import dump_costing

# Load environment variables
load_dotenv()

# Configure logging
logger.remove()
logger.add(sys.stdout, level="INFO")

# Initialize FastAPI
app = FastAPI()

@app.get("/")
def read_root():
    return {"message": "Welcome to the EKO API"}

@app.post("/sync-tables/")
def sync_tables():
    sync_xfer_tables_command()
    return {"status": "Table synchronization complete"}

@app.post("/fix/report-hash/")
def fix_doc_hash_route():
    fix_doc_hash()
    return {"status": "Report hash fix complete"}


@app.post("/chunk/")
def chunk(
        strategy: str = Query(default="all", description="Which strategy to use to select reports to process."),
        max_reports: int = Query(default=0, description="The maximum number of reports to process."),
        max_workers: int = Query(default=1, description="How many processes to use.")
):
    chunk_command(max_reports, max_workers, strategy)
    return {"status": "Chunking complete"}

class AnalysisRequest(BaseModel):
    entity_id: Optional[str] = Field(default=None, description="The entity short_id of the entity to analyse"),
    run_type: Optional[str] = Field(default=None, description="The type of run, hist (historical), comp (comparative), inc (incremental) or full"),
    start_year: Optional[int] = Field(default=None, description="The start year for the analysis."),
    end_year: Optional[int] = Field(default=None, description="The end year for the analysis. Omit for present day."),
    max_workers: int = Field(default=8, description="How many processes to use."),
    models: str = Field(default="sdg,doughnut,eko", description="Which ethical models to analyse against")


@app.post("/analyse/")
def analyse(request:AnalysisRequest):
    download_thread = threading.Thread(target=analyse_command, name="Analyse", args=(request.end_year, get_entity_by_short_id(request.entity_id),request.max_workers, request.run_type, request.start_year, request.models))
    download_thread.start()
    return {"status": "Analysis started"}

class ScrapeRequest(BaseModel):
    entity: Optional[str] = Field(default=None, description="The eko_id of the entity to analyse"),
    max_workers: int = Field(default=1, description="How many processes to use."),
    exclude_own_domains: bool = Field(default=True, description="Exclude domains owned by the entity.")

@app.post("/scrape-reports/")
def scrape_reports(request:ScrapeRequest):
    scrape_reports_command(request.entity, request.max_workers, request.exclude_own_domains)
    return {"status": "Scraping complete"}

@app.on_event("shutdown")
def shutdown_event():
    dump_costing()
