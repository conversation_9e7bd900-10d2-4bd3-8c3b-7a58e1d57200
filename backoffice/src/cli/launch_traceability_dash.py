"""
<PERSON><PERSON><PERSON> to launch the enhanced traceability dashboard directly.

This script bypasses the main CLI interface and directly launches the dashboard 
with the comprehensive traceability visualization component, providing detailed
insights into the complete analysis pipeline.
"""

import os
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from eko.db import get_bo_conn

# Ensure assets path is correctly set
ASSETS_PATH = Path(__file__).parent.parent / "eko" / "dash" / "assets"

def load_recent_runs():
    """
    Load information about recent runs that have traceability data.
    
    Returns:
        list: List of run information dictionaries
    """
    print("Looking for existing runs with traceability data...")
    
    runs_info = []
    
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            # Find runs with traceability data
            cursor.execute("""
                SELECT DISTINCT
                    ar.id,
                    ar.target,
                    ar.created_at,
                    ar.status,
                    COUNT(DISTINCT tsd.entity_id) as entity_count
                FROM 
                    ana_runs ar
                JOIN 
                    dash.trk_statement_decisions tsd ON ar.id = tsd.run_id
                GROUP BY
                    ar.id, ar.target, ar.created_at, ar.status
                ORDER BY
                    ar.created_at DESC
                LIMIT 10
            """)
            
            runs = cursor.fetchall()
            
            if not runs:
                print("No runs with traceability data found. You need to run the analysis process first.")
                return runs_info
            
            print(f"Found {len(runs)} runs with traceability data:")
            
            for run in runs:
                run_id, target, created_at, status, entity_count = run
                
                # Get some basic statistics for this run
                cursor.execute("""
                    SELECT
                        COUNT(*) as statement_count,
                        COUNT(DISTINCT entity_id) as entity_count
                    FROM
                        dash.trk_statement_decisions
                    WHERE
                        run_id = %s
                """, (run_id,))
                
                stats = cursor.fetchone()
                statement_count = stats[0] if stats else 0
                
                # Format date for display
                created_date = created_at.strftime("%Y-%m-%d %H:%M") if created_at else "Unknown date"
                
                run_info = {
                    "id": run_id,
                    "name": target or f"Run #{run_id}",
                    "created_at": created_date,
                    "status": status,
                    "entity_count": entity_count,
                    "statement_count": statement_count
                }
                
                runs_info.append(run_info)
                print(f"  - Run #{run_id}: {target} ({created_date}) - {entity_count} entities, {statement_count} statements")
    
    return runs_info
