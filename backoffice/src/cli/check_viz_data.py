import os
import sys

# Add parent directory to path
sys.path.append(os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from eko.entities.queries import get_entity_by_short_id

def check_viz_data(entity_short_id):
    """
    Check visualization data for an entity to diagnose process flow issues.
    """
    from eko.db import get_bo_conn
    
    # Get the entity
    entity = get_entity_by_short_id(entity_short_id)
    if not entity:
        print(f"Entity with short ID '{entity_short_id}' not found")
        return
    
    print(f"Checking visualization data for entity: {entity.name} ({entity_short_id})")
    
    # Get the most recent run for this entity
    with get_bo_conn() as conn:
        with conn.cursor() as cursor:
            cursor.execute("""
                SELECT id FROM ana_runs 
                WHERE target = %s::integer
                ORDER BY created_at DESC
                LIMIT 1
            """, (entity.id,))
            
            run = cursor.fetchone()
            if not run:
                print(f"No analysis runs found for entity {entity.name}")
                return
                
            run_id = run[0]
            print(f"Using run ID: {run_id}")
            
            # Check for effect models with data for this run
            cursor.execute("""
                SELECT id, effect_type 
                FROM ana_effects
                WHERE run_id = %s
            """, (run_id,))
            
            effects = cursor.fetchall()
            if not effects:
                print(f"No effects found for run ID {run_id}")
                return
                
            print(f"Found {len(effects)} effects:")
            effect_types = {}
            for effect_id, effect_type in effects:
                if effect_type not in effect_types:
                    effect_types[effect_type] = []
                effect_types[effect_type].append(effect_id)
                
            for effect_type, ids in effect_types.items():
                print(f"  - {effect_type}: {len(ids)} effects")
                
            # Check for effect flags with data for this run
            cursor.execute("""
                SELECT id, effect_type 
                FROM ana_effect_flags
                WHERE run_id = %s
            """, (run_id,))
            
            flags = cursor.fetchall()
            if not flags:
                print(f"No flags found for run ID {run_id}")
                return
                
            print(f"Found {len(flags)} flags:")
            flag_types = {}
            for flag_id, flag_type in flags:
                if flag_type not in flag_types:
                    flag_types[flag_type] = []
                flag_types[flag_type].append(flag_id)
                
            for flag_type, ids in flag_types.items():
                print(f"  - {flag_type}: {len(ids)} flags")
                
            # Examine the data structure that would be passed to the template
            print("\nExamining data structure for visualization:")
            
            # Build a simplified version of the analysis_data structure
            analysis_data = {
                "entity": entity.name,
                "flags": {
                    "red": [{"id": flag_id} for flag_id in flag_types.get("red", [])],
                    "green": [{"id": flag_id} for flag_id in flag_types.get("green", [])],
                },
                "process_flow": {
                    "red": {
                        "clusters": [{"id": i} for i in range(len(effect_types.get("red", [])))],
                        "initial_flags": [],
                        "merged_flags": [],
                    },
                    "green": {
                        "clusters": [{"id": i} for i in range(len(effect_types.get("green", [])))],
                        "initial_flags": [],
                        "merged_flags": [],
                    }
                }
            }
            
            # Check key elements of the data structure for emptiness
            for key_path in [
                "flags.red", "flags.green", 
                "process_flow.red.clusters", "process_flow.green.clusters",
                "process_flow.red.initial_flags", "process_flow.green.initial_flags",
                "process_flow.red.merged_flags", "process_flow.green.merged_flags"
            ]:
                parts = key_path.split('.')
                value = analysis_data
                for part in parts:
                    if part in value:
                        value = value[part]
                    else:
                        value = None
                        break
                
                print(f"  - {key_path}: {'Empty' if not value else f'Contains {len(value)} items'}")

    return analysis_data

if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("Usage: python check_viz_data.py <entity_short_id>")
        sys.exit(1)
        
    entity_short_id = sys.argv[1]
    check_viz_data(entity_short_id)
