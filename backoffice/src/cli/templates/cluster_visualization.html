<!DOCTYPE html>
<html>
<head>
    <title>Entity Clustering Visualization</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <style>
        body { padding: 20px; }
        .cluster-card { margin-bottom: 20px; }
        .entity-list { max-height: 300px; overflow-y: auto; }
        .viz-container { margin-bottom: 30px; }
        .tsne-container { 
            border: 1px solid #ddd;
            padding: 10px;
            margin-bottom: 20px;
            border-radius: 5px;
        }
        .nav-tabs { margin-bottom: 20px; }
        .canonical { background-color: #d4edda; }
    </style>
</head>
<body>
    <div class="container-fluid">
        <h1 class="mb-4">Entity Clustering Visualization</h1>
        <p>{{ clusters|length }} clusters found based on name similarity</p>
        
        <ul class="nav nav-tabs" id="myTab" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="visualization-tab" data-bs-toggle="tab" data-bs-target="#visualization" 
                    type="button" role="tab" aria-selected="true">t-SNE Visualization</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="clusters-tab" data-bs-toggle="tab" data-bs-target="#clusters" 
                    type="button" role="tab" aria-selected="false">Cluster Details</button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="stats-tab" data-bs-toggle="tab" data-bs-target="#stats" 
                    type="button" role="tab" aria-selected="false">Statistics</button>
            </li>
        </ul>
        
        <div class="tab-content" id="myTabContent">
            <!-- t-SNE Visualization Tab -->
            <div class="tab-pane fade show active" id="visualization" role="tabpanel">
                <div class="tsne-container">
                    <h2>t-SNE Visualization</h2>
                    <p>This visualization shows entities positioned based on name similarity. 
                    Entities close together are more likely to be the same legal entity.</p>
                    {% if tsne_viz_base64 %}
                        <img src="data:image/png;base64,{{ tsne_viz_base64 }}" class="img-fluid" alt="t-SNE Visualization">
                    {% else %}
                        <div class="alert alert-warning">No visualization available</div>
                    {% endif %}
                </div>
            </div>
            
            <!-- Cluster Details Tab -->
            <div class="tab-pane fade" id="clusters" role="tabpanel">
                <h2>Cluster Details</h2>
                <div class="row">
                    {% for cluster in cluster_data %}
                    <div class="col-md-6">
                        <div class="card cluster-card">
                            <div class="card-header">
                                <h5 class="card-title">Cluster {{ cluster.id }} ({{ cluster.entities|length }} entities)</h5>
                            </div>
                            <div class="card-body">
                                <div class="entity-list">
                                    <table class="table table-sm">
                                        <thead>
                                            <tr>
                                                <th>ID</th>
                                                <th>Name</th>
                                                <th>Legal Name</th>
                                                <th>Jurisdiction</th>
                                                <th>LEI</th>
                                                <th>Canonical</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for entity in cluster.entities %}
                                            <tr {% if entity.canonical %}class="canonical"{% endif %}>
                                                <td>{{ entity.id }}</td>
                                                <td>{{ entity.name }}</td>
                                                <td>{{ entity.legal_name }}</td>
                                                <td>{{ entity.jurisdiction }}</td>
                                                <td>{{ entity.lei }}</td>
                                                <td>{% if entity.canonical %}✅{% endif %}</td>
                                            </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    {% endfor %}
                </div>
            </div>
            
            <!-- Statistics Tab -->
            <div class="tab-pane fade" id="stats" role="tabpanel">
                <h2>Clustering Statistics</h2>
                <div class="card">
                    <div class="card-body">
                        <table class="table">
                            <tr>
                                <th>Total Clusters</th>
                                <td>{{ stats.total_clusters }}</td>
                            </tr>
                            <tr>
                                <th>Processed Clusters</th>
                                <td>{{ stats.processed_clusters }}</td>
                            </tr>
                            <tr>
                                <th>Successful Updates</th>
                                <td>{{ stats.successful_updates }}</td>
                            </tr>
                            <tr>
                                <th>Failed Updates</th>
                                <td>{{ stats.failed_updates }}</td>
                            </tr>
                            <tr>
                                <th>Already Canonical</th>
                                <td>{{ stats.already_canonical }}</td>
                            </tr>
                            <tr>
                                <th>Search Filter</th>
                                <td>{{ stats.search_string or 'None' }}</td>
                            </tr>
                            <tr>
                                <th>Started</th>
                                <td>{{ stats.started_at }}</td>
                            </tr>
                            <tr>
                                <th>Completed</th>
                                <td>{{ stats.completed_at }}</td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>