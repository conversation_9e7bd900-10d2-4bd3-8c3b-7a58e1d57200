FROM nvidia/cuda:12.6.2-cudnn-devel-ubuntu22.04

ARG PYVER="3.11"
ENV TZ=Europe/London
ENV DEBIAN_FRONTEND=noninteractive

LABEL maintainer="ekoIntelligence"

ARG GITUSERNAME="ekoIntelligence"
ARG GITEMAIL="<EMAIL>"
ENV GOOGLE_APPLICATION_CREDENTIALS=/app/credentials/service-account-key.json
ENV GOOGLE_CLOUD_PROJECT=***********

# Update the apt repo and install necessary packages
RUN apt-get update && \
    apt-get install -y software-properties-common && \
    add-apt-repository -y ppa:deadsnakes/ppa && \
    apt-get update && \
    apt-get install -y \
        python$PYVER \
        python3-pip \
        git-all \
        build-essential \
        gcc \
        g++ \
        make \
        libffi-dev \
        libssl-dev \
        python$PYVER-dev \
        libopenblas-dev \
        liblapack-dev \
        pkg-config \
        libleptonica-dev \
        tesseract-ocr \
        libtesseract-dev \
        python3-pil \
        tesseract-ocr-eng \
        tesseract-ocr-script-latn \
        libxml2-dev \
        libxslt-dev \
        libjpeg-dev \
        zlib1g-dev \
        libpng-dev \
        libatlas-base-dev \
        gfortran \
    && apt-get -y upgrade

# Clean up apt cache to reduce image size
RUN apt-get clean && rm -rf /var/lib/apt/lists/*

# Set PYVER as the default Python interpreter
RUN update-alternatives --install /usr/bin/python3 python3 /usr/bin/python$PYVER 1

# Update PIP and install pipenv
RUN python3 -m pip install --upgrade pip setuptools wheel uv uvicorn gunicorn

# Configure git
RUN git config --global user.name "$GITUSERNAME" && \
    git config --global user.email $GITEMAIL && \
    git config --global init.defaultBranch main

# Set the working directory
WORKDIR /app

# Create necessary directories
RUN mkdir -p /var/lib/session /app/src


WORKDIR /app/src
RUN mkdir -p /app/var
RUN ln -s /app/var ~/eko_var

# Pre-install numpy and cython before installing dependencies
RUN uv venv
RUN uv pip install numpy cython
ENV BLIS_ARCH="generic"
RUN uv pip install blis
RUN uv pip install spacy
RUN uv pip install pip

# Copy Pipfile and Pipfile.lock
COPY src/pyproject.toml /app/src/pyproject.toml


#RUN uv install -v
RUN uv pip install pip

# Download spaCy model inside the virtual environment
RUN  uv run python -m spacy download en_core_web_sm

# Install additional Python packages
RUN uv add "psycopg[binary]" "lxml[html_clean]"

## Install playwright
RUN uv run playwright install
RUN uv run playwright install chromium
RUN uv run playwright install-deps


# Copy your application code
COPY src /app/src

# Copy and set up your scripts
COPY eko /bin
RUN chmod +x /bin/eko

EXPOSE 5000
