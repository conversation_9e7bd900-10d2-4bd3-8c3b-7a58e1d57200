drop view if exists view_my_companies;

create view view_my_companies with (security_invoker=on) as
select co.id,
       co.name,
       co.entity_xid,
       quota.quantity as quota,
       profiles.id    as profile_id,
       org.id         as org_id
from cus_ana_companies co
         join acc_quota quota on co.quota_id = quota.id
         left join acc_organisations org on (quota.scope = 'org' and quota.organisation = org.id)
         left join profiles on (quota.scope = 'individual' and quota.customer = profiles.id) or
                               (org.id is null or org.id = profiles.organisation)
where quota.item_type = 'entity';


drop view if exists view_quota_for_single_doc;

create view view_quota_for_single_doc with (security_invoker=on) as
select quota.id,
       quota.quantity as quota,
       profiles.id    as profile_id,
       org.id         as org_id
from acc_quota quota
         left join acc_organisations org on (quota.scope = 'org' and quota.organisation = org.id)
         left join profiles on (quota.scope = 'individual' and quota.customer = profiles.id) or
                               (org.id is null or org.id = profiles.organisation)
where quota.item_type = 'document-analysis';



drop view if exists view_quota_for_entity_analysis;

create view view_quota_for_entity_analysis with (security_invoker=on) as
select quota.id,
       quota.quantity as quota,
       profiles.id    as profile_id,
       org.id         as org_id
from acc_quota quota
         left join acc_organisations org on (quota.scope = 'org' and quota.organisation = org.id)
         left join profiles on (quota.scope = 'individual' and quota.customer = profiles.id) or
                               (org.id is null or org.id = profiles.organisation)
where quota.item_type = 'entity-analysis';


drop view if exists view_single_doc_runs;
create view view_single_doc_runs with (security_invoker=on) as
select doc_runs.id,
       doc_runs.doc_ana_id as analysis_id,
       doc_runs.quota_id,
       run_by,
       ent.name,
       ent.type,
       doc.public_url,
       doc.analysis_json,
       doc_runs.created_at,
       doc_runs.entity_xid
from cus_ana_hist_gw_single_doc_runs doc_runs
         join xfer_entities_v2 as ent on ent.entity_xid = doc_runs.entity_xid
         join xfer_gw_single_doc as doc on doc.id = doc_runs.doc_ana_id;

drop view if exists view_entity_analysis_runs;
create view view_entity_analysis_runs with (security_invoker=on) as
select entity_runs.id,
       entity_runs.quota_id,
       run_by,
       ent.name,
       ent.type,
       run.id as run_id,
       entity_runs.created_at,
       entity_runs.entity_xid
from cus_ana_hist_entity_runs entity_runs
         join xfer_entities_v2 as ent on ent.entity_xid = entity_runs.entity_xid
         left join xfer_runs_v2 as run on run.id = entity_runs.run_id;



drop view if exists view_quota_for_customer cascade;
create view view_quota_for_customer with (security_invoker=on) as
select quota.*,
       profiles.id as profile_id,
       org.id      as org_id
from acc_quota quota
         left join acc_organisations org on (quota.scope = 'org' and quota.organisation = org.id)
         left join profiles on (quota.scope = 'individual' and quota.customer = profiles.id) or
                               (org.id is null or org.id = profiles.organisation);


drop view if exists view_quota_used;
create view view_quota_used as
select item_type as type, used.count as used, quantity as quota, period, scope, q.profile_id
from view_quota_for_customer q
         left join (select count(*) count, quota_id
                    from cus_ana_hist_gw_single_doc_runs doc_runs
                    group by quota_id) used on q.id = used.quota_id
where q.item_type = 'document-analysis'
union
select item_type as type, used.count as used, quantity as quota, period, scope, q.profile_id
from view_quota_for_customer q
         left join (select count(*) count, quota_id from cus_ana_companies co group by quota_id) used
                   on q.id = used.quota_id
where item_type = 'entity'
union
select item_type as type, used.count as used, quantity as quota, period, scope, q.profile_id
from view_quota_for_customer q
         left join (select count(*) count, quota_id from cus_ana_hist_entity_runs ero group by quota_id) used
                   on q.id = used.quota_id
where item_type = 'entity-analysis';

drop view if exists view_api_queue_expanded;
create view view_api_queue_expanded as select id,
                                              request_data,
                                              response_data,
                                              status,
                                              created_at,
                                              updated_at,
                                              requester,
                                              request_action,
                                              message,
                                              input_cost,
                                              output_cost,
                                              entity_xid,
                                              name,
                                              description,
                                              type,
                                              searchable_text,
                                              url,
                                              lei,
                                              ticker,
                                              synonyms
                                       from api_queue join xfer_entities on xfer_entities.entity_xid = request_data->>'entity';
