
DROP VIEW view_xfer_runs;

CREATE VIEW view_xfer_runs AS
 SELECT max(ana_runs.id) AS id
   FROM ana_runs
  WHERE ((ana_runs.status = 'completed'::run_status) AND (ana_runs.run_type = 'full'::run_type))
UNION
 SELECT ana_runs.id
   FROM ana_runs
  WHERE ((ana_runs.status = 'completed'::run_status) AND (ana_runs.run_type = 'inc'::run_type) AND (ana_runs.id > ( SELECT max(ana_runs_1.id) AS max
           FROM ana_runs ana_runs_1
          WHERE ((ana_runs_1.status = 'completed'::run_status) AND (ana_runs_1.run_type = 'full'::run_type)))))
UNION
 SELECT ana_runs.id
   FROM ana_runs
  WHERE (((ana_runs.status = 'completed'::run_status) AND (ana_runs.run_type = 'hist'::run_type)) OR (ana_runs.run_type = 'hist-inc'::run_type));
