create or replace function audit_trigger_func() returns trigger
    language plpgsql
as
$$
DECLARE
    status_column TEXT := 'status';
    record_id_column TEXT := 'id';
    eko_id_column TEXT := 'eko_id';
    issue_column TEXT := 'issue';
    has_status_column BOOLEAN;
    has_eko_id_column BOOLEAN;
    has_issue_column BOOLEAN;
    rec_id INTEGER;
    eko_id_val eko_id;
    old_status_val TEXT;
    new_status_val TEXT;
    issue_val TEXT;
    current_user_name TEXT;
    current_run_id INTEGER;
BEGIN
    -- Check if the table has status, eko_id, and issue columns
    has_status_column := column_exists(TG_TABLE_NAME, status_column);
    has_eko_id_column := column_exists(TG_TABLE_NAME, eko_id_column);
    has_issue_column := column_exists(TG_TABLE_NAME, issue_column);

    -- Determine the record ID
    EXECUTE format('SELECT ($1).%I', record_id_column)
        INTO rec_id
        USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;

    -- Get eko_id if the column exists
    IF has_eko_id_column THEN
        EXECUTE format('SELECT ($1).%I', eko_id_column)
            INTO eko_id_val
            USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
    END IF;

    -- Get status values if the column exists
    IF has_status_column THEN
        IF TG_OP IN ('UPDATE', 'DELETE') THEN
            EXECUTE format('SELECT ($1).%I', status_column)
                INTO old_status_val
                USING OLD;
        END IF;
        IF TG_OP IN ('INSERT', 'UPDATE') THEN
            EXECUTE format('SELECT ($1).%I', status_column)
                INTO new_status_val
                USING NEW;
        END IF;
    END IF;

    -- Get the current issue value if the column exists
    IF has_issue_column THEN
        EXECUTE format('SELECT ($1).%I', issue_column)
            INTO issue_val
            USING CASE WHEN TG_OP = 'DELETE' THEN OLD ELSE NEW END;
    END IF;

    -- Get the current user
    SELECT session_user INTO current_user_name;

    -- Get the latest run_id
    SELECT get_latest_run_id() INTO current_run_id;

    -- Handle different operations
    IF TG_OP = 'INSERT' THEN
        INSERT INTO audit_log (
            table_name, operation, record_id, eko_id, new_data, new_status, issue, run_id, changed_by
        ) VALUES (
                     TG_TABLE_NAME, 'insert', rec_id, eko_id_val, row_to_json(NEW),
                     new_status_val, issue_val, current_run_id, current_user_name
                 );
    ELSIF TG_OP = 'UPDATE' THEN
        INSERT INTO audit_log (
            table_name, operation, record_id, eko_id, old_data, new_data,
            old_status, new_status, issue, run_id, changed_by
        ) VALUES (
                     TG_TABLE_NAME, CASE
                                        WHEN new_status_val != old_status_val and new_status_val ='authorized' THEN 'authorize'
                                        WHEN new_status_val != old_status_val and new_status_val ='deleted' THEN 'logical_delete'
                                        WHEN new_status_val != old_status_val and new_status_val ='revoked' THEN 'revoke'
                                        WHEN new_status_val != old_status_val and new_status_val ='pending' THEN 'pending'
                                        ELSE 'update'
                END::audit_operation, rec_id, eko_id_val, row_to_json(OLD), row_to_json(NEW),
                     old_status_val, new_status_val, issue_val, current_run_id, current_user_name
                 );
    ELSIF TG_OP = 'DELETE' THEN
        INSERT INTO audit_log (
            table_name, operation, record_id, eko_id, old_data, old_status, issue, run_id, changed_by
        ) VALUES (
                     TG_TABLE_NAME, 'delete', rec_id, eko_id_val, row_to_json(OLD),
                     old_status_val, issue_val, current_run_id, current_user_name
                 );
    END IF;
    RETURN NULL;
END;
$$;
