DROP VIEW public.view_sec_for_llm;

CREATE VIEW
    public.view_sec_for_llm WITH (security_barrier, security_invoker) AS
SELECT
    kg_securities.eko_id as eko_id,
    kg_companies.name AS name,
    concat(
            'This is a company, the following is a description of them: ',
            kg_companies.description
    ) as description,
    kg_companies.ticker AS identifier,
    kg_companies.url AS url
FROM
    kg_securities
        JOIN kg_companies ON kg_securities.entity = kg_companies.eko_id
UNION
SELECT
    kg_securities.eko_id as eko_id,
    kg_securities.name AS name,
    concat(
            'This is a bond, please assume the bond name includes the company name, the bond name is ',
            kg_securities.name
    ) as description,
    CASE
        WHEN kg_securities.isin IS NOT NULL THEN kg_securities.isin
        ELSE kg_securities.ticker
        END AS identifier,
    kg_securities.url AS url
FROM
    kg_securities
WHERE kg_securities.entity IS NULL;
