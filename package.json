{"name": "mono-repo", "private": true, "scripts": {"build": "turbo run build", "dev": "turbo run dev", "lint": "turbo run lint", "format": "prettier --write \"**/*.{ts,tsx,md}\""}, "devDependencies": {"prettier": "^3.3.3", "turbo": "latest", "typescript": "^5.7.2"}, "packageManager": "pnpm@9.0.0", "engines": {"node": ">=18"}, "dependencies": {"@next/eslint-plugin-next": "latest", "@radix-ui/react-switch": "^1.2.2", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "jszip": "^3.10.1", "pptxgenjs": "^4.0.0"}, "overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}, "pnpm": {"overrides": {"@types/react": "npm:types-react@19.0.0-rc.1", "@types/react-dom": "npm:types-react-dom@19.0.0-rc.1"}}}