#!/bin/bash
cd "$(dirname $0)/.." || exit 1
cat .env
. .env
echo $POSTGRES_DB

# Database connection details
DB_NAME=${POSTGRES_DB}
DB_USER=${POSTGRES_USER}
DB_HOST=${POSTGRES_HOST}
DB_PORT=${POSTGRES_PORT}
export PGPASSWORD="${POSTGRES_PASSWORD}"
PGDUMP_FLAGS="--schema-only --clean --no-owner --no-privileges --no-tablespaces --no-publications"
# Output directory
OUTPUT_DIR="./schema/backoffice"
mkdir -p $OUTPUT_DIR

cleanup() {
  echo
#  echo "$2: $(< $1)" | llm > $output_file.cleaned.sql
}

# Function to create directories for a schema
create_schema_directories() {
    local schema_name=$1
    mkdir -p "$OUTPUT_DIR/schemas/$schema_name/tables"
    mkdir -p "$OUTPUT_DIR/schemas/$schema_name/views"
    mkdir -p "$OUTPUT_DIR/schemas/$schema_name/indexes"
    mkdir -p "$OUTPUT_DIR/schemas/$schema_name/functions"
    mkdir -p "$OUTPUT_DIR/schemas/$schema_name/enums"
}

# Function to export the DDL of a table
export_table_ddl() {
    local schema_name=$1
    local table_name=$2
    local output_file="$OUTPUT_DIR/schemas/$schema_name/tables/${table_name}.sql"

    pg_dump ${PGDUMP_FLAGS} --table="${schema_name}.${table_name}" --dbname=$DB_NAME --username=$DB_USER --host=$DB_HOST --port=$DB_PORT > "$output_file"

    echo "Cleaning and commenting DDL for table: $table_name"
    cleanup "$output_file" "Please clean up this SQL script and add appropriate comments explaining the purpose of each part of the DDL."
}

# Function to export the DDL of a view
export_view_ddl() {
    local schema_name=$1
    local view_name=$2
    local output_file="$OUTPUT_DIR/schemas/$schema_name/views/${view_name}.sql"

    pg_dump ${PGDUMP_FLAGS} --table="${schema_name}.${view_name}" --dbname=$DB_NAME --username=$DB_USER --host=$DB_HOST --port=$DB_PORT > "$output_file"

    echo "Cleaning and commenting DDL for view: $view_name"
    cleanup "$output_file" "Please clean up this SQL script and add appropriate comments explaining the purpose of each part of the DDL."
}

# Function to export the DDL of an enum
export_enum_ddl() {
    local schema_name=$1
    local enum_name=$2
    local output_file="$OUTPUT_DIR/schemas/$schema_name/enums/${enum_name}.sql"

    enum_ddl=$(psql -d $DB_NAME -U $DB_USER -h $DB_HOST -p $DB_PORT -t -c "SELECT 'CREATE TYPE ' || n.nspname || '.' || t.typname || ' AS ENUM (' || string_agg(quote_literal(e.enumlabel), ', ') || ');' AS ddl FROM pg_type t JOIN pg_enum e ON t.oid = e.enumtypid JOIN pg_namespace n ON n.oid = t.typnamespace WHERE t.typname = '${enum_name}' AND n.nspname = '${schema_name}' GROUP BY t.typname, n.nspname;")

    echo "$enum_ddl" > "$output_file"

    echo "Cleaning and commenting DDL for enum: $enum_name"
    cleanup "$output_file" "Please clean up this SQL script and add appropriate comments explaining the purpose of each part of the enum definition."
}

# Function to export the DDL of a function
export_function_ddl() {
    local schema_name=$1
    local function_name=$2
    local output_file="$OUTPUT_DIR/schemas/$schema_name/functions/${function_name}.sql"

    pg_dump ${PGDUMP_FLAGS} --schema="${schema_name}" --function="${schema_name}.${function_name}" --dbname=$DB_NAME --username=$DB_USER --host=$DB_HOST --port=$DB_PORT > "$output_file"

    echo "Cleaning and commenting DDL for function: $function_name"
    cleanup "$output_file" "Please clean up this SQL script and add appropriate comments explaining the purpose and functionality of this function."
}

# Export table DDL
export_tables() {
    local schema_name=$1
    tables=$(psql -d $DB_NAME -U $DB_USER -h $DB_HOST -p $DB_PORT -t -c "SELECT tablename FROM pg_tables WHERE schemaname = '${schema_name}';")
    for table in $tables; do
        echo "Exporting DDL for table: $table"
        export_table_ddl $schema_name $table
    done
}

# Export view DDL
export_views() {
    local schema_name=$1
    views=$(psql -d $DB_NAME -U $DB_USER -h $DB_HOST -p $DB_PORT -t -c "SELECT viewname FROM pg_views WHERE schemaname = '${schema_name}';")
    for view in $views; do
        echo "Exporting DDL for view: $view"
        export_view_ddl $schema_name $view
    done
}

# Export enum DDL
export_enums() {
    local schema_name=$1
    enums=$(psql -d $DB_NAME -U $DB_USER -h $DB_HOST -p $DB_PORT -t -c "SELECT t.typname FROM pg_type t JOIN pg_namespace n ON t.typnamespace = n.oid WHERE n.nspname = '${schema_name}' AND t.typtype = 'e';")
    for enum in $enums; do
        echo "Exporting DDL for enum: $enum"
        export_enum_ddl $schema_name $enum
    done
}

# Export function DDL
export_functions() {
    local schema_name=$1
    functions=$(psql -d $DB_NAME -U $DB_USER -h $DB_HOST -p $DB_PORT -t -c "SELECT p.proname FROM pg_proc p JOIN pg_namespace n ON p.pronamespace = n.oid WHERE n.nspname = '${schema_name}' AND p.prokind = 'f';")
    for function in $functions; do
        echo "Exporting DDL for function: $function"
        export_function_ddl $schema_name $function
    done
}

# Main execution
for schema in 'public'; do
    echo "Processing schema: $schema"
    create_schema_directories $schema
    export_tables $schema
    export_views $schema
    export_enums $schema
    export_functions $schema
done

echo "DDL export completed!"
