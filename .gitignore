# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# Dependencies
node_modules
.pnp
.pnp.js

# Local env files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Testing
coverage

# Turbo
.turbo

# Vercel
.vercel

# Build Outputs
.next/
out/
build
dist


# Debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Misc
.DS_Store
*.pem
.aider*

/stacks/
/backoffice/services/eko_ethics/var/
/backoffice/services/eko_ethics/models/var/
/backoffice/services/eko_ethics/embedding/embedding_training.json
/backoffice/services/eko_ethics/embedding/modernbert_embedding_model.pt
/backoffice/notebooks/embedding_training.json
/backoffice/notebooks/cleaned_embedding_training.json
/backoffice/services/tests/ethics_model/fixtures/distilbert_embedding_model.pt
/backoffice/services/tests/ethics_model/fixtures/modernbert_embedding_model.pt
/backoffice/src/tests/var/
/schema/

.idea/AugmentWebviewStateStore.xml

venv
/.mcp.json
/.claude-docker-home/
/.claude/.credentials.json
/.claude/settings.local.json
